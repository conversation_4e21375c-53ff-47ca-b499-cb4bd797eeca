/**
 * Aayush Agent - Hierarchical Agent System
 *
 * An advanced supervisor-managed multi-agent system for content research and generation.
 * Features shared memory, structured data with delimiters, and coordinated execution.
 * Named "Aayush" - the intelligent content creation assistant.
 */

import { GeminiService } from '../gemini'
import { OpenRouterService } from '../openrouter'
import { GoogleSearchService } from '../search'

// ============================================================================
// SHARED MEMORY SYSTEM
// ============================================================================

export interface SharedMemory {
  topic: string
  topicAnalysis: string
  primaryResearch: string
  gapAnalysis: string
  deepResearch: string
  contentGeneration: string
  metadata: {
    startTime: Date
    currentPhase: string
    completedPhases: string[]
    errors: string[]
    agentLogs: Array<{
      agent: string
      timestamp: Date
      action: string
      result: string
    }>
  }
}

export class MemoryManager {
  private memory: SharedMemory

  constructor(topic: string) {
    this.memory = {
      topic,
      topicAnalysis: '',
      primaryResearch: '',
      gapAnalysis: '',
      deepResearch: '',
      contentGeneration: '',
      metadata: {
        startTime: new Date(),
        currentPhase: 'initialization',
        completedPhases: [],
        errors: [],
        agentLogs: []
      }
    }
  }

  // Store data with structured delimiters
  store(section: keyof SharedMemory, data: string): void {
    if (section === 'metadata') return // Metadata handled separately
    
    this.memory[section] = data
    this.logAction('MemoryManager', 'store', `Stored data in ${section}`)
  }

  // Retrieve data from memory
  retrieve(section: keyof SharedMemory): string {
    if (section === 'metadata') return JSON.stringify(this.memory.metadata)
    return this.memory[section] || ''
  }

  // Get all research data for RAG
  getAllResearchData(): string {
    const sections = [
      this.memory.topicAnalysis,
      this.memory.primaryResearch,
      this.memory.gapAnalysis,
      this.memory.deepResearch
    ].filter(section => section.length > 0)

    return sections.join('\n\n===MEMORY_SEPARATOR===\n\n')
  }

  // Update current phase
  updatePhase(phase: string): void {
    if (this.memory.metadata.currentPhase !== 'initialization') {
      this.memory.metadata.completedPhases.push(this.memory.metadata.currentPhase)
    }
    this.memory.metadata.currentPhase = phase
    this.logAction('MemoryManager', 'updatePhase', `Phase updated to: ${phase}`)
  }

  // Log agent actions
  logAction(agent: string, action: string, result: string): void {
    this.memory.metadata.agentLogs.push({
      agent,
      timestamp: new Date(),
      action,
      result: result.substring(0, 200) // Truncate for memory efficiency
    })
  }

  // Log errors
  logError(error: string): void {
    this.memory.metadata.errors.push(`${new Date().toISOString()}: ${error}`)
  }

  // Get memory summary
  getSummary(): string {
    const { metadata } = this.memory
    return `
===MEMORY_SUMMARY===
Topic: ${this.memory.topic}
Current Phase: ${metadata.currentPhase}
Completed Phases: ${metadata.completedPhases.join(', ')}
Total Logs: ${metadata.agentLogs.length}
Errors: ${metadata.errors.length}
Runtime: ${Date.now() - metadata.startTime.getTime()}ms
===END_SUMMARY===
    `.trim()
  }
}

// ============================================================================
// PROGRESS TRACKING
// ============================================================================

export interface AgentProgress {
  agent: string
  phase: string
  step: string
  progress: number
  message: string
  timestamp: Date
}

export type ProgressCallback = (progress: AgentProgress) => void

// ============================================================================
// BASE AGENT CLASS
// ============================================================================

export abstract class BaseAgent {
  protected name: string
  protected memory: MemoryManager
  protected onProgress?: ProgressCallback

  constructor(name: string, memory: MemoryManager, onProgress?: ProgressCallback) {
    this.name = name
    this.memory = memory
    this.onProgress = onProgress
  }

  protected updateProgress(phase: string, step: string, progress: number, message: string): void {
    if (this.onProgress) {
      this.onProgress({
        agent: this.name,
        phase,
        step,
        progress,
        message,
        timestamp: new Date()
      })
    }
  }

  protected logAction(action: string, result: string): void {
    this.memory.logAction(this.name, action, result)
  }

  protected logError(error: string): void {
    this.memory.logError(`${this.name}: ${error}`)
  }

  // Parse structured data with delimiters
  protected parseStructuredData(content: string, delimiter: string): Record<string, string> {
    const result: Record<string, string> = {}

    try {
      // Split by the main delimiter
      const sections = content.split(`===${delimiter}===`)

      if (sections.length < 2) {
        console.log(`⚠️ No ${delimiter} sections found in content`)
        return result
      }

      // Process each section
      for (let i = 1; i < sections.length; i += 2) {
        if (i + 1 < sections.length) {
          const key = sections[i].trim()
          let value = sections[i + 1]

          // Find the next delimiter to properly extract the value
          const nextDelimiterIndex = value.indexOf('===')
          if (nextDelimiterIndex !== -1) {
            value = value.substring(0, nextDelimiterIndex)
          }

          result[key] = value.trim()
        }
      }

      console.log(`🔍 Parsed ${Object.keys(result).length} keys from ${delimiter}:`, Object.keys(result))

    } catch (error) {
      console.error(`❌ Error parsing structured data for ${delimiter}:`, error)
    }

    return result
  }

  // Create structured data with delimiters
  protected createStructuredData(data: Record<string, any>, prefix: string = 'DATA'): string {
    let result = `===${prefix}_START===\n`
    
    for (const [key, value] of Object.entries(data)) {
      result += `===${key.toUpperCase()}===\n${value}\n`
    }
    
    result += `===${prefix}_END===`
    return result
  }

  abstract execute(): Promise<string>
}

// ============================================================================
// TOPIC ANALYST AGENT
// ============================================================================

export class TopicAnalystAgent extends BaseAgent {
  private qwen: OpenRouterService
  private searchService: GoogleSearchService

  constructor(memory: MemoryManager, onProgress?: ProgressCallback) {
    super('TopicAnalyst', memory, onProgress)
    this.qwen = new OpenRouterService()
    this.searchService = new GoogleSearchService()
  }

  async execute(): Promise<string> {
    this.updateProgress('topic-analysis', 'keyword-extraction', 10, 'Extracting single keyword from topic...')
    
    const topic = this.memory.retrieve('topic')
    
    // Step 1: Extract single keyword using Qwen
    const keywordPrompt = `
Extract the most important single keyword from this topic for Google search: "${topic}"

Rules:
- Return only ONE keyword that best represents the core concept
- Choose the most specific and searchable term
- Do not include multiple words unless it's a compound term
- Focus on the main subject or concept

Return using structured format:

===KEYWORD===
[single keyword here]

===REASONING===
[brief explanation of why this keyword was chosen]
`

    try {
      const keywordResult = await this.qwen.generateThinkingContent(keywordPrompt, {
        temperature: 0.1,
        maxTokens: 200
      })

      const keywordData = this.parseStructuredData(keywordResult, 'KEYWORD')
      const keyword = keywordData.KEYWORD || topic.split(' ')[0]

      this.logAction('keyword-extraction', `Extracted keyword: ${keyword}`)
      this.updateProgress('topic-analysis', 'search-execution', 30, `Searching for top 5 pages with keyword: ${keyword}`)

      // Step 2: Search and extract top 5 pages
      const searchResults = await this.searchService.searchAndExtract(keyword, 5)
      
      if (!searchResults.extractedContent.length) {
        throw new Error(`No search results found for keyword: ${keyword}`)
      }

      this.updateProgress('topic-analysis', 'content-analysis', 60, 'Analyzing extracted content with Qwen...')

      // Step 3: Analyze content with Qwen
      const contentSummary = searchResults.extractedContent
        .map((item, index) => `===PAGE_${index + 1}===\nURL: ${item.url}\nContent: ${item.content.substring(0, 800)}...`)
        .join('\n\n')

      const analysisPrompt = `
Analyze the following search results for the topic "${topic}" using keyword "${keyword}":

${contentSummary}

Provide comprehensive analysis using structured format:

===MAIN_TOPIC===
[refined main topic based on search results]

===SUBTOPICS===
[subtopic1]
[subtopic2]
[subtopic3]
[subtopic4]
[subtopic5]

===KEY_INSIGHTS===
[insight1]
[insight2]
[insight3]

===CONTENT_PATTERNS===
[pattern1]
[pattern2]
[pattern3]

===RESEARCH_QUERIES===
[query1 for primary research]
[query2 for primary research]
[query3 for primary research]
[query4 for primary research]

===ANALYSIS_SUMMARY===
[comprehensive summary of findings and recommendations for next research phase]
`

      const analysisResult = await this.qwen.generateAnalysisContent(analysisPrompt, contentSummary, {
        temperature: 0.3,
        maxTokens: 2000
      })

      this.updateProgress('topic-analysis', 'completion', 100, 'Topic analysis completed')
      this.logAction('analysis-completion', 'Successfully analyzed topic and generated research queries')

      // Store in memory
      const finalResult = this.createStructuredData({
        keyword,
        search_results_count: searchResults.extractedContent.length,
        analysis: analysisResult
      }, 'TOPIC_ANALYSIS')

      this.memory.store('topicAnalysis', finalResult)
      return finalResult

    } catch (error) {
      const errorMsg = `Topic analysis failed: ${error}`
      this.logError(errorMsg)
      throw new Error(errorMsg)
    }
  }
}

// ============================================================================
// PRIMARY RESEARCH AGENT
// ============================================================================

export class PrimaryResearchAgent extends BaseAgent {
  private searchService: GoogleSearchService

  constructor(memory: MemoryManager, onProgress?: ProgressCallback) {
    super('PrimaryResearch', memory, onProgress)
    this.searchService = new GoogleSearchService()
  }

  async execute(): Promise<string> {
    this.updateProgress('primary-research', 'query-extraction', 10, 'Extracting research queries from topic analysis...')

    const topicAnalysis = this.memory.retrieve('topicAnalysis')
    if (!topicAnalysis) {
      throw new Error('Topic analysis not found in memory')
    }

    // Extract research queries from topic analysis
    this.logAction('debug-parsing', `Topic analysis data length: ${topicAnalysis.length}`)

    const analysisData = this.parseStructuredData(topicAnalysis, 'TOPIC_ANALYSIS')
    this.logAction('debug-parsing', `Parsed keys: ${Object.keys(analysisData).join(', ')}`)

    // Try to extract the analysis section which contains the research queries
    let queriesSection = ''
    if (analysisData.ANALYSIS) {
      // Parse the nested analysis content
      const nestedAnalysis = this.parseStructuredData(analysisData.ANALYSIS, 'RESEARCH_QUERIES')
      queriesSection = nestedAnalysis.RESEARCH_QUERIES || ''
      this.logAction('debug-parsing', `Found nested queries: ${queriesSection.length} chars`)
    }

    // If no queries found, try direct parsing
    if (!queriesSection) {
      const directParse = this.parseStructuredData(topicAnalysis, 'RESEARCH_QUERIES')
      queriesSection = directParse.RESEARCH_QUERIES || ''
      this.logAction('debug-parsing', `Found direct queries: ${queriesSection.length} chars`)
    }

    const queries = queriesSection.split('\n').filter(q => q.trim().length > 0 && !q.includes('===') && !q.includes('[query')).slice(0, 4)
    this.logAction('debug-parsing', `Final queries count: ${queries.length}`)
    queries.forEach((q, i) => this.logAction('debug-query', `Query ${i + 1}: ${q}`))

    if (queries.length === 0) {
      // Fallback: Generate basic research queries from the topic
      const topic = this.memory.retrieve('topic')
      const fallbackQueries = [
        `${topic} overview`,
        `${topic} benefits`,
        `${topic} challenges`,
        `${topic} future trends`
      ]

      this.logAction('fallback-queries', `No queries found in analysis, using fallback queries for topic: ${topic}`)

      // Use fallback queries
      const allResearchData: Array<{
        query: string
        results: Array<{ url: string; content: string }>
      }> = []

      // Execute fallback queries
      for (let i = 0; i < fallbackQueries.length; i++) {
        const query = fallbackQueries[i].trim()
        this.updateProgress('primary-research', 'search-execution', 30 + (i * 15), `Searching: ${query}`)

        try {
          const searchResults = await this.searchService.searchAndExtract(query, 5)

          allResearchData.push({
            query,
            results: searchResults.extractedContent
          })

          this.logAction('search-execution', `Query "${query}" returned ${searchResults.extractedContent.length} results`)

          // Brief pause to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000))
        } catch (error) {
          this.logError(`Search failed for query "${query}": ${error}`)
          continue
        }
      }

      this.updateProgress('primary-research', 'data-compilation', 90, 'Compiling research data...')

      // Compile all research data with structured format
      let compiledData = '===PRIMARY_RESEARCH_START===\n'

      allResearchData.forEach((queryData, queryIndex) => {
        compiledData += `===QUERY_${queryIndex + 1}===\n${queryData.query}\n`

        queryData.results.forEach((result, resultIndex) => {
          compiledData += `===RESULT_${queryIndex + 1}_${resultIndex + 1}===\n`
          compiledData += `URL: ${result.url}\n`
          compiledData += `CONTENT: ${result.content.substring(0, 1500)}...\n`
        })
      })

      compiledData += '===PRIMARY_RESEARCH_END==='

      this.updateProgress('primary-research', 'completion', 100, 'Primary research completed with fallback queries')
      this.logAction('research-completion', `Compiled research from ${allResearchData.length} fallback queries`)

      // Store in memory
      this.memory.store('primaryResearch', compiledData)
      return compiledData
    }

    this.logAction('query-extraction', `Extracted ${queries.length} research queries`)
    this.updateProgress('primary-research', 'multi-search', 30, `Executing ${queries.length} research queries...`)

    const allResearchData: Array<{
      query: string
      results: Array<{ url: string; content: string }>
    }> = []

    // Execute each query and get top 5 pages
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i].trim()
      this.updateProgress('primary-research', 'search-execution', 30 + (i * 15), `Searching: ${query}`)

      try {
        const searchResults = await this.searchService.searchAndExtract(query, 5)

        allResearchData.push({
          query,
          results: searchResults.extractedContent
        })

        this.logAction('search-execution', `Query "${query}" returned ${searchResults.extractedContent.length} results`)

        // Brief pause to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        this.logError(`Search failed for query "${query}": ${error}`)
        continue
      }
    }

    this.updateProgress('primary-research', 'data-compilation', 90, 'Compiling research data...')

    // Compile all research data with structured format
    let compiledData = '===PRIMARY_RESEARCH_START===\n'

    allResearchData.forEach((queryData, queryIndex) => {
      compiledData += `===QUERY_${queryIndex + 1}===\n${queryData.query}\n`

      queryData.results.forEach((result, resultIndex) => {
        compiledData += `===RESULT_${queryIndex + 1}_${resultIndex + 1}===\n`
        compiledData += `URL: ${result.url}\n`
        compiledData += `CONTENT: ${result.content.substring(0, 1500)}...\n`
      })
    })

    compiledData += '===PRIMARY_RESEARCH_END==='

    this.updateProgress('primary-research', 'completion', 100, 'Primary research completed')
    this.logAction('research-completion', `Compiled research from ${allResearchData.length} queries`)

    // Store in memory
    this.memory.store('primaryResearch', compiledData)
    return compiledData
  }
}

// ============================================================================
// GAP ANALYST AGENT
// ============================================================================

export class GapAnalystAgent extends BaseAgent {
  private qwen: OpenRouterService

  constructor(memory: MemoryManager, onProgress?: ProgressCallback) {
    super('GapAnalyst', memory, onProgress)
    this.qwen = new OpenRouterService()
  }

  async execute(): Promise<string> {
    this.updateProgress('gap-analysis', 'data-retrieval', 10, 'Retrieving research data from memory...')

    const topicAnalysis = this.memory.retrieve('topicAnalysis')
    const primaryResearch = this.memory.retrieve('primaryResearch')

    if (!topicAnalysis || !primaryResearch) {
      throw new Error('Required data not found in memory for gap analysis')
    }

    this.updateProgress('gap-analysis', 'content-analysis', 30, 'Analyzing content gaps with Qwen...')

    const gapAnalysisPrompt = `
Analyze the research data to identify content gaps and areas that need deeper research.

TOPIC ANALYSIS:
${topicAnalysis}

PRIMARY RESEARCH DATA:
${primaryResearch}

Identify gaps, missing information, and areas that need deeper research using structured format:

===CONTENT_GAPS===
[gap1: description of missing content area]
[gap2: description of missing content area]
[gap3: description of missing content area]

===MISSING_PERSPECTIVES===
[perspective1: what viewpoint is missing]
[perspective2: what viewpoint is missing]
[perspective3: what viewpoint is missing]

===DEPTH_NEEDED===
[area1: topic that needs deeper coverage]
[area2: topic that needs deeper coverage]
[area3: topic that needs deeper coverage]

===DEEP_RESEARCH_QUERIES===
[query1 for deep research to fill gaps]
[query2 for deep research to fill gaps]
[query3 for deep research to fill gaps]

===GAP_ANALYSIS_SUMMARY===
[comprehensive summary of gaps found and recommendations for deep research phase]

===COVERAGE_ASSESSMENT===
[assessment of how well the current research covers the topic - percentage and explanation]
`

    try {
      const gapAnalysisResult = await this.qwen.generateAnalysisContent(gapAnalysisPrompt, '', {
        temperature: 0.2,
        maxTokens: 2000
      })

      this.updateProgress('gap-analysis', 'completion', 100, 'Gap analysis completed')
      this.logAction('gap-analysis-completion', 'Successfully identified content gaps and deep research needs')

      // Store in memory
      const finalResult = this.createStructuredData({
        analysis: gapAnalysisResult,
        timestamp: new Date().toISOString()
      }, 'GAP_ANALYSIS')

      this.memory.store('gapAnalysis', finalResult)
      return finalResult

    } catch (error) {
      const errorMsg = `Gap analysis failed: ${error}`
      this.logError(errorMsg)
      throw new Error(errorMsg)
    }
  }
}

// ============================================================================
// DEEP RESEARCH AGENT
// ============================================================================

export class DeepResearchAgent extends BaseAgent {
  private searchService: GoogleSearchService

  constructor(memory: MemoryManager, onProgress?: ProgressCallback) {
    super('DeepResearch', memory, onProgress)
    this.searchService = new GoogleSearchService()
  }

  async execute(): Promise<string> {
    this.updateProgress('deep-research', 'gap-analysis-review', 10, 'Reviewing gap analysis for deep research needs...')

    const gapAnalysis = this.memory.retrieve('gapAnalysis')
    if (!gapAnalysis) {
      throw new Error('Gap analysis not found in memory')
    }

    // Extract deep research queries from gap analysis
    const gapData = this.parseStructuredData(gapAnalysis, 'GAP_ANALYSIS')

    // Try to extract the analysis section which contains the deep research queries
    let queriesSection = ''
    if (gapData.ANALYSIS) {
      // Parse the nested analysis content
      const nestedAnalysis = this.parseStructuredData(gapData.ANALYSIS, 'DEEP_RESEARCH_QUERIES')
      queriesSection = nestedAnalysis.DEEP_RESEARCH_QUERIES || ''
    }

    // If no queries found, try direct parsing
    if (!queriesSection) {
      const directParse = this.parseStructuredData(gapAnalysis, 'DEEP_RESEARCH_QUERIES')
      queriesSection = directParse.DEEP_RESEARCH_QUERIES || ''
    }

    const deepQueries = queriesSection.split('\n').filter(q => q.trim().length > 0 && !q.includes('===') && !q.includes('[query')).slice(0, 3)

    if (deepQueries.length === 0) {
      this.logAction('deep-research-skip', 'No deep research queries found, skipping deep research')
      return this.createStructuredData({
        status: 'skipped',
        reason: 'No deep research needed based on gap analysis'
      }, 'DEEP_RESEARCH')
    }

    this.updateProgress('deep-research', 'targeted-search', 30, `Executing ${deepQueries.length} targeted deep research queries...`)

    const deepResearchData: Array<{
      query: string
      results: Array<{ url: string; content: string }>
    }> = []

    // Execute deep research queries
    for (let i = 0; i < deepQueries.length; i++) {
      const query = deepQueries[i].trim()
      this.updateProgress('deep-research', 'search-execution', 30 + (i * 20), `Deep search: ${query}`)

      try {
        const searchResults = await this.searchService.searchAndExtract(query, 3)

        deepResearchData.push({
          query,
          results: searchResults.extractedContent
        })

        this.logAction('deep-search-execution', `Deep query "${query}" returned ${searchResults.extractedContent.length} results`)

        // Brief pause to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1200))
      } catch (error) {
        this.logError(`Deep search failed for query "${query}": ${error}`)
        continue
      }
    }

    this.updateProgress('deep-research', 'data-compilation', 90, 'Compiling deep research data...')

    // Compile deep research data with structured format
    let compiledData = '===DEEP_RESEARCH_START===\n'

    deepResearchData.forEach((queryData, queryIndex) => {
      compiledData += `===DEEP_QUERY_${queryIndex + 1}===\n${queryData.query}\n`

      queryData.results.forEach((result, resultIndex) => {
        compiledData += `===DEEP_RESULT_${queryIndex + 1}_${resultIndex + 1}===\n`
        compiledData += `URL: ${result.url}\n`
        compiledData += `CONTENT: ${result.content.substring(0, 1200)}...\n`
      })
    })

    compiledData += '===DEEP_RESEARCH_END==='

    this.updateProgress('deep-research', 'completion', 100, 'Deep research completed')
    this.logAction('deep-research-completion', `Compiled deep research from ${deepResearchData.length} targeted queries`)

    // Store in memory
    this.memory.store('deepResearch', compiledData)
    return compiledData
  }
}

// ============================================================================
// CONTENT WRITING AGENT
// ============================================================================

export class ContentWritingAgent extends BaseAgent {
  private gemini: GeminiService
  private searchService: GoogleSearchService

  constructor(memory: MemoryManager, onProgress?: ProgressCallback) {
    super('ContentWriting', memory, onProgress)
    this.gemini = new GeminiService()
    this.searchService = new GoogleSearchService()
  }

  async execute(): Promise<string> {
    this.updateProgress('content-writing', 'keyword-search', 10, 'Performing keyword search for writing style analysis...')

    const topic = this.memory.retrieve('topic')

    // Step 1: Extract keyword and search for top 5 pages for style analysis
    const keyword = await this.extractKeywordFromTopic(topic)
    const styleAnalysisResults = await this.searchService.searchAndExtract(keyword, 5)

    this.updateProgress('content-writing', 'style-analysis', 30, 'Analyzing writing styles from top 5 pages...')

    // Step 2: Analyze writing styles
    const writingStyleAnalysis = await this.analyzeWritingStyles(styleAnalysisResults.extractedContent)

    this.updateProgress('content-writing', 'rag-preparation', 50, 'Preparing RAG knowledge base from memory...')

    // Step 3: Prepare RAG knowledge base from all memory data
    const knowledgeBase = this.memory.getAllResearchData()

    this.updateProgress('content-writing', 'content-generation', 70, 'Generating content using RAG approach...')

    // Step 4: Generate content using RAG approach
    const generatedContent = await this.generateContentWithRAG(topic, knowledgeBase, writingStyleAnalysis)

    this.updateProgress('content-writing', 'completion', 100, 'Content generation completed')

    // Debug: Log the generated content
    console.log('📝 Generated content length:', generatedContent.length)
    console.log('📝 Generated content preview:', generatedContent.substring(0, 200))

    // Store in memory
    const finalResult = this.createStructuredData({
      keyword_used: keyword,
      style_analysis: writingStyleAnalysis,
      generated_content: generatedContent,
      knowledge_base_size: knowledgeBase.length
    }, 'CONTENT_GENERATION')

    console.log('📝 Final result length:', finalResult.length)
    console.log('📝 Final result preview:', finalResult.substring(0, 300))

    this.memory.store('contentGeneration', finalResult)
    return finalResult
  }

  private async extractKeywordFromTopic(topic: string): Promise<string> {
    try {
      const keyword = await this.gemini.extractKeywords(topic)
      this.logAction('keyword-extraction', `Extracted keyword: ${keyword}`)
      return keyword.trim()
    } catch (error) {
      this.logError(`Keyword extraction failed: ${error}`)
      return topic.split(' ')[0] // Fallback to first word
    }
  }

  private async analyzeWritingStyles(pages: Array<{ url: string; content: string }>): Promise<string> {
    const contentSummary = pages
      .map((page, index) => `===STYLE_PAGE_${index + 1}===\nURL: ${page.url}\nContent: ${page.content.substring(0, 1000)}...`)
      .join('\n\n')

    const styleAnalysisPrompt = `
Analyze the writing styles from these top 5 pages to understand how to write in a human, engaging way:

${contentSummary}

Provide analysis using structured format:

===WRITING_TONE===
[description of the overall tone used across these pages]

===SENTENCE_STRUCTURE===
[analysis of sentence patterns and structure]

===ENGAGEMENT_TECHNIQUES===
[techniques used to engage readers]

===CONTENT_ORGANIZATION===
[how content is typically organized and structured]

===STYLE_RECOMMENDATIONS===
[specific recommendations for mimicking this human writing style]
`

    try {
      return await this.gemini.generateContent(styleAnalysisPrompt, {
        temperature: 0.3,
        maxOutputTokens: 1500
      })
    } catch (error) {
      this.logError(`Style analysis failed: ${error}`)
      return 'Style analysis unavailable - using default professional writing approach'
    }
  }

  private async generateContentWithRAG(topic: string, knowledgeBase: string, styleAnalysis: string): Promise<string> {
    const ragPrompt = `
You are a world-class content writer using RAG (Retrieval-Augmented Generation) approach. Create a comprehensive, engaging article about "${topic}".

KNOWLEDGE BASE (from research):
${knowledgeBase.substring(0, 8000)} // Limit to prevent token overflow

WRITING STYLE ANALYSIS:
${styleAnalysis}

CONTENT REQUIREMENTS:
- Write in clean, valid markdown format
- Use # for main title, ## for major sections, ### for subsections
- Create engaging, human-like content that mimics the analyzed writing style
- Include all important details from the knowledge base
- Make this a comprehensive, go-to article for the topic
- Use the research data to support all claims and statements
- Include relevant examples, statistics, and insights from the knowledge base
- Write in a way that feels natural and human, not AI-generated
- Target 2000+ words for comprehensive coverage
- Include compelling introduction and strong conclusion

STRUCTURE:
1. Compelling title and introduction
2. Well-organized sections with clear headings
3. Use of bullet points and numbered lists where appropriate
4. Integration of research findings throughout
5. Practical insights and actionable advice
6. Strong conclusion with key takeaways

Create content that demonstrates deep expertise and provides exceptional value to readers. Use the knowledge base extensively to support your writing.

Return ONLY the markdown content, no additional text or formatting.
`

    try {
      return await this.gemini.generateContent(ragPrompt, {
        temperature: 0.7,
        maxOutputTokens: 8000
      })
    } catch (error) {
      this.logError(`Content generation failed: ${error}`)
      throw new Error(`Content generation failed: ${error}`)
    }
  }
}

// ============================================================================
// SUPERVISOR AGENT
// ============================================================================

export interface SupervisorOptions {
  maxRetries?: number
  timeoutMs?: number
  enableDetailedLogging?: boolean
}

export class SupervisorAgent {
  private memory: MemoryManager
  private agents: {
    topicAnalyst: TopicAnalystAgent
    primaryResearch: PrimaryResearchAgent
    gapAnalyst: GapAnalystAgent
    deepResearch: DeepResearchAgent
    contentWriting: ContentWritingAgent
  }
  private onProgress?: ProgressCallback
  private options: SupervisorOptions

  constructor(topic: string, onProgress?: ProgressCallback, options: SupervisorOptions = {}) {
    this.memory = new MemoryManager(topic)
    this.onProgress = onProgress
    this.options = {
      maxRetries: 3,
      timeoutMs: 300000, // 5 minutes
      enableDetailedLogging: true,
      ...options
    }

    // Initialize all agents with shared memory
    this.agents = {
      topicAnalyst: new TopicAnalystAgent(this.memory, onProgress),
      primaryResearch: new PrimaryResearchAgent(this.memory, onProgress),
      gapAnalyst: new GapAnalystAgent(this.memory, onProgress),
      deepResearch: new DeepResearchAgent(this.memory, onProgress),
      contentWriting: new ContentWritingAgent(this.memory, onProgress)
    }
  }

  async executeWorkflow(): Promise<{
    success: boolean
    content: string
    memory: SharedMemory
    executionTime: number
    errors: string[]
  }> {
    const startTime = Date.now()
    const errors: string[] = []

    try {
      this.updateProgress('supervisor', 'initialization', 0, 'Starting hierarchical agent workflow...')

      // Phase 1: Topic Analysis
      this.memory.updatePhase('topic-analysis')
      await this.executeWithRetry('topicAnalyst', 'Topic Analysis')

      // Phase 2: Primary Research
      this.memory.updatePhase('primary-research')
      await this.executeWithRetry('primaryResearch', 'Primary Research')

      // Phase 3: Gap Analysis
      this.memory.updatePhase('gap-analysis')
      await this.executeWithRetry('gapAnalyst', 'Gap Analysis')

      // Phase 4: Deep Research
      this.memory.updatePhase('deep-research')
      await this.executeWithRetry('deepResearch', 'Deep Research')

      // Phase 5: Content Writing
      this.memory.updatePhase('content-writing')
      const contentResult = await this.executeWithRetry('contentWriting', 'Content Writing')

      // Extract final content
      this.updateProgress('supervisor', 'content-extraction', 95, 'Extracting generated content...')

      // Debug: Log the content result structure
      console.log('🔍 Content result length:', contentResult.length)
      console.log('🔍 Content result preview:', contentResult.substring(0, 200))

      const contentData = this.parseStructuredData(contentResult, 'CONTENT_GENERATION')
      console.log('🔍 Parsed content keys:', Object.keys(contentData))

      // Try multiple extraction methods
      let finalContent = contentData.GENERATED_CONTENT || contentData.generated_content

      // If still not found, try extracting from the raw content
      if (!finalContent || finalContent === 'Content generation failed') {
        // Try to extract content directly from the structured data
        const allKeys = Object.keys(contentData)
        console.log('🔍 All available keys:', allKeys)

        // Look for any key that might contain the content
        for (const key of allKeys) {
          if (key.includes('CONTENT') || key.includes('GENERATED')) {
            finalContent = contentData[key]
            console.log(`🔍 Found content in key: ${key}`)
            break
          }
        }
      }

      // Final fallback: if still no content, return a meaningful message
      if (!finalContent || finalContent.trim().length === 0) {
        finalContent = 'Content generation completed but content extraction failed. Please check the logs.'
        console.log('❌ Content extraction failed, using fallback message')
      } else {
        console.log('✅ Content successfully extracted, length:', finalContent.length)
      }

      this.memory.updatePhase('completed')
      this.updateProgress('supervisor', 'completion', 100, 'Workflow completed successfully')

      return {
        success: true,
        content: finalContent,
        memory: {
          topic: this.memory.retrieve('topic'),
          topicAnalysis: this.memory.retrieve('topicAnalysis'),
          primaryResearch: this.memory.retrieve('primaryResearch'),
          gapAnalysis: this.memory.retrieve('gapAnalysis'),
          deepResearch: this.memory.retrieve('deepResearch'),
          contentGeneration: this.memory.retrieve('contentGeneration'),
          metadata: (this.memory as any).memory?.metadata || {
            startTime: new Date(),
            currentPhase: 'completed',
            completedPhases: [],
            errors: [],
            agentLogs: []
          }
        } as SharedMemory,
        executionTime: Date.now() - startTime,
        errors: (this.memory as any).memory?.metadata?.errors || []
      }

    } catch (error) {
      const errorMsg = `Workflow failed: ${error}`
      errors.push(errorMsg)
      this.memory.logError(errorMsg)

      return {
        success: false,
        content: '',
        memory: {
          topic: this.memory.retrieve('topic'),
          topicAnalysis: this.memory.retrieve('topicAnalysis'),
          primaryResearch: this.memory.retrieve('primaryResearch'),
          gapAnalysis: this.memory.retrieve('gapAnalysis'),
          deepResearch: this.memory.retrieve('deepResearch'),
          contentGeneration: this.memory.retrieve('contentGeneration'),
          metadata: (this.memory as any).memory?.metadata || {
            startTime: new Date(),
            currentPhase: 'failed',
            completedPhases: [],
            errors: [],
            agentLogs: []
          }
        } as SharedMemory,
        executionTime: Date.now() - startTime,
        errors
      }
    }
  }

  private async executeWithRetry(agentKey: keyof typeof this.agents, phaseName: string): Promise<string> {
    const agent = this.agents[agentKey]
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.options.maxRetries!; attempt++) {
      try {
        this.updateProgress('supervisor', 'agent-execution', 0, `Executing ${phaseName} (attempt ${attempt})`)

        const result = await Promise.race([
          agent.execute(),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Agent execution timeout')), this.options.timeoutMs)
          )
        ])

        this.updateProgress('supervisor', 'agent-completion', 0, `${phaseName} completed successfully`)
        return result

      } catch (error) {
        lastError = error as Error
        this.memory.logError(`${phaseName} attempt ${attempt} failed: ${error}`)

        if (attempt < this.options.maxRetries!) {
          const delay = Math.pow(2, attempt) * 1000 // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    throw lastError || new Error(`${phaseName} failed after ${this.options.maxRetries} attempts`)
  }

  private updateProgress(agent: string, phase: string, progress: number, message: string): void {
    if (this.onProgress) {
      this.onProgress({
        agent,
        phase,
        step: phase,
        progress,
        message,
        timestamp: new Date()
      })
    }
  }

  private parseStructuredData(content: string, delimiter: string): Record<string, string> {
    const result: Record<string, string> = {}
    const sections = content.split(`===${delimiter}===`)

    for (let i = 1; i < sections.length; i += 2) {
      if (i + 1 < sections.length) {
        const key = sections[i].trim()
        const value = sections[i + 1].split('===')[0].trim()
        result[key] = value
      }
    }

    return result
  }

  // Get memory summary for debugging
  getMemorySummary(): string {
    return this.memory.getSummary()
  }

  // Get specific memory section
  getMemorySection(section: keyof SharedMemory): string {
    return this.memory.retrieve(section)
  }
}
