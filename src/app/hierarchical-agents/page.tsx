'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Brain, Search, BarChart3, Zap, PenTool,
  Play, Clock, CheckCircle, AlertCircle,
  FileText, Eye, Edit3, Download, Share2,
  Sparkles, Target, Database, Lightbulb
} from 'lucide-react'
import Enhanced<PERSON><PERSON><PERSON>WYGEditor from '@/components/editor/EnhancedWYSIWYGEditor'
import WarmSE<PERSON><PERSON> from '@/components/editor/WarmSEOMeter'
import ModernCard from '@/components/ui/ModernCard'
import ModernButton from '@/components/ui/ModernButton'

interface AgentProgress {
  agent: string
  phase: string
  step: string
  progress: number
  message: string
  timestamp: Date
}

interface WorkflowResult {
  success: boolean
  content: string
  metadata: {
    topic: string
    executionTime: number
    progressLogs: AgentProgress[]
    memorySize: number
    completedPhases: string[]
    errors: string[]
  }
}

export default function HierarchicalAgentsPage() {
  const [topic, setTopic] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<WorkflowResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [progressLogs, setProgressLogs] = useState<AgentProgress[]>([])
  const [currentPhase, setCurrentPhase] = useState('')
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('preview')
  const [showEditor, setShowEditor] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!topic.trim()) {
      setError('Please enter a topic')
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)
    setProgressLogs([])
    setCurrentPhase('')
    setShowEditor(false)

    try {
      console.log('🚀 Starting hierarchical agent workflow...')

      const response = await fetch('/api/hierarchical-agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic.trim(),
          options: {
            maxRetries: 3,
            timeoutMs: 300000 // 5 minutes
          }
        }),
      })

      const data = await response.json()

      if (data.success) {
        setResult(data)
        setProgressLogs(data.metadata.progressLogs || [])
        setShowEditor(true)
        console.log('✅ Workflow completed successfully')
      } else {
        setError(data.error || 'Workflow failed')
        if (data.metadata?.progressLogs) {
          setProgressLogs(data.metadata.progressLogs)
        }
        console.error('❌ Workflow failed:', data.details)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      console.error('API request failed:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Agent configuration with warm colors and icons
  const agents = [
    {
      id: 'topic-analyst',
      name: 'Topic Analyst',
      description: 'Keyword extraction + Qwen analysis',
      icon: Brain,
      color: 'from-red-500 to-orange-500',
      bgColor: 'from-red-50 to-orange-50',
      textColor: 'text-red-700',
      borderColor: 'border-red-200'
    },
    {
      id: 'primary-research',
      name: 'Primary Research',
      description: 'Multi-query Google search',
      icon: Search,
      color: 'from-orange-500 to-amber-500',
      bgColor: 'from-orange-50 to-amber-50',
      textColor: 'text-orange-700',
      borderColor: 'border-orange-200'
    },
    {
      id: 'gap-analyst',
      name: 'Gap Analyst',
      description: 'Qwen gap analysis',
      icon: BarChart3,
      color: 'from-amber-500 to-yellow-500',
      bgColor: 'from-amber-50 to-yellow-50',
      textColor: 'text-amber-700',
      borderColor: 'border-amber-200'
    },
    {
      id: 'deep-research',
      name: 'Deep Research',
      description: 'Targeted gap filling',
      icon: Zap,
      color: 'from-yellow-500 to-orange-500',
      bgColor: 'from-yellow-50 to-orange-50',
      textColor: 'text-yellow-700',
      borderColor: 'border-yellow-200'
    },
    {
      id: 'content-writing',
      name: 'Content Writing',
      description: 'RAG-based Gemini generation',
      icon: PenTool,
      color: 'from-orange-500 to-red-500',
      bgColor: 'from-orange-50 to-red-50',
      textColor: 'text-orange-700',
      borderColor: 'border-orange-200'
    }
  ]

  const handleContentChange = (newContent: string) => {
    if (result) {
      setResult({
        ...result,
        content: newContent
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 via-orange-600 to-amber-600 text-white">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Sparkles className="h-8 w-8" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold">
                Aayush Agent
              </h1>
            </div>
            <p className="text-xl text-orange-100 max-w-3xl mx-auto leading-relaxed">
              Advanced hierarchical AI system with 5 specialized agents for comprehensive content research and generation
            </p>
          </motion.div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Features Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <ModernCard className="p-6 bg-gradient-to-br from-white to-red-50/50 border border-red-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-lg">
                <Brain className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Intelligent Research</h3>
            </div>
            <p className="text-gray-600 leading-relaxed">
              Our AI agents conduct comprehensive research using advanced search strategies and content analysis to gather authoritative information.
            </p>
          </ModernCard>

          <ModernCard className="p-6 bg-gradient-to-br from-white to-orange-50/50 border border-orange-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Gap Analysis</h3>
            </div>
            <p className="text-gray-600 leading-relaxed">
              Advanced gap detection identifies missing information and ensures comprehensive coverage of your topic with targeted deep research.
            </p>
          </ModernCard>

          <ModernCard className="p-6 bg-gradient-to-br from-white to-amber-50/50 border border-amber-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-amber-100 rounded-lg">
                <PenTool className="h-6 w-6 text-amber-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Human-like Writing</h3>
            </div>
            <p className="text-gray-600 leading-relaxed">
              RAG-based content generation creates engaging, authoritative articles that mimic human writing styles while incorporating all research findings.
            </p>
          </ModernCard>
        </div>

        {/* Agent Workflow Overview */}
        <ModernCard className="mb-8 p-8 bg-gradient-to-br from-white to-orange-50/50 border border-orange-200">
          <div className="flex items-center gap-3 mb-6">
            <Target className="h-6 w-6 text-orange-600" />
            <h2 className="text-2xl font-bold text-gray-900">5-Agent Workflow Pipeline</h2>
            <div className="ml-auto flex items-center gap-2 text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full">
              <Sparkles className="h-4 w-4" />
              <span className="font-medium">Supervisor Managed</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {agents.map((agent, index) => (
              <motion.div
                key={agent.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`relative p-6 rounded-xl bg-gradient-to-br ${agent.bgColor} border ${agent.borderColor} hover:shadow-lg transition-all duration-300 group`}
              >
                <div className="text-center">
                  <div className={`w-16 h-16 bg-gradient-to-r ${agent.color} text-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <agent.icon className="h-8 w-8" />
                  </div>
                  <div className="absolute -top-2 -left-2 w-8 h-8 bg-gradient-to-r from-orange-400 to-red-400 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                    {index + 1}
                  </div>
                  <h3 className={`font-bold text-lg ${agent.textColor} mb-2`}>
                    {agent.name}
                  </h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {agent.description}
                  </p>
                </div>

                {/* Connection Arrow */}
                {index < agents.length - 1 && (
                  <div className="hidden md:block absolute -right-3 top-1/2 transform -translate-y-1/2 z-10">
                    <div className="w-6 h-0.5 bg-gradient-to-r from-orange-300 to-red-300"></div>
                    <div className="absolute -right-1 -top-1 w-2 h-2 bg-red-400 rounded-full"></div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          <div className="mt-6 p-4 bg-gradient-to-r from-orange-100 to-amber-100 rounded-lg border border-orange-200">
            <div className="flex items-center gap-2 text-sm text-orange-800">
              <Database className="h-4 w-4" />
              <span className="font-medium">Shared Memory:</span>
              <span>All agents access the same knowledge base with structured data delimiters</span>
            </div>
          </div>
        </ModernCard>

        {/* Input Form */}
        <ModernCard className="mb-8 p-8 bg-gradient-to-br from-white to-amber-50/30 border border-amber-200">
          <div className="flex items-center gap-3 mb-6">
            <Lightbulb className="h-6 w-6 text-amber-600" />
            <h2 className="text-2xl font-bold text-gray-900">Start Your Research</h2>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <input
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder="Enter a topic to research and write about... (e.g., 'artificial intelligence in healthcare')"
                disabled={isLoading}
                className="w-full px-6 py-4 text-lg bg-white/90 backdrop-blur-sm border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 placeholder-gray-500 text-gray-900 shadow-sm"
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <FileText className="h-5 w-5 text-orange-400" />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span>Estimated time: 3-5 minutes</span>
                </div>
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-amber-500" />
                  <span>Sources: 15-25 pages</span>
                </div>
              </div>

              <ModernButton
                type="submit"
                disabled={isLoading || !topic.trim()}
                className="px-8 py-3 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Processing...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    Start Workflow
                  </div>
                )}
              </ModernButton>
            </div>
          </form>
        </ModernCard>

        {/* Progress Tracking */}
        <AnimatePresence>
          {(isLoading || progressLogs.length > 0) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-8"
            >
              <ModernCard className="p-8 bg-gradient-to-br from-white to-blue-50/30 border border-blue-200">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                  </div>
                  <h2 className="text-xl font-bold text-gray-900">Workflow Progress</h2>
                  {isLoading && (
                    <div className="ml-auto flex items-center gap-2 text-blue-600">
                      <div className="w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin" />
                      <span className="text-sm font-medium">Processing...</span>
                    </div>
                  )}
                </div>

                {/* Agent Progress Indicators */}
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                  {agents.map((agent, index) => {
                    const agentLogs = progressLogs.filter(log => log.agent.toLowerCase().includes(agent.id.split('-')[0]))
                    const isActive = agentLogs.length > 0
                    const isCompleted = agentLogs.some(log => log.progress === 100)

                    return (
                      <div
                        key={agent.id}
                        className={`p-4 rounded-lg border transition-all duration-300 ${
                          isCompleted
                            ? `bg-gradient-to-br ${agent.bgColor} ${agent.borderColor} shadow-md`
                            : isActive
                              ? 'bg-blue-50 border-blue-200 shadow-sm'
                              : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            isCompleted
                              ? `bg-gradient-to-r ${agent.color} text-white`
                              : isActive
                                ? 'bg-blue-100 text-blue-600'
                                : 'bg-gray-100 text-gray-400'
                          }`}>
                            {isCompleted ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : isActive ? (
                              <div className="w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin" />
                            ) : (
                              <agent.icon className="h-4 w-4" />
                            )}
                          </div>
                          <div>
                            <div className={`text-sm font-medium ${
                              isCompleted ? agent.textColor : isActive ? 'text-blue-700' : 'text-gray-500'
                            }`}>
                              {agent.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {isCompleted ? 'Completed' : isActive ? 'Processing...' : 'Waiting'}
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Detailed Progress Logs */}
                {progressLogs.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto border border-gray-200">
                    <div className="space-y-2">
                      {progressLogs.slice(-10).map((log, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-center gap-3 text-sm"
                        >
                          <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0" />
                          <span className="font-medium text-blue-700">[{log.agent}]</span>
                          <span className="text-gray-700 flex-1">{log.message}</span>
                          <span className="text-blue-600 font-medium">({log.progress}%)</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </ModernCard>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="mb-8"
            >
              <ModernCard className="p-6 bg-gradient-to-br from-red-50 to-orange-50 border border-red-200">
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-red-100 rounded-lg flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-red-800 mb-2">Workflow Error</h3>
                    <p className="text-red-700 leading-relaxed">{error}</p>
                  </div>
                </div>
              </ModernCard>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Results and WYSIWYG Editor */}
        <AnimatePresence>
          {result && showEditor && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-8"
            >
              {/* Success Metadata */}
              <ModernCard className="p-8 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-green-800">Workflow Completed Successfully!</h2>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center p-4 bg-white/60 rounded-lg border border-green-200">
                    <Clock className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-800">
                      {(result.metadata.executionTime / 1000).toFixed(1)}s
                    </div>
                    <div className="text-sm text-green-600">Execution Time</div>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg border border-green-200">
                    <Database className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-800">
                      {result.metadata.memorySize}
                    </div>
                    <div className="text-sm text-green-600">Memory Logs</div>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg border border-green-200">
                    <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-800">
                      {result.metadata.completedPhases.length}
                    </div>
                    <div className="text-sm text-green-600">Completed Phases</div>
                  </div>
                  <div className="text-center p-4 bg-white/60 rounded-lg border border-green-200">
                    <AlertCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-800">
                      {result.metadata.errors.length}
                    </div>
                    <div className="text-sm text-green-600">Errors</div>
                  </div>
                </div>
              </ModernCard>

              {/* SEO Analysis */}
              <WarmSEOMeter
                content={result.content}
                targetKeyword={result.metadata.topic}
                className="mb-8"
              />

              {/* WYSIWYG Editor */}
              <ModernCard className="overflow-hidden bg-white border border-orange-200 shadow-xl">
                <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                        <PenTool className="h-6 w-6" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold">Generated Article</h2>
                        <p className="text-orange-100">Edit and refine your content with our enhanced WYSIWYG editor</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="flex bg-white/20 rounded-lg p-1 backdrop-blur-sm">
                        <button
                          onClick={() => setViewMode('edit')}
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                            viewMode === 'edit'
                              ? 'bg-white text-orange-600 shadow-sm'
                              : 'text-white hover:bg-white/20'
                          }`}
                        >
                          <Edit3 className="h-4 w-4 mr-2 inline" />
                          Edit
                        </button>
                        <button
                          onClick={() => setViewMode('preview')}
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                            viewMode === 'preview'
                              ? 'bg-white text-orange-600 shadow-sm'
                              : 'text-white hover:bg-white/20'
                          }`}
                        >
                          <Eye className="h-4 w-4 mr-2 inline" />
                          Preview
                        </button>
                      </div>

                      <div className="flex gap-2">
                        <button
                          className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors backdrop-blur-sm"
                          onClick={() => {
                            const blob = new Blob([result.content], { type: 'text/markdown' })
                            const url = URL.createObjectURL(blob)
                            const a = document.createElement('a')
                            a.href = url
                            a.download = `${result.metadata.topic.replace(/\s+/g, '-').toLowerCase()}.md`
                            document.body.appendChild(a)
                            a.click()
                            document.body.removeChild(a)
                            URL.revokeObjectURL(url)
                          }}
                          title="Download as Markdown"
                        >
                          <Download className="h-5 w-5" />
                        </button>
                        <button
                          className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors backdrop-blur-sm"
                          onClick={() => {
                            if (navigator.share) {
                              navigator.share({
                                title: `Article: ${result.metadata.topic}`,
                                text: result.content.substring(0, 200) + '...',
                                url: window.location.href
                              })
                            } else {
                              navigator.clipboard.writeText(result.content)
                              alert('Content copied to clipboard!')
                            }
                          }}
                          title="Share or Copy"
                        >
                          <Share2 className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="h-[800px]">
                  <EnhancedWYSIWYGEditor
                    content={result.content}
                    onChange={handleContentChange}
                    viewMode={viewMode}
                  />
                </div>
              </ModernCard>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Loading State */}
        <AnimatePresence>
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-16"
            >
              <div className="max-w-md mx-auto">
                <div className="relative">
                  <div className="w-20 h-20 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto mb-6"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Sparkles className="h-8 w-8 text-orange-600 animate-pulse" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Agents are working their magic...
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our intelligent agents are researching, analyzing, and crafting your content.
                  This process typically takes 3-5 minutes.
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Footer Information */}
        {!isLoading && !showEditor && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-16 text-center"
          >
            <ModernCard className="p-8 bg-gradient-to-br from-white to-orange-50/30 border border-orange-200">
              <div className="max-w-3xl mx-auto">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  How It Works
                </h3>
                <p className="text-gray-600 leading-relaxed mb-6">
                  Our hierarchical agent system uses a supervisor to coordinate 5 specialized AI agents.
                  Each agent has a specific role in the content creation pipeline, from initial research
                  to final content generation, ensuring comprehensive and high-quality results.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                  <div className="flex items-center gap-3 text-gray-600">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full flex items-center justify-center flex-shrink-0">
                      <Search className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Google Search Integration</div>
                      <div>Real-time web research</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 text-gray-600">
                    <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-amber-500 text-white rounded-full flex items-center justify-center flex-shrink-0">
                      <Brain className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Qwen + Gemini Models</div>
                      <div>Advanced AI reasoning</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 text-gray-600">
                    <div className="w-8 h-8 bg-gradient-to-r from-amber-500 to-yellow-500 text-white rounded-full flex items-center justify-center flex-shrink-0">
                      <Database className="h-4 w-4" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Shared Memory System</div>
                      <div>Structured data coordination</div>
                    </div>
                  </div>
                </div>
              </div>
            </ModernCard>
          </motion.div>
        )}
      </div>
    </div>
  )
}
