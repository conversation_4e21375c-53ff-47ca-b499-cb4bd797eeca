/**
 * API Route for Aayush Agent (Hierarchical Agent System)
 *
 * Endpoint to execute the Aayush agent workflow for intelligent content generation
 */

import { NextRequest, NextResponse } from 'next/server'
import { SupervisorAgent, AgentProgress } from '@/lib/agents/hierarchical-agent-system'

export async function POST(request: NextRequest) {
  try {
    const { topic, options = {} } = await request.json()

    if (!topic || typeof topic !== 'string' || topic.trim().length === 0) {
      return NextResponse.json(
        { error: 'Topic is required and must be a non-empty string' },
        { status: 400 }
      )
    }

    console.log(`🚀 Starting Aayush agent workflow for topic: "${topic}"`)

    // Progress tracking
    const progressLogs: AgentProgress[] = []
    const onProgress = (progress: AgentProgress) => {
      progressLogs.push(progress)
      console.log(`📊 [Aayush:${progress.agent}] ${progress.phase}: ${progress.message} (${progress.progress}%)`)
    }

    // Initialize supervisor with all agents
    const supervisor = new SupervisorAgent(topic, onProgress, {
      maxRetries: options.maxRetries || 3,
      timeoutMs: options.timeoutMs || 300000, // 5 minutes
      enableDetailedLogging: true
    })

    // Execute the complete workflow
    const result = await supervisor.executeWorkflow()

    if (result.success) {
      console.log(`✅ Workflow completed successfully in ${result.executionTime}ms`)

      // Safely extract metadata with fallbacks
      const metadata = result.memory?.metadata || {}
      const agentLogs = metadata.agentLogs || []
      const completedPhases = metadata.completedPhases || []

      return NextResponse.json({
        success: true,
        content: result.content,
        metadata: {
          topic,
          executionTime: result.executionTime,
          progressLogs: progressLogs.slice(-10), // Last 10 progress updates
          memorySize: agentLogs.length,
          completedPhases: completedPhases,
          errors: result.errors || []
        }
      })
    } else {
      console.error(`❌ Workflow failed:`, result.errors)

      return NextResponse.json({
        success: false,
        error: 'Workflow execution failed',
        details: result.errors || [],
        metadata: {
          topic,
          executionTime: result.executionTime,
          progressLogs: progressLogs.slice(-10),
          errors: result.errors || []
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Hierarchical agent API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Aayush Agent API',
    description: 'POST to this endpoint with a topic to start the Aayush agent workflow',
    agent_name: 'Aayush',
    version: '1.0.0',
    usage: {
      method: 'POST',
      body: {
        topic: 'string (required) - The topic to research and write about',
        options: {
          maxRetries: 'number (optional) - Maximum retry attempts per agent (default: 3)',
          timeoutMs: 'number (optional) - Timeout in milliseconds (default: 300000)',
        }
      }
    },
    agents: [
      'TopicAnalyst - Extracts keyword and analyzes topic with Qwen',
      'PrimaryResearch - Conducts multi-query research with Google Search',
      'GapAnalyst - Identifies content gaps using Qwen analysis',
      'DeepResearch - Fills gaps with targeted research',
      'ContentWriting - RAG-based content generation with Gemini'
    ],
    features: [
      'Advanced hierarchical agent coordination',
      'Shared memory system across all agents',
      'Structured data with delimiters (no JSON)',
      'Supervisor coordination and error handling',
      'Progress tracking and detailed logging',
      'Retry logic with exponential backoff',
      'WYSIWYG editor integration',
      'Real-time SEO analysis'
    ]
  })
}
