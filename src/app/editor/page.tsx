'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { motion } from 'framer-motion';
import {
  Save, Download, Copy, Eye, Edit3, BarChart3, Target,
  FileText, Clock, Users, Search, Lightbulb, Star,
  ArrowLeft, Settings, Palette, Type, AlignLeft
} from 'lucide-react';
import { useSearchParams, useRouter } from 'next/navigation';
import EnhancedWYSIWYGEditor from '@/components/editor/EnhancedWYSIWYGEditor';
import EnhancedSEOMeter from '@/components/editor/EnhancedSEOMeter';

interface ArticleData {
  content: string;
  title: string;
  wordCount: number;
  sourcesUsed: number;
  qualityScore: number;
  metadata?: any;
}

function EditorPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [articleData, setArticleData] = useState<ArticleData | null>(null);
  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('edit');

  useEffect(() => {
    // Get article data from URL params or localStorage
    const articleParam = searchParams.get('article');
    if (articleParam) {
      try {
        const data = JSON.parse(decodeURIComponent(articleParam));
        console.log('📝 [EDITOR] Article data from URL params:', data);
        setArticleData(data);
        setContent(data.content || '');
        setTitle(data.title || 'Untitled Article');
      } catch (error) {
        console.error('❌ [EDITOR] Error parsing article data from URL params:', error);
      }
    } else {
      // Try to get from localStorage as fallback
      const savedData = localStorage.getItem('generatedArticle');
      if (savedData) {
        try {
          const data = JSON.parse(savedData);
          console.log('📝 [EDITOR] Article data from localStorage:', data);
          console.log('📝 [EDITOR] Content field:', data.content);
          console.log('📝 [EDITOR] Title field:', data.title);
          setArticleData(data);
          setContent(data.content || '');
          setTitle(data.title || 'Untitled Article');
        } catch (error) {
          console.error('❌ [EDITOR] Error parsing saved article data from localStorage:', error);
        }
      } else {
        console.log('⚠️ [EDITOR] No article data found in localStorage');
      }
    }
    setIsLoading(false);
  }, [searchParams]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save to localStorage
      const updatedData = {
        ...articleData,
        content,
        title,
        lastModified: new Date().toISOString()
      };
      localStorage.setItem('generatedArticle', JSON.stringify(updatedData));
      setLastSaved(new Date());
      
      // Here you could also save to a backend API
      // await fetch('/api/articles/save', { method: 'POST', body: JSON.stringify(updatedData) });
      
    } catch (error) {
      console.error('Error saving article:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDownload = () => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      // You could add a toast notification here
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-orange-700 font-medium">Loading editor...</p>
        </div>
      </div>
    );
  }

  if (!articleData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <FileText className="h-16 w-16 text-orange-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-orange-900 mb-2">No Article Found</h1>
          <p className="text-orange-700 mb-6">
            No article data was found. Please generate an article first using the AI Superagent.
          </p>
          <button
            onClick={() => router.push('/superagent')}
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl hover:from-orange-600 hover:to-red-600 transition-all duration-300 font-semibold"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Generator
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-orange-200 sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-orange-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-orange-700" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-orange-900">Article Editor</h1>
                <p className="text-sm text-orange-600">
                  {lastSaved ? `Last saved: ${lastSaved.toLocaleTimeString()}` : 'Unsaved changes'}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* View Mode Toggle */}
              <div className="flex bg-orange-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('edit')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-all ${
                    viewMode === 'edit'
                      ? 'bg-white text-orange-900 shadow-sm'
                      : 'text-orange-600 hover:text-orange-900'
                  }`}
                >
                  <Edit3 className="h-4 w-4 inline mr-1" />
                  Edit
                </button>
                <button
                  onClick={() => setViewMode('preview')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-all ${
                    viewMode === 'preview'
                      ? 'bg-white text-orange-900 shadow-sm'
                      : 'text-orange-600 hover:text-orange-900'
                  }`}
                >
                  <Eye className="h-4 w-4 inline mr-1" />
                  Preview
                </button>
              </div>

              {/* Action Buttons */}
              <button
                onClick={handleCopy}
                className="p-2 hover:bg-orange-100 rounded-lg transition-colors text-orange-700"
                title="Copy to clipboard"
              >
                <Copy className="h-4 w-4" />
              </button>
              <button
                onClick={handleDownload}
                className="p-2 hover:bg-orange-100 rounded-lg transition-colors text-orange-700"
                title="Download as Markdown"
              >
                <Download className="h-4 w-4" />
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg hover:from-orange-600 hover:to-red-600 transition-all duration-300 font-medium disabled:opacity-50"
              >
                <Save className="h-4 w-4" />
                {isSaving ? 'Saving...' : 'Save'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Editor Section */}
          <div className="xl:col-span-3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-orange-200 overflow-hidden"
            >
              {/* Title Editor */}
              <div className="p-6 border-b border-orange-100">
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full text-2xl font-bold text-orange-900 bg-transparent border-none outline-none placeholder-orange-400"
                  placeholder="Article Title..."
                />
              </div>

              {/* Enhanced WYSIWYG Editor */}
              <div className="min-h-[600px]">
                <EnhancedWYSIWYGEditor
                  content={content}
                  onChange={setContent}
                  viewMode={viewMode}
                />
              </div>
            </motion.div>
          </div>

          {/* SEO Meter Sidebar */}
          <div className="xl:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <EnhancedSEOMeter
                content={content}
                title={title}
                articleData={articleData}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function EditorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-orange-700 font-medium">Loading editor...</p>
        </div>
      </div>
    }>
      <EditorPageContent />
    </Suspense>
  );
}
