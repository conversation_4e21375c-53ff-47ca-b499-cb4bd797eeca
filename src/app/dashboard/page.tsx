'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import {
  PenTool,
  Mail,
  Twitter,
  Video,
  TrendingUp,
  Clock,
  FileText,
  Zap,
  Users,
  BarChart3,
  ArrowRight,
  Plus,
  Star,
  Calendar,
  Target
} from 'lucide-react'
import ModernCard from '@/components/ui/ModernCard'
import ModernButton from '@/components/ui/ModernButton'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import {
  ContentViewsWidget,
  EngagementWidget,
  ContentCreatedWidget,
  AvgReadTimeWidget,
  ConversionRateWidget,
  AIGenerationsWidget
} from '@/components/dashboard/AnalyticsWidget'

const tools = [
  {
    id: 'aayush',
    title: 'Aayush Agent',
    description: 'Advanced hierarchical AI system for comprehensive content research and generation',
    icon: Zap,
    href: '/hierarchical-agents',
    gradient: 'from-orange-500 to-red-500',
    usage: '15 articles this month',
    popular: true,
    featured: true
  },
  {
    id: 'blog',
    title: 'Blog Generator',
    description: 'Create SEO-optimized blog posts with AI research',
    icon: PenTool,
    href: '/blog',
    gradient: 'from-blue-500 to-purple-600',
    usage: '24 posts this month',
    popular: true
  },
  {
    id: 'email',
    title: 'Email Generator',
    description: 'Craft high-converting email campaigns',
    icon: Mail,
    href: '/email',
    gradient: 'from-pink-500 to-rose-500',
    usage: '12 emails this month',
    popular: false
  },
  {
    id: 'tweet',
    title: 'Tweet Generator',
    description: 'Generate viral-worthy social media content',
    icon: Twitter,
    href: '/tweet',
    gradient: 'from-cyan-500 to-blue-500',
    usage: '48 tweets this month',
    popular: false
  },
  {
    id: 'youtube',
    title: 'YouTube Scripts',
    description: 'Create engaging video scripts with hooks',
    icon: Video,
    href: '/youtube',
    gradient: 'from-orange-500 to-red-500',
    usage: '8 scripts this month',
    popular: false
  }
]

const stats = [
  {
    title: 'Content Generated',
    value: '92',
    change: '+12%',
    trend: 'up',
    icon: FileText,
    color: 'blue'
  },
  {
    title: 'Words Written',
    value: '45.2K',
    change: '+8%',
    trend: 'up',
    icon: PenTool,
    color: 'purple'
  },
  {
    title: 'Time Saved',
    value: '24h',
    change: '+15%',
    trend: 'up',
    icon: Clock,
    color: 'green'
  },
  {
    title: 'Engagement Rate',
    value: '94%',
    change: '+3%',
    trend: 'up',
    icon: TrendingUp,
    color: 'orange'
  }
]

const recentContent = [
  {
    id: 1,
    title: 'The Future of AI in Healthcare',
    type: 'Aayush Article',
    createdAt: '2 hours ago',
    status: 'Published',
    engagement: '1.2K views',
    agent: 'Aayush'
  },
  {
    id: 2,
    title: 'Sustainable Technology Trends 2024',
    type: 'Aayush Article',
    createdAt: '4 hours ago',
    status: 'Published',
    engagement: '856 views',
    agent: 'Aayush'
  },
  {
    id: 3,
    title: 'Welcome to Our Newsletter',
    type: 'Email',
    createdAt: '5 hours ago',
    status: 'Sent',
    engagement: '89% open rate'
  },
  {
    id: 4,
    title: 'Top 10 AI Tools for 2024',
    type: 'Tweet Thread',
    createdAt: '1 day ago',
    status: 'Published',
    engagement: '245 likes'
  }
]

export default function DashboardPage() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                Welcome back, John! 👋
              </h1>
              <p className="text-white/70">
                Ready to create amazing content? Let's see what you can build today.
              </p>
            </div>
            <ModernButton
              variant="primary"
              size="lg"
              icon={<Plus className="w-5 h-5" />}
            >
              Create Content
            </ModernButton>
          </div>
        </motion.div>

        {/* Analytics Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          <ContentCreatedWidget />
          <AvgReadTimeWidget />
          <ConversionRateWidget />
        </motion.div>

        {/* Advanced Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.15 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          <ContentViewsWidget />
          <EngagementWidget />
        </motion.div>

        {/* Aayush Agent Spotlight */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.15 }}
        >
          <ModernCard variant="glass" className="p-8 bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/30">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg">
                  <Zap className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Aayush Agent</h2>
                  <p className="text-orange-200">Advanced Hierarchical AI Content System</p>
                </div>
              </div>
              <Link href="/hierarchical-agents">
                <ModernButton
                  variant="primary"
                  className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  icon={<ArrowRight className="w-4 h-4" />}
                >
                  Launch Aayush
                </ModernButton>
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="text-center p-4 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="text-2xl font-bold text-orange-400 mb-1">15</div>
                <div className="text-sm text-white/70">Articles Generated</div>
              </div>
              <div className="text-center p-4 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="text-2xl font-bold text-orange-400 mb-1">127</div>
                <div className="text-sm text-white/70">Sources Analyzed</div>
              </div>
              <div className="text-center p-4 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="text-2xl font-bold text-orange-400 mb-1">4.2m</div>
                <div className="text-sm text-white/70">Words Processed</div>
              </div>
              <div className="text-center p-4 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="text-2xl font-bold text-orange-400 mb-1">98%</div>
                <div className="text-sm text-white/70">Success Rate</div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                  <Target className="w-4 h-4 text-red-400" />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">Topic Analyst</div>
                  <div className="text-xs text-white/60">Keyword extraction</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-orange-400" />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">Primary Research</div>
                  <div className="text-xs text-white/60">Multi-query search</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="w-8 h-8 bg-amber-500/20 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-amber-400" />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">Gap Analyst</div>
                  <div className="text-xs text-white/60">Content gaps</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                  <Zap className="w-4 h-4 text-yellow-400" />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">Deep Research</div>
                  <div className="text-xs text-white/60">Targeted filling</div>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20">
                <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                  <PenTool className="w-4 h-4 text-red-400" />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">Content Writing</div>
                  <div className="text-xs text-white/60">RAG generation</div>
                </div>
              </div>
            </div>
          </ModernCard>
        </motion.div>

        {/* AI Usage Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <AIGenerationsWidget />
        </motion.div>

        {/* Tools Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Content Tools</h2>
            <Link href="/tools" className="text-blue-400 hover:text-blue-300 text-sm font-medium">
              View all tools →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tools.map((tool, index) => (
              <motion.div
                key={tool.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                className={tool.featured ? 'md:col-span-2' : ''}
              >
                <ModernCard
                  variant="elevated"
                  className={`p-6 h-full group cursor-pointer relative overflow-hidden ${
                    tool.featured
                      ? 'border-2 border-orange-500/50 bg-gradient-to-br from-orange-500/10 to-red-500/10'
                      : ''
                  }`}
                >
                  {tool.featured && (
                    <div className="absolute top-0 right-0 bg-gradient-to-l from-orange-500 to-red-500 text-white px-3 py-1 text-xs font-bold rounded-bl-lg">
                      FEATURED
                    </div>
                  )}

                  <Link href={tool.href} className="block h-full">
                    <div className="flex flex-col h-full">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`p-3 rounded-xl bg-gradient-to-r ${tool.gradient} ${
                          tool.featured ? 'shadow-lg shadow-orange-500/25' : ''
                        }`}>
                          <tool.icon className="w-6 h-6 text-white" />
                        </div>
                        {tool.popular && !tool.featured && (
                          <div className="flex items-center gap-1 px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium">
                            <Star className="w-3 h-3" />
                            Popular
                          </div>
                        )}
                      </div>

                      <h3 className={`font-semibold mb-2 ${
                        tool.featured ? 'text-xl text-white' : 'text-lg text-white'
                      }`}>
                        {tool.title}
                      </h3>

                      <p className={`text-white/70 mb-4 flex-1 ${
                        tool.featured ? 'text-base leading-relaxed' : 'text-sm'
                      }`}>
                        {tool.description}
                      </p>

                      {tool.featured && (
                        <div className="mb-4 p-3 bg-white/5 rounded-lg border border-orange-500/20">
                          <div className="flex items-center gap-2 text-orange-400 text-sm font-medium mb-1">
                            <Zap className="w-4 h-4" />
                            Advanced Features
                          </div>
                          <div className="text-white/70 text-xs">
                            5-agent workflow • Real-time research • WYSIWYG editor • SEO analysis
                          </div>
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <span className="text-white/50 text-xs">{tool.usage}</span>
                        <ArrowRight className="w-4 h-4 text-white/50 group-hover:text-white group-hover:translate-x-1 transition-all" />
                      </div>
                    </div>
                  </Link>
                </ModernCard>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Recent Content & Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="lg:col-span-2"
          >
            <ModernCard variant="glass" className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Recent Content</h3>
                <Link href="/library" className="text-blue-400 hover:text-blue-300 text-sm font-medium">
                  View all →
                </Link>
              </div>

              <div className="space-y-4">
                {recentContent.map((content) => (
                  <div
                    key={content.id}
                    className={`flex items-center justify-between p-4 rounded-xl transition-colors cursor-pointer ${
                      content.agent === 'Aayush'
                        ? 'bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 hover:from-orange-500/15 hover:to-red-500/15'
                        : 'bg-white/5 hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      {content.agent === 'Aayush' && (
                        <div className="p-1.5 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
                          <Zap className="w-3 h-3 text-white" />
                        </div>
                      )}
                      <div className="flex-1">
                        <h4 className="text-white font-medium">{content.title}</h4>
                        <div className="flex items-center gap-4 mt-1">
                          <span className={`text-sm font-medium ${
                            content.agent === 'Aayush' ? 'text-orange-400' : 'text-white/60'
                          }`}>
                            {content.type}
                          </span>
                          <span className="text-white/50 text-sm">{content.createdAt}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 text-sm font-medium">{content.status}</div>
                      <div className="text-white/60 text-sm">{content.engagement}</div>
                    </div>
                  </div>
                ))}
              </div>
            </ModernCard>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <ModernCard variant="glass" className="p-6">
              <h3 className="text-xl font-semibold text-white mb-6">Quick Actions</h3>

              <div className="space-y-3">
                <Link href="/hierarchical-agents">
                  <ModernButton
                    variant="primary"
                    className="w-full justify-start bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                    icon={<Zap className="w-4 h-4" />}
                  >
                    Launch Aayush Agent
                  </ModernButton>
                </Link>
                <ModernButton
                  variant="secondary"
                  className="w-full justify-start"
                  icon={<PenTool className="w-4 h-4" />}
                >
                  Write Blog Post
                </ModernButton>
                <ModernButton
                  variant="secondary"
                  className="w-full justify-start"
                  icon={<Mail className="w-4 h-4" />}
                >
                  Create Email
                </ModernButton>
                <ModernButton
                  variant="secondary"
                  className="w-full justify-start"
                  icon={<Twitter className="w-4 h-4" />}
                >
                  Generate Tweet
                </ModernButton>
                <ModernButton
                  variant="secondary"
                  className="w-full justify-start"
                  icon={<BarChart3 className="w-4 h-4" />}
                >
                  View Analytics
                </ModernButton>
              </div>

              <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-xl border border-blue-500/30">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 font-medium text-sm">Monthly Goal</span>
                </div>
                <div className="text-white text-sm mb-2">Content pieces: 92/100</div>
                <div className="w-full bg-white/10 rounded-full h-2">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                </div>
              </div>
            </ModernCard>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  )
}
