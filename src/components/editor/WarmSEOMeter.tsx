'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Target, TrendingUp, Eye, Search, 
  FileText, Clock, Users, Star,
  CheckCircle, AlertTriangle, XCircle
} from 'lucide-react'

interface SEOMetrics {
  wordCount: number
  readingTime: number
  headingStructure: number
  keywordDensity: number
  readabilityScore: number
  metaDescription: boolean
  internalLinks: number
  externalLinks: number
}

interface WarmSEOMeterProps {
  content: string
  targetKeyword?: string
  className?: string
}

export default function WarmSEOMeter({ content, targetKeyword = '', className = '' }: WarmSEOMeterProps) {
  // Calculate SEO metrics
  const calculateMetrics = (): SEOMetrics => {
    const words = content.split(/\s+/).filter(word => word.length > 0)
    const wordCount = words.length
    const readingTime = Math.ceil(wordCount / 200) // Average reading speed
    
    // Count headings
    const headingMatches = content.match(/^#{1,6}\s/gm) || []
    const headingStructure = headingMatches.length
    
    // Calculate keyword density
    let keywordDensity = 0
    if (targetKeyword && wordCount > 0) {
      const keywordMatches = content.toLowerCase().split(targetKeyword.toLowerCase()).length - 1
      keywordDensity = (keywordMatches / wordCount) * 100
    }
    
    // Simple readability score (based on sentence length)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const avgWordsPerSentence = sentences.length > 0 ? wordCount / sentences.length : 0
    const readabilityScore = Math.max(0, Math.min(100, 100 - (avgWordsPerSentence - 15) * 2))
    
    // Check for meta description (simplified)
    const metaDescription = content.length > 150
    
    // Count links
    const internalLinks = (content.match(/\[.*?\]\((?!http)/g) || []).length
    const externalLinks = (content.match(/\[.*?\]\(http/g) || []).length
    
    return {
      wordCount,
      readingTime,
      headingStructure,
      keywordDensity,
      readabilityScore,
      metaDescription,
      internalLinks,
      externalLinks
    }
  }

  const metrics = calculateMetrics()
  
  // Calculate overall SEO score
  const calculateOverallScore = (): number => {
    let score = 0
    
    // Word count (ideal: 1500-3000 words)
    if (metrics.wordCount >= 1500 && metrics.wordCount <= 3000) score += 20
    else if (metrics.wordCount >= 1000) score += 15
    else if (metrics.wordCount >= 500) score += 10
    
    // Heading structure
    if (metrics.headingStructure >= 5) score += 15
    else if (metrics.headingStructure >= 3) score += 10
    else if (metrics.headingStructure >= 1) score += 5
    
    // Keyword density (ideal: 1-3%)
    if (targetKeyword) {
      if (metrics.keywordDensity >= 1 && metrics.keywordDensity <= 3) score += 20
      else if (metrics.keywordDensity > 0 && metrics.keywordDensity < 5) score += 10
    } else {
      score += 10 // No penalty if no target keyword
    }
    
    // Readability
    if (metrics.readabilityScore >= 80) score += 15
    else if (metrics.readabilityScore >= 60) score += 10
    else if (metrics.readabilityScore >= 40) score += 5
    
    // Meta description
    if (metrics.metaDescription) score += 10
    
    // Links
    if (metrics.internalLinks > 0) score += 10
    if (metrics.externalLinks > 0) score += 10
    
    return Math.min(100, score)
  }

  const overallScore = calculateOverallScore()
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'from-green-500 to-emerald-500'
    if (score >= 60) return 'from-yellow-500 to-orange-500'
    return 'from-red-500 to-pink-500'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return CheckCircle
    if (score >= 60) return AlertTriangle
    return XCircle
  }

  const ScoreIcon = getScoreIcon(overallScore)

  const seoChecks = [
    {
      label: 'Word Count',
      value: metrics.wordCount.toLocaleString(),
      score: metrics.wordCount >= 1500 ? 100 : metrics.wordCount >= 1000 ? 75 : metrics.wordCount >= 500 ? 50 : 25,
      ideal: '1,500-3,000 words',
      icon: FileText
    },
    {
      label: 'Reading Time',
      value: `${metrics.readingTime} min`,
      score: metrics.readingTime >= 5 && metrics.readingTime <= 15 ? 100 : metrics.readingTime >= 3 ? 75 : 50,
      ideal: '5-15 minutes',
      icon: Clock
    },
    {
      label: 'Headings',
      value: metrics.headingStructure.toString(),
      score: metrics.headingStructure >= 5 ? 100 : metrics.headingStructure >= 3 ? 75 : metrics.headingStructure >= 1 ? 50 : 0,
      ideal: '5+ headings',
      icon: Target
    },
    {
      label: 'Readability',
      value: `${metrics.readabilityScore.toFixed(0)}%`,
      score: metrics.readabilityScore,
      ideal: '80%+ score',
      icon: Users
    },
    {
      label: 'Internal Links',
      value: metrics.internalLinks.toString(),
      score: metrics.internalLinks > 0 ? 100 : 0,
      ideal: '2+ links',
      icon: TrendingUp
    },
    {
      label: 'External Links',
      value: metrics.externalLinks.toString(),
      score: metrics.externalLinks > 0 ? 100 : 0,
      ideal: '1+ links',
      icon: Search
    }
  ]

  if (targetKeyword) {
    seoChecks.splice(3, 0, {
      label: 'Keyword Density',
      value: `${metrics.keywordDensity.toFixed(1)}%`,
      score: metrics.keywordDensity >= 1 && metrics.keywordDensity <= 3 ? 100 : metrics.keywordDensity > 0 ? 50 : 0,
      ideal: '1-3% density',
      icon: Eye
    })
  }

  return (
    <div className={`bg-gradient-to-br from-amber-50 to-orange-50 border border-orange-200 rounded-xl p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg">
            <Star className="h-5 w-5" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900">SEO Analysis</h3>
            <p className="text-sm text-gray-600">Content optimization insights</p>
          </div>
        </div>
        
        <div className="text-right">
          <div className="flex items-center gap-2 mb-1">
            <ScoreIcon className={`h-5 w-5 ${overallScore >= 80 ? 'text-green-600' : overallScore >= 60 ? 'text-yellow-600' : 'text-red-600'}`} />
            <span className="text-2xl font-bold text-gray-900">{overallScore}</span>
            <span className="text-gray-500">/100</span>
          </div>
          <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${overallScore}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
              className={`h-full bg-gradient-to-r ${getScoreColor(overallScore)}`}
            />
          </div>
        </div>
      </div>

      {/* SEO Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {seoChecks.map((check, index) => (
          <motion.div
            key={check.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-orange-100"
          >
            <div className="flex items-center gap-3 mb-2">
              <div className={`p-1.5 rounded-md ${
                check.score >= 80 ? 'bg-green-100 text-green-600' :
                check.score >= 60 ? 'bg-yellow-100 text-yellow-600' :
                'bg-red-100 text-red-600'
              }`}>
                <check.icon className="h-4 w-4" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">{check.label}</div>
                <div className="text-xs text-gray-500">{check.ideal}</div>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-lg font-bold text-gray-900">{check.value}</span>
              <div className="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${check.score}%` }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className={`h-full ${
                    check.score >= 80 ? 'bg-green-500' :
                    check.score >= 60 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Target Keyword Info */}
      {targetKeyword && (
        <div className="mt-4 p-3 bg-gradient-to-r from-orange-100 to-amber-100 rounded-lg border border-orange-200">
          <div className="flex items-center gap-2 text-sm">
            <Target className="h-4 w-4 text-orange-600" />
            <span className="font-medium text-orange-800">Target Keyword:</span>
            <span className="text-orange-700">{targetKeyword}</span>
            <span className="text-orange-600">({metrics.keywordDensity.toFixed(1)}% density)</span>
          </div>
        </div>
      )}
    </div>
  )
}
