'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Bold, Italic, Underline, List, ListOrdered, Link2, 
  Image, Quote, Code, Heading1, Heading2, Heading3,
  AlignLeft, AlignCenter, AlignRight, Undo, Redo,
  Type, Palette, Eye, Edit3, Save, Download
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface EnhancedWYSIWYGEditorProps {
  content: string;
  onChange: (content: string) => void;
  viewMode: 'edit' | 'preview';
}

export default function EnhancedWYSIWYGEditor({ content, onChange, viewMode }: EnhancedWYSIWYGEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [selectedText, setSelectedText] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);
  const [wordCount, setWordCount] = useState(0);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
    
    // Update word count
    const words = content.split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, [content]);

  const insertText = (before: string, after: string = '', placeholder: string = '') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newContent = 
      content.substring(0, start) + 
      before + textToInsert + after + 
      content.substring(end);
    
    onChange(newContent);
    
    // Set cursor position after insertion
    setTimeout(() => {
      if (textareaRef.current) {
        const newPosition = start + before.length + textToInsert.length;
        textareaRef.current.setSelectionRange(newPosition, newPosition);
        textareaRef.current.focus();
      }
    }, 0);
  };

  const formatButtons = [
    {
      icon: Heading1,
      label: 'Heading 1',
      action: () => insertText('# ', '', 'Heading 1'),
      color: 'text-red-600 hover:bg-red-50 hover:text-red-700',
      bgColor: 'hover:shadow-red-200'
    },
    {
      icon: Heading2,
      label: 'Heading 2',
      action: () => insertText('## ', '', 'Heading 2'),
      color: 'text-orange-600 hover:bg-orange-50 hover:text-orange-700',
      bgColor: 'hover:shadow-orange-200'
    },
    {
      icon: Heading3,
      label: 'Heading 3',
      action: () => insertText('### ', '', 'Heading 3'),
      color: 'text-amber-600 hover:bg-amber-50 hover:text-amber-700',
      bgColor: 'hover:shadow-amber-200'
    },
    {
      icon: Bold,
      label: 'Bold',
      action: () => insertText('**', '**', 'bold text'),
      color: 'text-red-700 hover:bg-red-50 hover:text-red-800',
      bgColor: 'hover:shadow-red-200'
    },
    {
      icon: Italic,
      label: 'Italic',
      action: () => insertText('*', '*', 'italic text'),
      color: 'text-orange-700 hover:bg-orange-50 hover:text-orange-800',
      bgColor: 'hover:shadow-orange-200'
    },
    {
      icon: Quote,
      label: 'Quote',
      action: () => insertText('> ', '', 'quote'),
      color: 'text-amber-700 hover:bg-amber-50 hover:text-amber-800',
      bgColor: 'hover:shadow-amber-200'
    },
    {
      icon: List,
      label: 'Bullet List',
      action: () => insertText('- ', '', 'list item'),
      color: 'text-red-600 hover:bg-red-50 hover:text-red-700',
      bgColor: 'hover:shadow-red-200'
    },
    {
      icon: ListOrdered,
      label: 'Numbered List',
      action: () => insertText('1. ', '', 'list item'),
      color: 'text-orange-600 hover:bg-orange-50 hover:text-orange-700',
      bgColor: 'hover:shadow-orange-200'
    },
    {
      icon: Link2,
      label: 'Link',
      action: () => insertText('[', '](url)', 'link text'),
      color: 'text-amber-600 hover:bg-amber-50 hover:text-amber-700',
      bgColor: 'hover:shadow-amber-200'
    },
    {
      icon: Image,
      label: 'Image',
      action: () => insertText('![', '](image-url)', 'alt text'),
      color: 'text-red-700 hover:bg-red-50 hover:text-red-800',
      bgColor: 'hover:shadow-red-200'
    },
    {
      icon: Code,
      label: 'Code',
      action: () => insertText('`', '`', 'code'),
      color: 'text-orange-700 hover:bg-orange-50 hover:text-orange-800',
      bgColor: 'hover:shadow-orange-200'
    }
  ];

  if (viewMode === 'preview') {
    return (
      <div className="p-8 min-h-[600px] bg-gradient-to-br from-amber-50/50 via-orange-50/50 to-red-50/30">
        <div className="prose prose-lg prose-orange max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              code({ node, inline, className, children, ...props }: any) {
                const match = /language-(\w+)/.exec(className || '');
                return !inline && match ? (
                  <div className="my-4 rounded-xl overflow-hidden border border-orange-200 shadow-lg">
                    <div className="bg-gradient-to-r from-orange-100 to-red-100 px-4 py-2 text-sm font-medium text-orange-800 border-b border-orange-200">
                      {match[1]}
                    </div>
                    <SyntaxHighlighter
                      style={tomorrow}
                      language={match[1]}
                      PreTag="div"
                      className="!bg-gradient-to-br !from-orange-50 !to-amber-50"
                      {...props}
                    >
                      {String(children).replace(/\n$/, '')}
                    </SyntaxHighlighter>
                  </div>
                ) : (
                  <code className="bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 px-2 py-1 rounded-md text-sm font-medium border border-orange-200" {...props}>
                    {children}
                  </code>
                );
              },
              h1: ({ children }) => (
                <h1 className="text-4xl font-bold bg-gradient-to-r from-red-700 to-orange-600 bg-clip-text text-transparent mb-6 border-b-2 border-gradient-to-r from-red-200 to-orange-200 pb-3">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-3xl font-bold bg-gradient-to-r from-orange-700 to-amber-600 bg-clip-text text-transparent mb-4 border-b border-orange-200 pb-2">
                  {children}
                </h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-2xl font-bold bg-gradient-to-r from-amber-700 to-yellow-600 bg-clip-text text-transparent mb-3">
                  {children}
                </h3>
              ),
              p: ({ children }) => (
                <p className="text-gray-800 leading-relaxed mb-6 text-lg">
                  {children}
                </p>
              ),
              blockquote: ({ children }) => (
                <blockquote className="border-l-4 border-gradient-to-b from-orange-400 to-red-400 pl-6 py-4 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-900 italic my-6 rounded-r-lg shadow-sm">
                  {children}
                </blockquote>
              ),
              ul: ({ children }) => (
                <ul className="list-disc list-inside text-gray-800 mb-6 space-y-2 text-lg">
                  {children}
                </ul>
              ),
              ol: ({ children }) => (
                <ol className="list-decimal list-inside text-gray-800 mb-6 space-y-2 text-lg">
                  {children}
                </ol>
              ),
              li: ({ children }) => (
                <li className="ml-2">
                  {children}
                </li>
              ),
              a: ({ children, href }) => (
                <a 
                  href={href} 
                  className="text-orange-600 hover:text-red-600 underline decoration-orange-300 hover:decoration-red-400 transition-all duration-300 font-medium"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {children}
                </a>
              ),
              strong: ({ children }) => (
                <strong className="font-bold bg-gradient-to-r from-red-700 to-orange-600 bg-clip-text text-transparent">
                  {children}
                </strong>
              ),
              em: ({ children }) => (
                <em className="italic text-orange-800 font-medium">
                  {children}
                </em>
              ),
              table: ({ children }) => (
                <div className="overflow-x-auto my-6">
                  <table className="min-w-full border border-orange-200 rounded-lg overflow-hidden shadow-sm">
                    {children}
                  </table>
                </div>
              ),
              thead: ({ children }) => (
                <thead className="bg-gradient-to-r from-orange-100 to-amber-100">
                  {children}
                </thead>
              ),
              th: ({ children }) => (
                <th className="px-4 py-3 text-left font-semibold text-orange-900 border-b border-orange-200">
                  {children}
                </th>
              ),
              td: ({ children }) => (
                <td className="px-4 py-3 text-gray-800 border-b border-orange-100">
                  {children}
                </td>
              )
            }}
          >
            {content}
          </ReactMarkdown>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-amber-50/30 to-orange-50/30">
      {/* Enhanced Toolbar */}
      <div className="flex flex-wrap items-center gap-2 p-6 bg-gradient-to-r from-amber-50 via-orange-50 to-red-50 border-b border-orange-200 shadow-sm">
        <div className="flex flex-wrap items-center gap-1">
          {formatButtons.map((button, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.05, y: -1 }}
              whileTap={{ scale: 0.95 }}
              onClick={button.action}
              className={`p-3 rounded-xl transition-all duration-300 ${button.color} ${button.bgColor} shadow-sm hover:shadow-md border border-transparent hover:border-orange-200`}
              title={button.label}
            >
              <button.icon className="h-4 w-4" />
            </motion.button>
          ))}
        </div>
        
        <div className="w-px h-8 bg-gradient-to-b from-orange-300 to-red-300 mx-3" />
        
        <div className="flex items-center gap-3 text-sm font-medium">
          <div className="flex items-center gap-2 text-orange-700 bg-white/60 px-3 py-2 rounded-lg border border-orange-200">
            <Type className="h-4 w-4" />
            <span>Words: {wordCount.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-2 text-amber-700 bg-white/60 px-3 py-2 rounded-lg border border-amber-200">
            <Edit3 className="h-4 w-4" />
            <span>Characters: {content.length.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Enhanced Editor */}
      <div className="flex-1 relative">
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => onChange(e.target.value)}
          className="w-full h-full min-h-[500px] p-8 bg-gradient-to-br from-amber-50/20 via-orange-50/20 to-red-50/10 border-none outline-none resize-none text-gray-800 leading-relaxed text-base placeholder-orange-400"
          placeholder="Start writing your article in Markdown... Use the toolbar above for quick formatting!"
          style={{
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
            lineHeight: '1.8'
          }}
        />
        
        {/* Enhanced Markdown Syntax Helper */}
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute bottom-6 right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-orange-200 text-xs text-orange-700 max-w-sm"
        >
          <div className="font-bold mb-3 text-orange-900 flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Markdown Quick Reference
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <code className="bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium"># H1</code>
              <code className="bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium">## H2</code>
              <code className="bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium">### H3</code>
            </div>
            <div className="flex items-center gap-2">
              <code className="bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium">**bold**</code>
              <code className="bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium">*italic*</code>
            </div>
            <div className="flex items-center gap-2">
              <code className="bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium">- list</code>
              <code className="bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium">1. numbered</code>
            </div>
            <div>
              <code className="bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium">[link](url)</code>
            </div>
            <div>
              <code className="bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium">`inline code`</code>
            </div>
            <div>
              <code className="bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium">&gt; blockquote</code>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
