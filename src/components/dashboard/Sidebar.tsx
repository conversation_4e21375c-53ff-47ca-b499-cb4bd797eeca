'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSettings } from '@/contexts/SettingsContext'
import {
  LayoutDashboard,
  PenTool,
  Mail,
  Twitter,
  Video,
  Settings,
  User,
  CreditCard,
  BarChart3,
  FileText,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  LogOut,
  HelpCircle,
  Zap
} from 'lucide-react'

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
}

const navigationItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    badge: null
  },


  {
    title: 'Aayush Agent',
    href: '/hierarchical-agents',
    icon: Zap,
    badge: 'New'
  },
  {
    title: 'AI Superagent',
    href: '/superagent',
    icon: Sparkles,
    badge: 'Legacy'
  },

  {
    title: 'Content Tools',
    items: [
      {
        title: 'Blog Generator',
        href: '/blog',
        icon: PenTool,
        badge: 'Popular'
      },
      {
        title: 'Email Generator',
        href: '/email',
        icon: Mail,
        badge: null
      },
      {
        title: 'Tweet Generator',
        href: '/tweet',
        icon: Twitter,
        badge: 'New'
      },
      {
        title: 'YouTube Scripts',
        href: '/youtube',
        icon: Video,
        badge: null
      }
    ]
  },
  {
    title: 'Analytics',
    href: '/analytics',
    icon: BarChart3,
    badge: null
  },
  {
    title: 'Content Library',
    href: '/library',
    icon: FileText,
    badge: null
  }
]

const bottomItems = [
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings
  },
  {
    title: 'Help & Support',
    href: '/help',
    icon: HelpCircle
  }
]

export default function Sidebar({ isCollapsed, onToggle }: SidebarProps) {
  const pathname = usePathname()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const { settings } = useSettings()

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  const SidebarItem = ({ item, isNested = false }: { item: any, isNested?: boolean }) => {
    const active = isActive(item.href)
    const Icon = item.icon
    const isItemHovered = hoveredItem === item.href

    return (
      <div className="relative">
        <Link
          href={item.href}
          className={`
            relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 cursor-pointer
            ${active
              ? 'bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-white border border-blue-500/30'
              : 'text-white/70 hover:text-white hover:bg-white/5'
            }
            ${isNested ? 'ml-6' : ''}
            ${isCollapsed ? 'justify-center' : ''}
          `}
          onMouseEnter={() => setHoveredItem(item.href)}
          onMouseLeave={() => setHoveredItem(null)}
        >
          {active && (
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-xl" />
          )}

          <div className="relative z-10 flex items-center gap-3 w-full">
            <Icon className={`w-5 h-5 flex-shrink-0 ${active ? 'text-blue-400' : ''}`} />

            {!isCollapsed && (
              <div className="flex items-center justify-between w-full">
                <span className="font-medium">{item.title}</span>
                {item.badge && (
                  <span className={`
                    px-2 py-0.5 text-xs font-semibold rounded-full
                    ${item.badge === 'New'
                      ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                      : 'bg-orange-500/20 text-orange-400 border border-orange-500/30'
                    }
                  `}>
                    {item.badge}
                  </span>
                )}
              </div>
            )}
          </div>
        </Link>

        {/* Tooltip for collapsed state */}
        {isCollapsed && isItemHovered && (
          <div
            className="absolute left-full ml-4 px-3 py-2 bg-gray-800/95 backdrop-blur-sm text-white text-sm rounded-lg shadow-xl border border-white/20 whitespace-nowrap z-[60]"
            style={{
              top: '50%',
              transform: 'translateY(-50%)',
              pointerEvents: 'none'
            }}
          >
            {item.title}
            {item.badge && (
              <span className="ml-2 px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded">
                {item.badge}
              </span>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <motion.aside
      animate={{ width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="fixed left-0 top-0 h-full bg-gray-900/95 backdrop-blur-xl border-r border-white/10 z-40 flex flex-col"
      onMouseLeave={() => setHoveredItem(null)}
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <Sparkles className="w-8 h-8 text-blue-400" />
              <span className="text-xl font-bold text-white">Invincible</span>
            </div>
          )}

          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 cursor-pointer"
          >
            {isCollapsed ? (
              <ChevronRight className="w-5 h-5 text-white/70" />
            ) : (
              <ChevronLeft className="w-5 h-5 text-white/70" />
            )}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 p-4 space-y-2 overflow-y-auto">
        {navigationItems.map((section, index) => (
          <div key={index} className="space-y-1">
            {section.items ? (
              <div className="space-y-1">
                {!isCollapsed && (
                  <div className="px-3 py-2 text-xs font-semibold text-white/50 uppercase tracking-wider">
                    {section.title}
                  </div>
                )}
                {section.items.map((item) => (
                  <SidebarItem key={item.href} item={item} isNested={!isCollapsed} />
                ))}
              </div>
            ) : (
              <SidebarItem item={section} />
            )}
          </div>
        ))}
      </div>

      {/* Bottom Section */}
      <div className="p-4 border-t border-white/10 space-y-2">
        {bottomItems.map((item) => (
          <SidebarItem key={item.href} item={item} />
        ))}

        {/* User Profile */}
        <div className={`
          flex items-center gap-3 p-3 rounded-xl bg-white/5 border border-white/10
          ${isCollapsed ? 'justify-center' : ''}
        `}>
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>

          {!isCollapsed && (
            <div className="flex items-center justify-between w-full">
              <div>
                <div className="text-sm font-medium text-white">
                  {settings.firstName} {settings.lastName}
                </div>
                <div className="text-xs text-white/60">Pro Plan</div>
              </div>
              <button className="p-1 rounded-lg hover:bg-white/10 transition-colors">
                <LogOut className="w-4 h-4 text-white/70" />
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.aside>
  )
}
