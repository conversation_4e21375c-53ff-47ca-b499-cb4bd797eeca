/**
 * Test Script for Hierarchical Agent System
 * 
 * This script tests the hierarchical agent system with a sample topic
 */

const { SupervisorAgent } = require('./src/lib/agents/hierarchical-agent-system.ts')

async function testHierarchicalAgents() {
  console.log('🧪 Testing Hierarchical Agent System')
  console.log('=====================================')

  const testTopic = 'artificial intelligence in healthcare'
  
  console.log(`📝 Test Topic: "${testTopic}"`)
  console.log('')

  // Progress tracking
  const progressLogs = []
  const onProgress = (progress) => {
    progressLogs.push(progress)
    console.log(`📊 [${progress.agent}] ${progress.phase}: ${progress.message} (${progress.progress}%)`)
  }

  try {
    // Initialize supervisor
    console.log('🚀 Initializing Supervisor Agent...')
    const supervisor = new SupervisorAgent(testTopic, onProgress, {
      maxRetries: 2,
      timeoutMs: 180000, // 3 minutes for testing
      enableDetailedLogging: true
    })

    console.log('✅ Supervisor initialized with all agents')
    console.log('')

    // Execute workflow
    console.log('🔄 Starting agent workflow execution...')
    const startTime = Date.now()
    
    const result = await supervisor.executeWorkflow()
    
    const executionTime = Date.now() - startTime
    console.log('')
    console.log('📊 WORKFLOW RESULTS')
    console.log('==================')
    console.log(`Success: ${result.success}`)
    console.log(`Execution Time: ${(executionTime / 1000).toFixed(1)}s`)
    console.log(`Errors: ${result.errors.length}`)
    
    if (result.success) {
      console.log('')
      console.log('📄 GENERATED CONTENT PREVIEW')
      console.log('============================')
      console.log(result.content.substring(0, 500) + '...')
      
      console.log('')
      console.log('🧠 MEMORY SUMMARY')
      console.log('================')
      console.log(supervisor.getMemorySummary())
      
      console.log('')
      console.log('✅ Test completed successfully!')
    } else {
      console.log('')
      console.log('❌ WORKFLOW ERRORS')
      console.log('==================')
      result.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`)
      })
    }

  } catch (error) {
    console.error('')
    console.error('💥 TEST FAILED')
    console.error('==============')
    console.error('Error:', error.message)
    console.error('Stack:', error.stack)
  }
}

// Run the test
if (require.main === module) {
  testHierarchicalAgents()
    .then(() => {
      console.log('')
      console.log('🏁 Test script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('')
      console.error('💥 Test script failed:', error)
      process.exit(1)
    })
}

module.exports = { testHierarchicalAgents }
