"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[672],{47672:(e,r,o)=>{o.d(r,{A:()=>C});var t=o(95155),a=o(12115),n=o(1978),l=o(28440),d=o(22705),s=o(92406),i=o(9727),c=o(19144),g=o(40224),m=o(15968),b=o(89140),x=o(74575),h=o(27213),u=o(29621),p=o(93500),f=o(44940),v=o(33127),j=o(88240),N=o(84823),w=o(58497),y=o(14472);function C(e){let{content:r,onChange:o,viewMode:C}=e,k=(0,a.useRef)(null),[A,H]=(0,a.useState)(""),[S,L]=(0,a.useState)(0),[M,q]=(0,a.useState)(0);(0,a.useEffect)(()=>{k.current&&(k.current.style.height="auto",k.current.style.height=k.current.scrollHeight+"px"),q(r.split(/\s+/).filter(e=>e.length>0).length)},[r]);let _=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(!k.current)return;let n=k.current,l=n.selectionStart,d=n.selectionEnd,s=r.substring(l,d)||a;o(r.substring(0,l)+e+s+t+r.substring(d)),setTimeout(()=>{if(k.current){let r=l+e.length+s.length;k.current.setSelectionRange(r,r),k.current.focus()}},0)},E=[{icon:l.A,label:"Heading 1",action:()=>_("# ","","Heading 1"),color:"text-red-600 hover:bg-red-50 hover:text-red-700",bgColor:"hover:shadow-red-200"},{icon:d.A,label:"Heading 2",action:()=>_("## ","","Heading 2"),color:"text-orange-600 hover:bg-orange-50 hover:text-orange-700",bgColor:"hover:shadow-orange-200"},{icon:s.A,label:"Heading 3",action:()=>_("### ","","Heading 3"),color:"text-amber-600 hover:bg-amber-50 hover:text-amber-700",bgColor:"hover:shadow-amber-200"},{icon:i.A,label:"Bold",action:()=>_("**","**","bold text"),color:"text-red-700 hover:bg-red-50 hover:text-red-800",bgColor:"hover:shadow-red-200"},{icon:c.A,label:"Italic",action:()=>_("*","*","italic text"),color:"text-orange-700 hover:bg-orange-50 hover:text-orange-800",bgColor:"hover:shadow-orange-200"},{icon:g.A,label:"Quote",action:()=>_("> ","","quote"),color:"text-amber-700 hover:bg-amber-50 hover:text-amber-800",bgColor:"hover:shadow-amber-200"},{icon:m.A,label:"Bullet List",action:()=>_("- ","","list item"),color:"text-red-600 hover:bg-red-50 hover:text-red-700",bgColor:"hover:shadow-red-200"},{icon:b.A,label:"Numbered List",action:()=>_("1. ","","list item"),color:"text-orange-600 hover:bg-orange-50 hover:text-orange-700",bgColor:"hover:shadow-orange-200"},{icon:x.A,label:"Link",action:()=>_("[","](url)","link text"),color:"text-amber-600 hover:bg-amber-50 hover:text-amber-700",bgColor:"hover:shadow-amber-200"},{icon:h.A,label:"Image",action:()=>_("![","](image-url)","alt text"),color:"text-red-700 hover:bg-red-50 hover:text-red-800",bgColor:"hover:shadow-red-200"},{icon:u.A,label:"Code",action:()=>_("`","`","code"),color:"text-orange-700 hover:bg-orange-50 hover:text-orange-800",bgColor:"hover:shadow-orange-200"}];return"preview"===C?(0,t.jsx)("div",{className:"p-8 min-h-[600px] bg-gradient-to-br from-amber-50/50 via-orange-50/50 to-red-50/30",children:(0,t.jsx)("div",{className:"prose prose-lg prose-orange max-w-none",children:(0,t.jsx)(j.oz,{remarkPlugins:[N.A],components:{code(e){let{node:r,inline:o,className:a,children:n,...l}=e,d=/language-(\w+)/.exec(a||"");return!o&&d?(0,t.jsxs)("div",{className:"my-4 rounded-xl overflow-hidden border border-orange-200 shadow-lg",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-orange-100 to-red-100 px-4 py-2 text-sm font-medium text-orange-800 border-b border-orange-200",children:d[1]}),(0,t.jsx)(w.A,{style:y.A,language:d[1],PreTag:"div",className:"!bg-gradient-to-br !from-orange-50 !to-amber-50",...l,children:String(n).replace(/\n$/,"")})]}):(0,t.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 px-2 py-1 rounded-md text-sm font-medium border border-orange-200",...l,children:n})},h1:e=>{let{children:r}=e;return(0,t.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-red-700 to-orange-600 bg-clip-text text-transparent mb-6 border-b-2 border-gradient-to-r from-red-200 to-orange-200 pb-3",children:r})},h2:e=>{let{children:r}=e;return(0,t.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-orange-700 to-amber-600 bg-clip-text text-transparent mb-4 border-b border-orange-200 pb-2",children:r})},h3:e=>{let{children:r}=e;return(0,t.jsx)("h3",{className:"text-2xl font-bold bg-gradient-to-r from-amber-700 to-yellow-600 bg-clip-text text-transparent mb-3",children:r})},p:e=>{let{children:r}=e;return(0,t.jsx)("p",{className:"text-gray-800 leading-relaxed mb-6 text-lg",children:r})},blockquote:e=>{let{children:r}=e;return(0,t.jsx)("blockquote",{className:"border-l-4 border-gradient-to-b from-orange-400 to-red-400 pl-6 py-4 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-900 italic my-6 rounded-r-lg shadow-sm",children:r})},ul:e=>{let{children:r}=e;return(0,t.jsx)("ul",{className:"list-disc list-inside text-gray-800 mb-6 space-y-2 text-lg",children:r})},ol:e=>{let{children:r}=e;return(0,t.jsx)("ol",{className:"list-decimal list-inside text-gray-800 mb-6 space-y-2 text-lg",children:r})},li:e=>{let{children:r}=e;return(0,t.jsx)("li",{className:"ml-2",children:r})},a:e=>{let{children:r,href:o}=e;return(0,t.jsx)("a",{href:o,className:"text-orange-600 hover:text-red-600 underline decoration-orange-300 hover:decoration-red-400 transition-all duration-300 font-medium",target:"_blank",rel:"noopener noreferrer",children:r})},strong:e=>{let{children:r}=e;return(0,t.jsx)("strong",{className:"font-bold bg-gradient-to-r from-red-700 to-orange-600 bg-clip-text text-transparent",children:r})},em:e=>{let{children:r}=e;return(0,t.jsx)("em",{className:"italic text-orange-800 font-medium",children:r})},table:e=>{let{children:r}=e;return(0,t.jsx)("div",{className:"overflow-x-auto my-6",children:(0,t.jsx)("table",{className:"min-w-full border border-orange-200 rounded-lg overflow-hidden shadow-sm",children:r})})},thead:e=>{let{children:r}=e;return(0,t.jsx)("thead",{className:"bg-gradient-to-r from-orange-100 to-amber-100",children:r})},th:e=>{let{children:r}=e;return(0,t.jsx)("th",{className:"px-4 py-3 text-left font-semibold text-orange-900 border-b border-orange-200",children:r})},td:e=>{let{children:r}=e;return(0,t.jsx)("td",{className:"px-4 py-3 text-gray-800 border-b border-orange-100",children:r})}},children:r})})}):(0,t.jsxs)("div",{className:"flex flex-col h-full bg-gradient-to-br from-amber-50/30 to-orange-50/30",children:[(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2 p-6 bg-gradient-to-r from-amber-50 via-orange-50 to-red-50 border-b border-orange-200 shadow-sm",children:[(0,t.jsx)("div",{className:"flex flex-wrap items-center gap-1",children:E.map((e,r)=>(0,t.jsx)(n.P.button,{whileHover:{scale:1.05,y:-1},whileTap:{scale:.95},onClick:e.action,className:"p-3 rounded-xl transition-all duration-300 ".concat(e.color," ").concat(e.bgColor," shadow-sm hover:shadow-md border border-transparent hover:border-orange-200"),title:e.label,children:(0,t.jsx)(e.icon,{className:"h-4 w-4"})},r))}),(0,t.jsx)("div",{className:"w-px h-8 bg-gradient-to-b from-orange-300 to-red-300 mx-3"}),(0,t.jsxs)("div",{className:"flex items-center gap-3 text-sm font-medium",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-orange-700 bg-white/60 px-3 py-2 rounded-lg border border-orange-200",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Words: ",M.toLocaleString()]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-amber-700 bg-white/60 px-3 py-2 rounded-lg border border-amber-200",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Characters: ",r.length.toLocaleString()]})]})]})]}),(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)("textarea",{ref:k,value:r,onChange:e=>o(e.target.value),className:"w-full h-full min-h-[500px] p-8 bg-gradient-to-br from-amber-50/20 via-orange-50/20 to-red-50/10 border-none outline-none resize-none text-gray-800 leading-relaxed text-base placeholder-orange-400",placeholder:"Start writing your article in Markdown... Use the toolbar above for quick formatting!",style:{fontFamily:'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',lineHeight:"1.8"}}),(0,t.jsxs)(n.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"absolute bottom-6 right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-orange-200 text-xs text-orange-700 max-w-sm",children:[(0,t.jsxs)("div",{className:"font-bold mb-3 text-orange-900 flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"Markdown Quick Reference"]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"# H1"}),(0,t.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium",children:"## H2"}),(0,t.jsx)("code",{className:"bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium",children:"### H3"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"**bold**"}),(0,t.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium",children:"*italic*"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium",children:"- list"}),(0,t.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"1. numbered"})]}),(0,t.jsx)("div",{children:(0,t.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium",children:"[link](url)"})}),(0,t.jsx)("div",{children:(0,t.jsx)("code",{className:"bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium",children:"`inline code`"})}),(0,t.jsx)("div",{children:(0,t.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"> blockquote"})})]})]})]})]})}}}]);