(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[681],{22958:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(95155),o=s(60760),a=s(1978),i=s(40646),n=s(54861),l=s(1243),c=s(81284),d=s(54416);function h(e){let{type:t,title:s,message:h,show:m,onClose:p,autoClose:u=!1,duration:x=5e3,icon:g}=e,b={success:(0,r.jsx)(i.A,{className:"w-5 h-5"}),error:(0,r.jsx)(n.A,{className:"w-5 h-5"}),warning:(0,r.jsx)(l.A,{className:"w-5 h-5"}),info:(0,r.jsx)(c.A,{className:"w-5 h-5"})};return u&&m&&p&&setTimeout(()=>{p()},x),(0,r.jsx)(o.N,{children:m&&(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3},className:"alert-modern ".concat({success:"alert-modern-success",error:"alert-modern-error",warning:"alert-modern-warning",info:"alert-modern-info"}[t]),children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:g||b[t]}),(0,r.jsxs)("div",{className:"flex-1",children:[s&&(0,r.jsx)("h4",{className:"font-semibold mb-1",children:s}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:h})]}),p&&(0,r.jsx)("button",{onClick:p,className:"flex-shrink-0 ml-4 p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})})]})})}},42637:()=>{},52779:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var r=s(95155),o=s(12115),a=s(35695),i=s(1978),n=s(35169),l=s(57100),c=s(24357),d=s(91788),h=s(4229),m=s(57434),p=s(96108),u=s(75109);s(42637);var x=s(58292),g=s(74979),b=s(33083),w=s(36761),f=s(4652),j=s(9727),v=s(19144),N=s(17240),y=s(28440),A=s(22705),k=s(92406),C=s(15968),L=s(89140),_=s(40224),M=s(29621),S=s(38164),E=s(93654),T=s(48932),P=s(16785),$=s(92657),H=s(72713),q=s(15448),I=s(14186),O=s(40646),R=s(85339),U=s(54861),B=s(33109),D=s(19910);function F(e){let{content:t,targetKeyword:s,metaDescription:a,title:n,wordCount:l}=e,[c,d]=(0,o.useState)(0),[h,p]=(0,o.useState)([]),u=e=>{let t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""},x=(e,t)=>{if(!t||!e)return 0;let s=e.toLowerCase().split(/\s+/),r=t.toLowerCase().split(/\s+/),o=0;for(let e=0;e<=s.length-r.length;e++)s.slice(e,e+r.length).join(" ")===t.toLowerCase()&&o++;return o/s.length*100},g=e=>{let t=document.createElement("div");return t.innerHTML=e,Array.from(t.querySelectorAll("h1, h2, h3, h4, h5, h6")).map(e=>({level:parseInt(e.tagName.charAt(1)),text:e.textContent||""}))},b=e=>{if(!e)return 0;let t=e.split(/[.!?]+/).filter(e=>e.trim().length>0),s=e.split(/\s+/).filter(e=>e.length>0),r=s.reduce((e,t)=>e+Math.max(1,t.toLowerCase().replace(/[^aeiou]/g,"").length),0);if(0===t.length||0===s.length)return 0;let o=s.length/t.length;return Math.max(0,Math.min(100,206.835-1.015*o-84.6*(r/s.length)))};(0,o.useEffect)(()=>{let e=u(t),o=x(e,s),i=g(t),c=b(e),h=[{id:"title-length",label:"Title Length",status:n.length>=30&&n.length<=60?"good":n.length>0?"warning":"error",score:n.length>=30&&n.length<=60?100:50*(n.length>0),message:n.length>=30&&n.length<=60?"Perfect title length (".concat(n.length," characters)"):n.length>60?"Title too long (".concat(n.length," characters). Keep it under 60."):n.length>0?"Title too short (".concat(n.length," characters). Aim for 30-60."):"Add a title for your content",icon:(0,r.jsx)(m.A,{className:"w-4 h-4"})},{id:"keyword-in-title",label:"Keyword in Title",status:s&&n.toLowerCase().includes(s.toLowerCase())?"good":"warning",score:s&&n.toLowerCase().includes(s.toLowerCase())?100:0,message:s&&n.toLowerCase().includes(s.toLowerCase())?"Target keyword found in title":s?"Include your target keyword in the title":"Set a target keyword first",icon:(0,r.jsx)(P.A,{className:"w-4 h-4"})},{id:"meta-description",label:"Meta Description",status:a.length>=120&&a.length<=160?"good":a.length>0?"warning":"error",score:a.length>=120&&a.length<=160?100:50*(a.length>0),message:a.length>=120&&a.length<=160?"Perfect meta description length (".concat(a.length," characters)"):a.length>160?"Meta description too long (".concat(a.length," characters)"):a.length>0?"Meta description too short (".concat(a.length," characters)"):"Add a meta description (120-160 characters)",icon:(0,r.jsx)($.A,{className:"w-4 h-4"})},{id:"word-count",label:"Content Length",status:l>=300?"good":l>=150?"warning":"error",score:l>=300?100:l>=150?60:20,message:l>=300?"Good content length (".concat(l," words)"):l>=150?"Content is short (".concat(l," words). Aim for 300+ words."):"Content too short (".concat(l," words). Add more content."),icon:(0,r.jsx)(H.A,{className:"w-4 h-4"})},{id:"keyword-density",label:"Keyword Density",status:o>=.5&&o<=2.5?"good":o>0?"warning":"error",score:o>=.5&&o<=2.5?100:50*(o>0),message:o>=.5&&o<=2.5?"Good keyword density (".concat(o.toFixed(1),"%)"):o>2.5?"Keyword density too high (".concat(o.toFixed(1),"%). Reduce usage."):o>0?"Keyword density low (".concat(o.toFixed(1),"%). Use keyword more."):s?"Target keyword not found in content":"Set a target keyword first",icon:(0,r.jsx)(q.A,{className:"w-4 h-4"})},{id:"headings",label:"Heading Structure",status:i.length>=2?"good":i.length>=1?"warning":"error",score:i.length>=2?100:60*(i.length>=1),message:i.length>=2?"Good heading structure (".concat(i.length," headings)"):i.length>=1?"Add more headings to improve structure":"Add headings to structure your content",icon:(0,r.jsx)(m.A,{className:"w-4 h-4"})},{id:"readability",label:"Readability",status:c>=60?"good":c>=30?"warning":"error",score:Math.round(c),message:c>=60?"Good readability score (".concat(Math.round(c),")"):c>=30?"Fair readability (".concat(Math.round(c),"). Simplify sentences."):"Poor readability (".concat(Math.round(c),"). Use shorter sentences."),icon:(0,r.jsx)(I.A,{className:"w-4 h-4"})}];p(h),d(Math.round(h.reduce((e,t)=>e+t.score,0)/(100*h.length)*100))},[t,s,a,n,l]);let w=e=>e>=80?"text-green-400":e>=60?"text-yellow-400":"text-red-400",f=e=>e>=80?"from-green-500 to-emerald-500":e>=60?"from-yellow-500 to-orange-500":"from-red-500 to-pink-500",j=e=>{switch(e){case"good":return(0,r.jsx)(O.A,{className:"w-4 h-4 text-green-400"});case"warning":return(0,r.jsx)(R.A,{className:"w-4 h-4 text-yellow-400"});case"error":return(0,r.jsx)(U.A,{className:"w-4 h-4 text-red-400"});default:return null}};return(0,r.jsxs)(D.A,{variant:"glass",className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-24 h-24 mx-auto mb-4",children:[(0,r.jsxs)("svg",{className:"w-24 h-24 transform -rotate-90",viewBox:"0 0 100 100",children:[(0,r.jsx)("circle",{cx:"50",cy:"50",r:"40",stroke:"rgba(255, 255, 255, 0.1)",strokeWidth:"8",fill:"none"}),(0,r.jsx)(i.P.circle,{cx:"50",cy:"50",r:"40",stroke:"url(#gradient)",strokeWidth:"8",fill:"none",strokeLinecap:"round",strokeDasharray:"".concat(2*Math.PI*40),initial:{strokeDashoffset:2*Math.PI*40},animate:{strokeDashoffset:2*Math.PI*40*(1-c/100)},transition:{duration:1,ease:"easeOut"}}),(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,r.jsx)("stop",{offset:"0%",className:"stop-color-".concat(f(c).split(" ")[0].replace("from-",""))}),(0,r.jsx)("stop",{offset:"100%",className:"stop-color-".concat(f(c).split(" ")[1].replace("to-",""))})]})})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-2xl font-bold ".concat(w(c)),children:c})})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-1",children:"SEO Score"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:c>=80?"Excellent":c>=60?"Good":"Needs Improvement"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-white/80 uppercase tracking-wider",children:"SEO Analysis"}),h.map((e,t)=>(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"flex items-start gap-3 p-3 rounded-xl bg-white/5 hover:bg-white/10 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.icon,j(e.status)]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:e.label}),(0,r.jsxs)("span",{className:"text-xs font-medium ".concat(w(e.score)),children:[e.score,"%"]})]}),(0,r.jsx)("p",{className:"text-xs text-white/60 leading-relaxed",children:e.message})]})]},e.id))]}),(0,r.jsxs)("div",{className:"pt-4 border-t border-white/10",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-white/80 mb-3",children:"Quick Tips"}),(0,r.jsxs)("div",{className:"space-y-2 text-xs text-white/60",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(B.A,{className:"w-3 h-3 text-blue-400"}),(0,r.jsx)("span",{children:"Use your target keyword naturally throughout the content"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-3 h-3 text-purple-400"}),(0,r.jsx)("span",{children:"Structure content with clear headings (H1, H2, H3)"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)($.A,{className:"w-3 h-3 text-green-400"}),(0,r.jsx)("span",{children:"Write compelling meta descriptions to improve CTR"})]})]})]})]})}function G(e){if(!e)return"";try{let t=e,s=[];t=t.replace(/```([\s\S]*?)```/g,(e,t)=>{let r=s.length;return s.push("<pre><code>".concat(t.trim(),"</code></pre>")),"__CODE_BLOCK_".concat(r,"__")});let r=[],o=(t=(t=(t=(t=(t=t.replace(/`([^`]+)`/g,(e,t)=>{let s=r.length;return r.push("<code>".concat(t,"</code>")),"__INLINE_CODE_".concat(s,"__")})).replace(/^#### (.*$)/gim,"<h4>$1</h4>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>")).replace(/\*\*\*(.*?)\*\*\*/g,"<strong><em>$1</em></strong>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/__(.*?)__/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/_(.*?)_/g,"<em>$1</em>")).replace(/!\[([^\]]*)\]\(([^)]+)\)/g,'<img src="$2" alt="$1" />').replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')).replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>")).split("\n"),a=[],i=!1,n="";for(let e=0;e<o.length;e++){let t=o[e].trim();t.match(/^[-*+] /)?(i&&"ul"===n||(i&&a.push("</".concat(n,">")),a.push("<ul>"),i=!0,n="ul"),a.push("<li>".concat(t.replace(/^[-*+] /,""),"</li>"))):t.match(/^\d+\. /)?(i&&"ol"===n||(i&&a.push("</".concat(n,">")),a.push("<ol>"),i=!0,n="ol"),a.push("<li>".concat(t.replace(/^\d+\. /,""),"</li>"))):(i&&(a.push("</".concat(n,">")),i=!1,n=""),a.push(t))}i&&a.push("</".concat(n,">"));let l=(t=a.join("\n")).split("\n"),c=[],d=!1;for(let e=0;e<l.length;e++){let t=l[e].trim();if(!t){d&&(c.push("</p>"),d=!1);continue}t.match(/^<(h[1-6]|ul|ol|li|blockquote|\/ul|\/ol|\/blockquote)/)?d&&(c.push("</p>"),d=!1):d||(c.push("<p>"),d=!0),c.push(t)}return d&&c.push("</p>"),t=(t=c.join("\n")).replace(/<p><\/p>/g,"").replace(/\n+/g,"\n").replace(/\n/g," ").replace(/<\/p>\s*<p>/g,"</p>\n<p>").replace(/<\/(h[1-6]|ul|ol|blockquote)>\s*<p>/g,"</$1>\n<p>").replace(/<\/(h[1-6]|ul|ol|blockquote)>/g,"</$1>\n").replace(/<(h[1-6]|ul|ol|blockquote)/g,"\n<$1").trim(),s.forEach((e,s)=>{t=t.replace("__CODE_BLOCK_".concat(s,"__"),e)}),r.forEach((e,s)=>{t=t.replace("__INLINE_CODE_".concat(s,"__"),e)}),t}catch(t){return console.error("Error converting markdown to HTML:",t),e}}function z(e){return!!e&&[/^#{1,6}\s+/m,/\*\*.*?\*\*/,/\*.*?\*/,/^\s*[-*+]\s+/m,/^\s*\d+\.\s+/m,/```[\s\S]*?```/,/`.*?`/,/^\s*>\s+/m,/\[.*?\]\(.*?\)/,/!\[.*?\]\(.*?\)/].some(t=>t.test(e))}let K={base:"max-w-none",enhanced:"prose-headings:font-bold prose-headings:text-inherit prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-p:text-white/90 prose-p:leading-relaxed prose-p:mb-6 prose-p:text-left prose-p:tracking-wide prose-strong:text-white prose-strong:font-semibold prose-em:text-orange-200 prose-code:text-yellow-200 prose-code:bg-orange-500/15 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:border prose-code:border-orange-500/30 prose-blockquote:border-l-4 prose-blockquote:border-blue-400 prose-blockquote:text-blue-100 prose-blockquote:bg-blue-500/10 prose-blockquote:pl-4 prose-blockquote:py-2 prose-a:text-orange-200 prose-a:no-underline hover:prose-a:text-orange-300 hover:prose-a:underline prose-ul:text-white/90 prose-ul:my-4 prose-ul:space-y-0 prose-ol:text-white/90 prose-ol:my-4 prose-ol:space-y-0 prose-li:text-white/90 prose-li:my-0 prose-li:leading-relaxed prose-li:mb-1 prose-pre:bg-gray-900/50 prose-pre:border prose-pre:border-white/20"};function W(e){let{content:t="",onChange:s,placeholder:a="Start writing your content...",className:n="",showSEOMeter:l=!0,targetKeyword:c="",metaDescription:d="",title:h=""}=e,[m,p]=(0,o.useState)([]),P=(0,u.hG)({extensions:[x.A.configure({bulletList:{keepMarks:!0,keepAttributes:!1,HTMLAttributes:{class:"tight-list"}},orderedList:{keepMarks:!0,keepAttributes:!1,HTMLAttributes:{class:"tight-list"}},listItem:{HTMLAttributes:{class:"tight-list-item"}}}),g.A.configure({placeholder:a}),b.A.configure({limit:1e4}),w.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-400 hover:text-blue-300 underline"}}),f.Ay.configure({multicolor:!0})],content:z(t)?G(t):t,onUpdate:e=>{let{editor:t}=e,r=t.getHTML();null==s||s(r);let o=[];t.isActive("bold")&&o.push("bold"),t.isActive("italic")&&o.push("italic"),t.isActive("underline")&&o.push("underline"),t.isActive("bulletList")&&o.push("bulletList"),t.isActive("orderedList")&&o.push("orderedList"),t.isActive("blockquote")&&o.push("blockquote"),t.isActive("code")&&o.push("code"),t.isActive("link")&&o.push("link"),t.isActive("heading",{level:1})&&o.push("h1"),t.isActive("heading",{level:2})&&o.push("h2"),t.isActive("heading",{level:3})&&o.push("h3"),p(o)},editorProps:{attributes:{class:"".concat(function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return e?"".concat(K.base," ").concat(K.enhanced):K.enhanced}()," focus:outline-none vibrant-editor-theme"),"data-tiptap-editor":"true","data-editor-theme":"vibrant-red-orange"}}});if((0,o.useEffect)(()=>{if(P&&t!==P.getHTML()){let e=z(t)?G(t):t;P.commands.setContent(e)}},[t,P]),(0,o.useEffect)(()=>{if(P){let e=P.view.dom;if(e){e.classList.add("vibrant-editor-theme");let t=document.createElement("style");t.textContent='\n          div[data-tiptap-editor="true"] .ProseMirror h1,\n          .vibrant-editor-theme h1 {\n            color: rgb(252, 165, 165) !important;\n            text-shadow: 0 0 10px rgba(252, 165, 165, 0.3) !important;\n            font-size: 2rem !important;\n            font-weight: 700 !important;\n          }\n          div[data-tiptap-editor="true"] .ProseMirror h2,\n          .vibrant-editor-theme h2 {\n            color: rgb(253, 186, 116) !important;\n            text-shadow: 0 0 8px rgba(253, 186, 116, 0.3) !important;\n            font-size: 1.75rem !important;\n            font-weight: 600 !important;\n          }\n          div[data-tiptap-editor="true"] .ProseMirror h3,\n          .vibrant-editor-theme h3 {\n            color: rgb(248, 113, 113) !important;\n            text-shadow: 0 0 6px rgba(248, 113, 113, 0.3) !important;\n            font-size: 1.5rem !important;\n            font-weight: 600 !important;\n          }\n          div[data-tiptap-editor="true"] .ProseMirror h4,\n          .vibrant-editor-theme h4 {\n            color: rgb(251, 146, 60) !important;\n            text-shadow: 0 0 4px rgba(251, 146, 60, 0.3) !important;\n            font-size: 1.25rem !important;\n            font-weight: 600 !important;\n          }\n          div[data-tiptap-editor="true"] .ProseMirror h5,\n          .vibrant-editor-theme h5 {\n            color: rgb(239, 68, 68) !important;\n            text-shadow: 0 0 4px rgba(239, 68, 68, 0.3) !important;\n            font-size: 1.125rem !important;\n            font-weight: 600 !important;\n          }\n          div[data-tiptap-editor="true"] .ProseMirror h6,\n          .vibrant-editor-theme h6 {\n            color: rgb(249, 115, 22) !important;\n            text-shadow: 0 0 4px rgba(249, 115, 22, 0.3) !important;\n            font-size: 1rem !important;\n            font-weight: 600 !important;\n          }\n          .vibrant-editor-theme strong { color: rgb(255, 255, 255) !important; text-shadow: 0 0 2px rgba(255, 255, 255, 0.5) !important; }\n          .vibrant-editor-theme em { color: rgb(254, 215, 170) !important; }\n          .vibrant-editor-theme code { color: rgb(254, 240, 138) !important; background-color: rgba(249, 115, 22, 0.15) !important; border: 1px solid rgba(249, 115, 22, 0.3) !important; }\n          .vibrant-editor-theme a { color: rgb(254, 215, 170) !important; border-bottom: 1px solid rgba(254, 215, 170, 0.3) !important; }\n          .vibrant-editor-theme a:hover { color: rgb(253, 186, 116) !important; border-bottom: 1px solid rgb(253, 186, 116) !important; text-shadow: 0 0 4px rgba(253, 186, 116, 0.4) !important; }\n        ',document.head.querySelector("#vibrant-editor-styles")||(t.id="vibrant-editor-styles",document.head.appendChild(t))}}},[P]),!P)return(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-12 bg-white/10 rounded-xl mb-4"}),(0,r.jsx)("div",{className:"h-96 bg-white/5 rounded-2xl"})]});let $=e=>{let{onClick:t,isActive:s,disabled:o=!1,children:a,title:n}=e;return(0,r.jsx)(i.P.button,{whileHover:o?{}:{scale:1.05},whileTap:o?{}:{scale:.95},onClick:t,title:n,disabled:o,className:"\n        toolbar-button\n        ".concat(s?"is-active":"","\n      "),children:a})},H=P.storage.characterCount.words(),q=P.storage.characterCount.characters();return(0,r.jsxs)("div",{className:"space-y-6 ".concat(n),children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-gradient-to-r from-white/10 via-white/5 to-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-white/10",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-400"}),(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-400"}),(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-400"}),(0,r.jsx)("span",{className:"text-white/70 text-sm font-medium ml-4",children:"Content Editor"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"text-white/60 text-sm",children:[P.storage.characterCount.characters()," characters"]}),(0,r.jsx)("div",{className:"w-px h-4 bg-white/20"}),(0,r.jsxs)("div",{className:"text-white/60 text-sm",children:[P.storage.characterCount.words()," words"]})]})]}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3 flex-wrap",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleBold().run(),isActive:m.includes("bold"),title:"Bold",children:(0,r.jsx)(j.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleItalic().run(),isActive:m.includes("italic"),title:"Italic",children:(0,r.jsx)(v.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleStrike().run(),isActive:P.isActive("strike"),title:"Strikethrough",children:(0,r.jsx)(N.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleHeading({level:1}).run(),isActive:m.includes("h1"),title:"Heading 1",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleHeading({level:2}).run(),isActive:m.includes("h2"),title:"Heading 2",children:(0,r.jsx)(A.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleHeading({level:3}).run(),isActive:m.includes("h3"),title:"Heading 3",children:(0,r.jsx)(k.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleBulletList().run(),isActive:m.includes("bulletList"),title:"Bullet List",children:(0,r.jsx)(C.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleOrderedList().run(),isActive:m.includes("orderedList"),title:"Numbered List",children:(0,r.jsx)(L.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleBlockquote().run(),isActive:m.includes("blockquote"),title:"Quote",children:(0,r.jsx)(_.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().toggleCode().run(),isActive:m.includes("code"),title:"Inline Code",children:(0,r.jsx)(M.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>{let e=window.prompt("Enter URL:");e&&P.chain().focus().setLink({href:e}).run()},isActive:m.includes("link"),title:"Add Link",children:(0,r.jsx)(S.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,r.jsx)($,{onClick:()=>P.chain().focus().undo().run(),disabled:!P.can().undo(),title:"Undo",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})}),(0,r.jsx)($,{onClick:()=>P.chain().focus().redo().run(),disabled:!P.can().redo(),title:"Redo",children:(0,r.jsx)(T.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"flex-1"}),(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsxs)("div",{className:"text-sm text-white/60",children:[H," words • ",q," characters"]})})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"".concat(l?"lg:col-span-2":"lg:col-span-3"),children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white/10 via-white/5 to-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 border-b border-white/10",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-blue-400 animate-pulse"}),(0,r.jsx)("span",{className:"text-white/70 text-sm font-medium",children:"Edit Mode"})]}),(0,r.jsx)("div",{className:"text-white/50 text-xs",children:"Auto-save enabled"})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("div",{className:"relative","data-tiptap-editor":"true",children:[(0,r.jsx)(u.$Z,{editor:P}),(0,r.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/50 backdrop-blur-lg rounded-lg px-3 py-1 text-xs text-white/70",children:[H," words"]})]})})]})}),l&&(0,r.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"lg:col-span-1",children:(0,r.jsx)(F,{content:P.getHTML(),targetKeyword:c,metaDescription:d,title:h,wordCount:H})})]})]})}var Q=s(39636),Z=s(22958);function J(){let e=(0,a.useRouter)(),t=(0,a.useSearchParams)(),[s,u]=(0,o.useState)(""),[x,g]=(0,o.useState)(""),[b,w]=(0,o.useState)(""),[f,j]=(0,o.useState)(""),[v,N]=(0,o.useState)(""),[y,A]=(0,o.useState)(!1),[k,C]=(0,o.useState)(""),[L,_]=(0,o.useState)("");(0,o.useEffect)(()=>{let e=t.get("content"),s=t.get("title"),r=t.get("keyword"),o=t.get("competitors"),a=t.get("audience");if(e)try{let t=decodeURIComponent(atob(e));u(t),console.log("Content loaded successfully")}catch(e){console.error("Failed to decode content:",e),_("Failed to load content. Please try generating new content.")}if(s)try{let e=decodeURIComponent(atob(s));g(e),console.log("Title loaded:",e)}catch(e){console.error("Failed to decode title:",e)}if(r)try{let e=decodeURIComponent(atob(r));w(e),console.log("Keyword loaded:",e)}catch(e){console.error("Failed to decode keyword:",e)}if(o)try{let e=decodeURIComponent(atob(o));j(e),console.log("Competitors loaded:",e)}catch(e){console.error("Failed to decode competitors:",e)}if(a)try{let e=decodeURIComponent(atob(a));N(e),console.log("Target audience loaded:",e)}catch(e){console.error("Failed to decode target audience:",e)}},[t]);let M=async()=>{A(!0),C("");try{await new Promise(e=>setTimeout(e,1e3)),C("Content saved successfully!"),setTimeout(()=>C(""),3e3)}catch(e){_("Failed to save content")}finally{A(!1)}},S=async()=>{try{await navigator.clipboard.writeText(s),C("Content copied to clipboard!"),setTimeout(()=>C(""),3e3)}catch(e){_("Failed to copy content")}},E=()=>{e.push("/blog")};return(0,r.jsx)(p.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)(i.P.div,{className:"mb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(Q.A,{variant:"secondary",size:"sm",onClick:E,icon:(0,r.jsx)(n.A,{className:"w-4 h-4"}),children:"Back to Generator"}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600",children:(0,r.jsx)(l.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Blog Editor"}),(0,r.jsx)("p",{className:"text-white/70",children:"Edit and refine your generated content"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(Q.A,{variant:"secondary",size:"sm",onClick:S,icon:(0,r.jsx)(c.A,{className:"w-4 h-4"}),children:"Copy"}),(0,r.jsx)(Q.A,{variant:"secondary",size:"sm",onClick:()=>{let e=new Blob([s],{type:"text/markdown"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download="blog-post-".concat(x.replace(/\s+/g,"-").toLowerCase()||"untitled",".md"),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t)},icon:(0,r.jsx)(d.A,{className:"w-4 h-4"}),children:"Download"}),(0,r.jsx)(Q.A,{variant:"primary",size:"sm",onClick:M,loading:y,icon:(0,r.jsx)(h.A,{className:"w-4 h-4"}),children:y?"Saving...":"Save"})]})]}),(0,r.jsx)(Z.A,{type:"error",message:L,show:!!L,onClose:()=>_(""),autoClose:!0}),k&&(0,r.jsx)(Z.A,{type:"success",message:k,show:!!k,onClose:()=>C(""),autoClose:!0})]}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:s?(0,r.jsx)(W,{content:s,onChange:u,placeholder:"Start writing your blog post...",showSEOMeter:!0,targetKeyword:b,metaDescription:"",title:x}):(0,r.jsxs)(D.A,{variant:"glass",className:"text-center py-16",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-white/40 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white/80 mb-2",children:"No content to edit"}),(0,r.jsx)("p",{className:"text-white/60 mb-6",children:"Generate content from the blog generator to start editing"}),(0,r.jsx)(Q.A,{variant:"primary",onClick:E,icon:(0,r.jsx)(n.A,{className:"w-4 h-4"}),children:"Go to Blog Generator"})]})})]})})}function V(){return(0,r.jsx)(o.Suspense,{fallback:(0,r.jsx)(p.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-white/70",children:"Loading editor..."})]})})})}),children:(0,r.jsx)(J,{})})}},76370:(e,t,s)=>{Promise.resolve().then(s.bind(s,52779))}},e=>{var t=t=>e(e.s=t);e.O(0,[242,4,277,322,309,503,595,483,441,684,358],()=>t(76370)),_N_E=e.O()}]);