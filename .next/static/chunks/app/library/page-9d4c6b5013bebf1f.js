(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[382],{13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15968:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},24357:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},24744:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(95155),i=s(12115),l=s(1978),r=s(19946);let c=(0,r.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var n=s(92657),d=s(51976),o=s(13717),h=s(24357),x=s(91788);let p=(0,r.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var u=s(15968),m=s(47924),g=s(57434),y=s(19910),w=s(39636),j=s(96108);let v=[{id:1,title:"The Future of AI in Healthcare",type:"Blog Post",status:"Published",createdAt:"2024-01-15",views:5200,engagement:12.3,tags:["AI","Healthcare","Technology"],thumbnail:"/api/placeholder/300/200"},{id:2,title:"Welcome to Our Newsletter",type:"Email",status:"Sent",createdAt:"2024-01-14",views:2100,engagement:15.7,tags:["Newsletter","Welcome"],thumbnail:"/api/placeholder/300/200"},{id:3,title:"Top 10 AI Tools for Content Creation",type:"Blog Post",status:"Draft",createdAt:"2024-01-13",views:0,engagement:0,tags:["AI","Tools","Content"],thumbnail:"/api/placeholder/300/200"},{id:4,title:"Quick Tips for Better Writing",type:"Tweet Thread",status:"Published",createdAt:"2024-01-12",views:1900,engagement:7.4,tags:["Writing","Tips"],thumbnail:"/api/placeholder/300/200"},{id:5,title:"How to Create Viral YouTube Content",type:"YouTube Script",status:"Published",createdAt:"2024-01-11",views:3400,engagement:9.8,tags:["YouTube","Viral","Content"],thumbnail:"/api/placeholder/300/200"},{id:6,title:"Email Marketing Best Practices",type:"Email",status:"Scheduled",createdAt:"2024-01-10",views:0,engagement:0,tags:["Email","Marketing"],thumbnail:"/api/placeholder/300/200"}],b=["All","Blog Post","Email","Tweet Thread","YouTube Script"],f=["All","Published","Draft","Sent","Scheduled"];function N(){let[e,t]=(0,i.useState)(!1),[s,r]=(0,i.useState)("grid"),[N,A]=(0,i.useState)(""),[k,M]=(0,i.useState)("All"),[C,S]=(0,i.useState)("All"),[P,T]=(0,i.useState)(v);if((0,i.useEffect)(()=>{t(!0)},[]),(0,i.useEffect)(()=>{let e=v;N&&(e=e.filter(e=>e.title.toLowerCase().includes(N.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(N.toLowerCase())))),"All"!==k&&(e=e.filter(e=>e.type===k)),"All"!==C&&(e=e.filter(e=>e.status===C)),T(e)},[N,k,C]),!e)return null;let z=e=>{switch(e){case"Published":return"text-green-400 bg-green-500/20";case"Draft":return"text-yellow-400 bg-yellow-500/20";case"Sent":return"text-blue-400 bg-blue-500/20";case"Scheduled":return"text-purple-400 bg-purple-500/20";default:return"text-gray-400 bg-gray-500/20"}},E=e=>{let{item:t}=e;return(0,a.jsx)(y.A,{variant:"glass",className:"p-6 group cursor-pointer",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-blue-400 transition-colors",children:t.title}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsx)("span",{className:"text-white/60 text-sm",children:t.type}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(z(t.status)),children:t.status})]})]}),(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors opacity-0 group-hover:opacity-100",children:(0,a.jsx)(c,{className:"w-4 h-4 text-white/70"})})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-white/10 text-white/70 text-xs rounded-lg",children:e},e))}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-white/10",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-white/60",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),t.views.toLocaleString()]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),t.engagement,"%"]})]}),(0,a.jsx)("span",{className:"text-white/50 text-sm",children:t.createdAt})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)(w.A,{variant:"secondary",size:"sm",icon:(0,a.jsx)(o.A,{className:"w-3 h-3"}),children:"Edit"}),(0,a.jsx)(w.A,{variant:"secondary",size:"sm",icon:(0,a.jsx)(h.A,{className:"w-3 h-3"}),children:"Copy"}),(0,a.jsx)(w.A,{variant:"secondary",size:"sm",icon:(0,a.jsx)(x.A,{className:"w-3 h-3"}),children:"Export"})]})]})})},L=e=>{let{item:t}=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors group",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-white font-medium group-hover:text-blue-400 transition-colors",children:t.title}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-1",children:[(0,a.jsx)("span",{className:"text-white/60 text-sm",children:t.type}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(z(t.status)),children:t.status}),(0,a.jsx)("span",{className:"text-white/50 text-sm",children:t.createdAt})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-white/60",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),t.views.toLocaleString()]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),t.engagement,"%"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(o.A,{className:"w-4 h-4 text-white/70"})}),(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(h.A,{className:"w-4 h-4 text-white/70"})}),(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(c,{className:"w-4 h-4 text-white/70"})})]})]})]})};return(0,a.jsx)(j.A,{children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Content Library"}),(0,a.jsx)("p",{className:"text-white/70",children:"Manage and organize all your generated content"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 p-1 bg-white/10 rounded-xl",children:[(0,a.jsx)("button",{onClick:()=>r("grid"),className:"p-2 rounded-lg transition-colors ".concat("grid"===s?"bg-white/20 text-white":"text-white/60 hover:text-white"),children:(0,a.jsx)(p,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>r("list"),className:"p-2 rounded-lg transition-colors ".concat("list"===s?"bg-white/20 text-white":"text-white/60 hover:text-white"),children:(0,a.jsx)(u.A,{className:"w-4 h-4"})})]})})]}),(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50"}),(0,a.jsx)("input",{type:"text",placeholder:"Search content...",value:N,onChange:e=>A(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-400"})]}),(0,a.jsx)("select",{value:k,onChange:e=>M(e.target.value),className:"px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-400",children:b.map(e=>(0,a.jsx)("option",{value:e,children:e},e))}),(0,a.jsx)("select",{value:C,onChange:e=>S(e.target.value),className:"px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-400",children:f.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:"grid"===s?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:P.map((e,t)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},children:(0,a.jsx)(E,{item:e})},e.id))}):(0,a.jsx)("div",{className:"space-y-4",children:P.map((e,t)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},children:(0,a.jsx)(L,{item:e})},e.id))})}),0===P.length&&(0,a.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[(0,a.jsx)(g.A,{className:"w-16 h-16 text-white/40 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white/80 mb-2",children:"No content found"}),(0,a.jsx)("p",{className:"text-white/60",children:"Try adjusting your search or filters"})]})]})})}},35441:(e,t,s)=>{Promise.resolve().then(s.bind(s,24744))},51976:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[322,309,503,483,441,684,358],()=>t(35441)),_N_E=e.O()}]);