(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[555],{9158:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>es});var s=a(95155),l=a(12115),i=a(1978),r=a(60760),n=a(49376),o=a(47924),c=a(72713),d=a(34869),h=a(57434),m=a(53311),x=a(16785),u=a(75525),b=a(70463),p=a(381),g=a(94498),v=a(47863),w=a(66474),j=a(71539),y=a(73314),f=a(74575),N=a(59964),k=a(38564),C=a(40646),A=a(85339),S=a(54213),P=a(14186),T=a(24357),E=a(91788),F=a(9727),M=a(19144),R=a(17240),L=a(32643),O=a(87325),H=a(67733),q=a(88702),z=a(15968),I=a(89140),W=a(40224),B=a(29621),D=a(93654),U=a(48932),_=a(28440),Q=a(22705),V=a(92406),G=a(33127),K=a(13717),J=a(92657),X=a(43900),Y=a(57918),Z=a(74347),$=a(38164),ee=a(93500),et=a(4229);function ea(e){let{content:t,onChange:a,onSEOAnalysis:n,className:c}=e,[d,m]=(0,l.useState)(t),[x,u]=(0,l.useState)(!1),[b,p]=(0,l.useState)(!1),[g,v]=(0,l.useState)(""),[w,y]=(0,l.useState)(!1),[f,N]=(0,l.useState)(!1),[k,C]=(0,l.useState)(null),[A,S]=(0,l.useState)(0),[P,E]=(0,l.useState)(0),ea=(0,l.useRef)(null),es=(0,l.useRef)(null);(0,l.useEffect)(()=>{m(t)},[t]);let el=(0,l.useCallback)(e=>{let t=e.split(/[.!?]+/).filter(e=>e.trim().length>0),a=e.trim().split(/\s+/).length/t.length,s=100;return a>20&&(s-=20),a>25&&(s-=20),Math.max(0,s)},[]),ei=(0,l.useCallback)(e=>{let t={};e.forEach(e=>{let a=e.toLowerCase().replace(/[^a-z]/g,"");a.length>3&&(t[a]=(t[a]||0)+1)});let a=Object.values(t);return 0===a.length?0:Math.min(100,20*(Math.max(...a)/e.length*100))},[]),er=(0,l.useCallback)((e,t,a,s)=>{let l=[];return t<300&&l.push("Consider adding more content (aim for 300+ words)"),t>2e3&&l.push("Content might be too long, consider breaking into sections"),a<2&&l.push("Add more headings to improve structure"),s<2&&l.push("Add relevant internal and external links"),e<60&&l.push("Optimize content for better SEO performance"),l},[]),en=(0,l.useCallback)(e=>{let t=e.replace(/<[^>]*>/g,""),a=t.trim().split(/\s+/),s=a.length,l=(e.match(/<h[1-6][^>]*>/gi)||[]).length,i=(e.match(/<a[^>]*>/gi)||[]).length,r=(e.match(/<img[^>]*>/gi)||[]).length,n=Math.min(100,s/1500*100),o=Math.min(100,l/5*100),c=el(t),d=ei(a),h=Math.round(.3*n+.2*o+.3*c+.2*d);return{score:h,readabilityScore:c,keywordDensity:d,headingStructure:o,metaOptimization:75,contentLength:n,internalLinks:Math.floor(.7*i),externalLinks:Math.floor(.3*i),imageOptimization:Math.min(100,20*r),suggestions:er(h,s,l,i)}},[el,ei,er]);(0,l.useEffect)(()=>{let e=d.replace(/<[^>]*>/g,"").trim().split(/\s+/).length;S(e),E(Math.ceil(e/200));let t=en(d);C(t),null==n||n(t)},[d,n,en]);let eo=(e,t)=>{if(document.execCommand(e,!1,t),ea.current){let e=ea.current.innerHTML;m(e),a(e)}},ec=()=>{let e=window.getSelection();e&&e.toString()&&v(e.toString())},ed=[{icon:F.A,command:"bold",tooltip:"Bold"},{icon:M.A,command:"italic",tooltip:"Italic"},{icon:R.A,command:"underline",tooltip:"Underline"},{icon:L.A,command:"strikeThrough",tooltip:"Strikethrough"},{icon:O.A,command:"justifyLeft",tooltip:"Align Left"},{icon:H.A,command:"justifyCenter",tooltip:"Align Center"},{icon:q.A,command:"justifyRight",tooltip:"Align Right"},{icon:z.A,command:"insertUnorderedList",tooltip:"Bullet List"},{icon:I.A,command:"insertOrderedList",tooltip:"Numbered List"},{icon:W.A,command:"formatBlock",value:"blockquote",tooltip:"Quote"},{icon:B.A,command:"formatBlock",value:"pre",tooltip:"Code Block"},{icon:D.A,command:"undo",tooltip:"Undo"},{icon:U.A,command:"redo",tooltip:"Redo"}];return(0,s.jsx)("div",{className:"".concat(c||""," ").concat(b?"fixed inset-0 z-50 bg-gray-900":"relative"),children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-white/5 backdrop-blur-sm border-b border-white/20 p-4",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:ed.slice(0,4).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>eo(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:ed.slice(4,7).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>eo(e.command),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:ed.slice(7,9).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>eo(e.command),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:[{icon:_.A,command:"formatBlock",value:"h1",tooltip:"Heading 1"},{icon:Q.A,command:"formatBlock",value:"h2",tooltip:"Heading 2"},{icon:V.A,command:"formatBlock",value:"h3",tooltip:"Heading 3"}].map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>eo(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>y(!w),className:"p-2 rounded-lg bg-white/10 hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:"Text Color",children:(0,s.jsx)(G.A,{className:"h-4 w-4"})}),(0,s.jsx)(r.N,{children:w&&(0,s.jsx)(i.P.div,{ref:es,initial:{opacity:0,scale:.9,y:-10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:-10},className:"absolute top-12 left-0 bg-white/20 backdrop-blur-xl rounded-xl p-3 border border-white/30 shadow-2xl z-10",children:(0,s.jsx)("div",{className:"grid grid-cols-6 gap-2",children:["#000000","#333333","#666666","#999999","#CCCCCC","#FFFFFF","#FF0000","#FF6600","#FFCC00","#33FF00","#0099FF","#6600FF","#FF0099","#FF3366","#FF6699","#66FF99","#99CCFF","#CC99FF"].map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{eo("foreColor",e),y(!1)},className:"w-6 h-6 rounded-full border-2 border-white/30 hover:border-white/60 transition-all duration-200",style:{backgroundColor:e}},t))})})})]}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:ed.slice(9).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>eo(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1 ml-auto",children:[(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>u(!x),className:"p-2 rounded-lg transition-all duration-200 ".concat(x?"bg-purple-500 text-white":"hover:bg-white/20 text-white/80 hover:text-white"),title:x?"Edit Mode":"Preview Mode",children:x?(0,s.jsx)(K.A,{className:"h-4 w-4"}):(0,s.jsx)(J.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>p(!b),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:b?"Exit Fullscreen":"Fullscreen",children:b?(0,s.jsx)(X.A,{className:"h-4 w-4"}):(0,s.jsx)(Y.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[x?(0,s.jsx)("div",{className:"p-8 min-h-[500px] text-white",children:(0,s.jsx)("div",{className:"prose prose-lg prose-invert max-w-none",dangerouslySetInnerHTML:{__html:d}})}):(0,s.jsx)("div",{ref:ea,contentEditable:!0,suppressContentEditableWarning:!0,onInput:e=>{let t=e.currentTarget.innerHTML;m(t),a(t)},onMouseUp:ec,onKeyUp:ec,className:"p-8 min-h-[500px] text-white focus:outline-none",style:{fontSize:"16px",lineHeight:"1.6",fontFamily:"Inter, system-ui, sans-serif"},dangerouslySetInnerHTML:{__html:d}}),(0,s.jsx)(r.N,{children:g&&!x&&(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:.9,y:10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:10},className:"absolute bg-gray-800/90 backdrop-blur-xl rounded-xl p-2 border border-white/20 shadow-2xl z-20",style:{top:"50%",left:"50%",transform:"translate(-50%, -50%)"},children:(0,s.jsx)("div",{className:"flex items-center gap-1",children:[{icon:F.A,command:"bold"},{icon:M.A,command:"italic"},{icon:R.A,command:"underline"},{icon:Z.A,command:"hiliteColor",value:"#ffff00"},{icon:$.A,command:"createLink",value:prompt("Enter URL:")||""}].map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>eo(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))})})})]}),k&&(0,s.jsx)("div",{className:"w-80 bg-white/5 backdrop-blur-sm border-l border-white/20 p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"SEO Analysis"}),(0,s.jsxs)("div",{className:"relative w-32 h-32 mx-auto",children:[(0,s.jsxs)("svg",{className:"w-32 h-32 transform -rotate-90",viewBox:"0 0 120 120",children:[(0,s.jsx)("circle",{cx:"60",cy:"60",r:"50",stroke:"rgba(255,255,255,0.1)",strokeWidth:"8",fill:"none"}),(0,s.jsx)(i.P.circle,{cx:"60",cy:"60",r:"50",stroke:k.score>=80?"#10B981":k.score>=60?"#F59E0B":"#EF4444",strokeWidth:"8",fill:"none",strokeLinecap:"round",initial:{strokeDasharray:0,strokeDashoffset:0},animate:{strokeDasharray:314,strokeDashoffset:314-314*k.score/100},transition:{duration:1.5,ease:"easeOut"}})]}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:k.score}),(0,s.jsx)("div",{className:"text-xs text-white/60",children:"SEO Score"})]})})]})]}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Readability",value:k.readabilityScore,icon:ee.A},{label:"Content Length",value:k.contentLength,icon:h.A},{label:"Heading Structure",value:k.headingStructure,icon:_.A},{label:"Keyword Density",value:k.keywordDensity,icon:o.A}].map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(e.icon,{className:"h-4 w-4 text-white/60"}),(0,s.jsx)("span",{className:"text-sm text-white/80",children:e.label})]}),(0,s.jsx)("span",{className:"text-sm font-bold text-white",children:Math.round(e.value)})]}),(0,s.jsx)("div",{className:"w-full bg-white/10 rounded-full h-2",children:(0,s.jsx)(i.P.div,{className:"h-2 rounded-full ".concat(e.value>=80?"bg-emerald-500":e.value>=60?"bg-yellow-500":"bg-red-500"),initial:{width:0},animate:{width:"".concat(e.value,"%")},transition:{duration:1,delay:.1*t}})})]},t))}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-bold text-white mb-3",children:"Content Stats"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Words"}),(0,s.jsx)("span",{className:"text-white",children:A})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Reading Time"}),(0,s.jsxs)("span",{className:"text-white",children:[P," min"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Internal Links"}),(0,s.jsx)("span",{className:"text-white",children:k.internalLinks})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"External Links"}),(0,s.jsx)("span",{className:"text-white",children:k.externalLinks})]})]})]}),k.suggestions.length>0&&(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-bold text-white mb-3",children:"SEO Suggestions"}),(0,s.jsx)("div",{className:"space-y-2",children:k.suggestions.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(j.A,{className:"h-3 w-3 text-yellow-400 mt-1 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-xs text-white/70",children:e})]},t))})]})]})})]}),(0,s.jsx)("div",{className:"bg-white/5 backdrop-blur-sm border-t border-white/20 px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-white/60",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("span",{children:[A," words"]}),(0,s.jsxs)("span",{children:[P," min read"]}),k&&(0,s.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs ".concat(k.score>=80?"bg-emerald-500/20 text-emerald-300":k.score>=60?"bg-yellow-500/20 text-yellow-300":"bg-red-500/20 text-red-300"),children:["SEO: ",k.score,"/100"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>navigator.clipboard.writeText(d),className:"p-1 rounded hover:bg-white/10 transition-colors",title:"Copy Content",children:(0,s.jsx)(T.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-1 rounded hover:bg-white/10 transition-colors",title:"Save",children:(0,s.jsx)(et.A,{className:"h-4 w-4"})})]})]})})]})})}function es(){var e,t,a,F,M;let[R,L]=(0,l.useState)(""),[O,H]=(0,l.useState)(!1),[q,z]=(0,l.useState)(null),[I,W]=(0,l.useState)(null),[B,D]=(0,l.useState)(null),[U,_]=(0,l.useState)(!1),[Q,V]=(0,l.useState)("content"),[G,K]=(0,l.useState)(!1),[J,X]=(0,l.useState)({contentType:"article",targetWordCount:2e3,tone:"professional",maxPrimaryResults:6,maxDeepResults:4,enableParallelProcessing:!0,qualityThreshold:40,includeSourceCitations:!0,enableFactChecking:!0,retryAttempts:3,targetAudience:"general",contentPurpose:"inform",includeHooks:!0,enableStorytelling:!0,seoOptimization:!0,readabilityTarget:"high-school",includeCallToAction:!0,contentStructure:"analytical",brandVoice:"",competitorAnalysis:!1,trendAnalysis:!1,factCheckingLevel:"standard"}),Y=(0,l.useRef)(null),Z=(0,l.useRef)(null);(0,l.useEffect)(()=>{let e=e=>{console.error("Unhandled promise rejection:",e.reason),e.preventDefault(),O&&(D("An unexpected error occurred during processing"),H(!1))};return window.addEventListener("unhandledrejection",e),()=>{if(window.removeEventListener("unhandledrejection",e),Y.current&&Y.current.close(),Z.current)try{Z.current.releaseLock()}catch(e){}}},[O]);let $=async()=>{if(!R.trim())return;H(!0),z(null),W(null),D(null);let e=null,t=null;try{Y.current&&Y.current.close(),t=setTimeout(()=>{if(O&&(D("Workflow timed out after 15 minutes"),H(!1),e))try{e.releaseLock()}catch(e){}},9e5);let a=await fetch("/api/superagent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({topic:R,options:J})});if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));if(!a.body)throw Error("No response body");Z.current=e=a.body.getReader();let s=new TextDecoder,l="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let r=s.decode(i,{stream:!0}),n=(l+=r).split("\n");for(let e of(l=n.pop()||"",n))if(e.startsWith("data: "))try{let a=e.slice(6).trim();if(a){let e=JSON.parse(a);if("progress"===e.type)z({phase:e.phase,step:e.step,progress:e.progress,message:e.message,estimatedTimeRemaining:e.estimatedTimeRemaining});else if("result"===e.type){W(e),H(!1),t&&clearTimeout(t);return}else if("error"===e.type){D(e.message),H(!1),t&&clearTimeout(t);return}}}catch(t){console.error("Error parsing SSE data:",t,"Line:",e)}}if(l.trim()&&l.startsWith("data: "))try{let e=l.slice(6).trim();if(e){let t=JSON.parse(e);"result"===t.type?(W(t),H(!1)):"error"===t.type&&(D(t.message),H(!1))}}catch(e){console.error("Error parsing final buffer data:",e)}}finally{if(e)try{e.releaseLock()}catch(e){console.error("Error releasing reader lock:",e)}}}catch(e){console.error("Workflow error:",e),D(e instanceof Error?e.message:"Failed to execute workflow"),H(!1)}finally{if(t&&clearTimeout(t),e)try{e.releaseLock()}catch(e){}}},ee=e=>{let t=Math.floor(e/1e3),a=Math.floor(t/60);return a>0?"".concat(a,"m ").concat(t%60,"s"):"".concat(t,"s")},et=async e=>{try{await navigator.clipboard.writeText(e),K(!0),setTimeout(()=>K(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"}),(0,s.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"4s"}})]}),(0,s.jsxs)("div",{className:"relative z-10 container mx-auto px-6 py-8",children:[(0,s.jsx)("div",{className:"text-center mb-16",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"mb-8",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-4 mb-6 p-4 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl blur-lg opacity-75"}),(0,s.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl",children:(0,s.jsx)(m.A,{className:"h-10 w-10 text-white"})})]}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("h1",{className:"text-5xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent",children:"Professional Content Writer AI"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)("div",{className:"px-3 py-1 bg-emerald-500/20 text-emerald-300 rounded-full text-sm font-medium border border-emerald-500/30",children:"Professional"}),(0,s.jsx)("div",{className:"px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30",children:"Enhanced"}),(0,s.jsx)("div",{className:"px-3 py-1 bg-yellow-500/20 text-yellow-300 rounded-full text-sm font-medium border border-yellow-500/30",children:"v3.0"})]})]})]}),(0,s.jsx)(i.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3,duration:.8},className:"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed",children:"Professional content writing AI that follows industry best practices. Features advanced research, content strategy development, storytelling integration, SEO optimization, and comprehensive quality analysis for publication-ready content."}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.8},className:"flex flex-wrap justify-center gap-3 mt-6",children:[{icon:m.A,text:"Professional Writing",color:"from-emerald-500 to-green-600"},{icon:x.A,text:"Content Strategy",color:"from-blue-500 to-cyan-600"},{icon:h.A,text:"Storytelling",color:"from-purple-500 to-pink-600"},{icon:o.A,text:"SEO Optimization",color:"from-orange-500 to-red-600"},{icon:c.A,text:"Quality Analysis",color:"from-yellow-500 to-amber-600"},{icon:u.A,text:"Fact Checking",color:"from-red-500 to-pink-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20",children:[(0,s.jsx)(e.icon,{className:"h-4 w-4 text-white/80"}),(0,s.jsx)("span",{className:"text-sm text-white/80 font-medium",children:e.text})]},t))})]})}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2,duration:.8},className:"max-w-5xl mx-auto mb-16",children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"}),(0,s.jsxs)("div",{className:"relative space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(b.A,{className:"h-6 w-6 text-yellow-400"}),(0,s.jsx)("label",{className:"text-xl font-bold text-white",children:"Research Topic"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:R,onChange:e=>L(e.target.value),placeholder:"Enter your research topic (e.g., 'artificial intelligence in healthcare', 'quantum computing applications')",className:"w-full px-6 py-5 text-lg bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 focus:bg-white/15",disabled:O}),R&&(0,s.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)("div",{className:"w-3 h-3 bg-emerald-400 rounded-full animate-pulse"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("h3",{className:"text-lg font-bold text-white",children:"Configuration"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Content Type"}),(0,s.jsxs)("select",{value:J.contentType,onChange:e=>X({...J,contentType:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"article",className:"bg-gray-800 text-white",children:"Article"}),(0,s.jsx)("option",{value:"blog-post",className:"bg-gray-800 text-white",children:"Blog Post"}),(0,s.jsx)("option",{value:"research-paper",className:"bg-gray-800 text-white",children:"Research Paper"}),(0,s.jsx)("option",{value:"comprehensive-guide",className:"bg-gray-800 text-white",children:"Comprehensive Guide"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Target Word Count"}),(0,s.jsx)("input",{type:"number",value:J.targetWordCount,onChange:e=>X({...J,targetWordCount:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Tone"}),(0,s.jsxs)("select",{value:J.tone,onChange:e=>X({...J,tone:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"professional",className:"bg-gray-800 text-white",children:"Professional"}),(0,s.jsx)("option",{value:"casual",className:"bg-gray-800 text-white",children:"Casual"}),(0,s.jsx)("option",{value:"academic",className:"bg-gray-800 text-white",children:"Academic"}),(0,s.jsx)("option",{value:"conversational",className:"bg-gray-800 text-white",children:"Conversational"}),(0,s.jsx)("option",{value:"authoritative",className:"bg-gray-800 text-white",children:"Authoritative"}),(0,s.jsx)("option",{value:"engaging",className:"bg-gray-800 text-white",children:"Engaging"}),(0,s.jsx)("option",{value:"technical",className:"bg-gray-800 text-white",children:"Technical"})]})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-white/20 pt-8",children:[(0,s.jsxs)(i.P.button,{onClick:()=>_(!U),className:"flex items-center gap-3 mb-6 text-white hover:text-purple-300 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-purple-400"}),(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Advanced Settings"}),U?(0,s.jsx)(v.A,{className:"h-5 w-5"}):(0,s.jsx)(w.A,{className:"h-5 w-5"})]}),(0,s.jsx)(r.N,{children:U&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Primary Sources"}),(0,s.jsx)("input",{type:"number",min:"3",max:"10",value:J.maxPrimaryResults,onChange:e=>X({...J,maxPrimaryResults:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Deep Research Sources"}),(0,s.jsx)("input",{type:"number",min:"2",max:"8",value:J.maxDeepResults,onChange:e=>X({...J,maxDeepResults:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Quality Threshold"}),(0,s.jsx)("input",{type:"number",min:"20",max:"80",value:J.qualityThreshold,onChange:e=>X({...J,qualityThreshold:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Retry Attempts"}),(0,s.jsx)("input",{type:"number",min:"1",max:"5",value:J.retryAttempts,onChange:e=>X({...J,retryAttempts:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 text-yellow-400"}),(0,s.jsx)("h4",{className:"text-lg font-bold text-white",children:"Professional Writing Options"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Target Audience"}),(0,s.jsxs)("select",{value:J.targetAudience,onChange:e=>X({...J,targetAudience:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"general",className:"bg-gray-800 text-white",children:"General"}),(0,s.jsx)("option",{value:"beginner",className:"bg-gray-800 text-white",children:"Beginner"}),(0,s.jsx)("option",{value:"intermediate",className:"bg-gray-800 text-white",children:"Intermediate"}),(0,s.jsx)("option",{value:"expert",className:"bg-gray-800 text-white",children:"Expert"}),(0,s.jsx)("option",{value:"technical",className:"bg-gray-800 text-white",children:"Technical"}),(0,s.jsx)("option",{value:"business",className:"bg-gray-800 text-white",children:"Business"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Content Purpose"}),(0,s.jsxs)("select",{value:J.contentPurpose,onChange:e=>X({...J,contentPurpose:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"inform",className:"bg-gray-800 text-white",children:"Inform"}),(0,s.jsx)("option",{value:"persuade",className:"bg-gray-800 text-white",children:"Persuade"}),(0,s.jsx)("option",{value:"educate",className:"bg-gray-800 text-white",children:"Educate"}),(0,s.jsx)("option",{value:"entertain",className:"bg-gray-800 text-white",children:"Entertain"}),(0,s.jsx)("option",{value:"convert",className:"bg-gray-800 text-white",children:"Convert"}),(0,s.jsx)("option",{value:"engage",className:"bg-gray-800 text-white",children:"Engage"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Content Structure"}),(0,s.jsxs)("select",{value:J.contentStructure,onChange:e=>X({...J,contentStructure:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"analytical",className:"bg-gray-800 text-white",children:"Analytical"}),(0,s.jsx)("option",{value:"problem-solution",className:"bg-gray-800 text-white",children:"Problem-Solution"}),(0,s.jsx)("option",{value:"how-to",className:"bg-gray-800 text-white",children:"How-To"}),(0,s.jsx)("option",{value:"listicle",className:"bg-gray-800 text-white",children:"Listicle"}),(0,s.jsx)("option",{value:"comparison",className:"bg-gray-800 text-white",children:"Comparison"}),(0,s.jsx)("option",{value:"narrative",className:"bg-gray-800 text-white",children:"Narrative"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Readability Target"}),(0,s.jsxs)("select",{value:J.readabilityTarget,onChange:e=>X({...J,readabilityTarget:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"elementary",className:"bg-gray-800 text-white",children:"Elementary"}),(0,s.jsx)("option",{value:"middle-school",className:"bg-gray-800 text-white",children:"Middle School"}),(0,s.jsx)("option",{value:"high-school",className:"bg-gray-800 text-white",children:"High School"}),(0,s.jsx)("option",{value:"college",className:"bg-gray-800 text-white",children:"College"}),(0,s.jsx)("option",{value:"graduate",className:"bg-gray-800 text-white",children:"Graduate"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Fact-Checking Level"}),(0,s.jsxs)("select",{value:J.factCheckingLevel,onChange:e=>X({...J,factCheckingLevel:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O,children:[(0,s.jsx)("option",{value:"basic",className:"bg-gray-800 text-white",children:"Basic"}),(0,s.jsx)("option",{value:"standard",className:"bg-gray-800 text-white",children:"Standard"}),(0,s.jsx)("option",{value:"rigorous",className:"bg-gray-800 text-white",children:"Rigorous"}),(0,s.jsx)("option",{value:"academic",className:"bg-gray-800 text-white",children:"Academic"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Brand Voice"}),(0,s.jsx)("input",{type:"text",value:J.brandVoice,onChange:e=>X({...J,brandVoice:e.target.value}),placeholder:"e.g., friendly, authoritative, innovative",className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:O})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[{id:"includeHooks",label:"Include Hooks",icon:x.A,checked:J.includeHooks,onChange:e=>X({...J,includeHooks:e})},{id:"enableStorytelling",label:"Enable Storytelling",icon:h.A,checked:J.enableStorytelling,onChange:e=>X({...J,enableStorytelling:e})},{id:"seoOptimization",label:"SEO Optimization",icon:o.A,checked:J.seoOptimization,onChange:e=>X({...J,seoOptimization:e})},{id:"includeCallToAction",label:"Include CTAs",icon:j.A,checked:J.includeCallToAction,onChange:e=>X({...J,includeCallToAction:e})},{id:"competitorAnalysis",label:"Competitor Analysis",icon:c.A,checked:J.competitorAnalysis,onChange:e=>X({...J,competitorAnalysis:e})},{id:"trendAnalysis",label:"Trend Analysis",icon:d.A,checked:J.trendAnalysis,onChange:e=>X({...J,trendAnalysis:e})}].map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center gap-4 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/10 transition-all duration-300",children:[(0,s.jsx)(e.icon,{className:"h-5 w-5 text-yellow-400"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-white cursor-pointer",children:e.label})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"checkbox",id:e.id,checked:e.checked,onChange:t=>e.onChange(t.target.checked),className:"sr-only",disabled:O}),(0,s.jsx)("div",{onClick:()=>!O&&e.onChange(!e.checked),className:"w-12 h-6 rounded-full cursor-pointer transition-all duration-300 ".concat(e.checked?"bg-yellow-500":"bg-white/20"),children:(0,s.jsx)("div",{className:"w-5 h-5 bg-white rounded-full shadow-lg transform transition-transform duration-300 mt-0.5 ".concat(e.checked?"translate-x-6":"translate-x-0.5")})})]})]},e.id))}),(0,s.jsxs)("div",{className:"border-t border-white/20 pt-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)(y.A,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("h4",{className:"text-lg font-bold text-white",children:"Technical Settings"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[{id:"parallelProcessing",label:"Parallel Processing",icon:y.A,checked:J.enableParallelProcessing,onChange:e=>X({...J,enableParallelProcessing:e})},{id:"sourceCitations",label:"Source Citations",icon:f.A,checked:J.includeSourceCitations,onChange:e=>X({...J,includeSourceCitations:e})},{id:"factChecking",label:"Fact Checking",icon:u.A,checked:J.enableFactChecking,onChange:e=>X({...J,enableFactChecking:e})}].map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center gap-4 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/10 transition-all duration-300",children:[(0,s.jsx)(e.icon,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-white cursor-pointer",children:e.label})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"checkbox",id:e.id,checked:e.checked,onChange:t=>e.onChange(t.target.checked),className:"sr-only",disabled:O}),(0,s.jsx)("div",{onClick:()=>!O&&e.onChange(!e.checked),className:"w-12 h-6 rounded-full cursor-pointer transition-all duration-300 ".concat(e.checked?"bg-blue-500":"bg-white/20"),children:(0,s.jsx)("div",{className:"w-5 h-5 bg-white rounded-full shadow-lg transform transition-transform duration-300 mt-0.5 ".concat(e.checked?"translate-x-6":"translate-x-0.5")})})]})]},e.id))})]})]})})]}),(0,s.jsxs)("div",{className:"pt-8",children:[(0,s.jsxs)(i.P.button,{whileHover:{scale:1.02,y:-2},whileTap:{scale:.98},onClick:$,disabled:O||!R.trim(),className:"relative w-full group overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,s.jsx)("div",{className:"relative bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 text-white font-bold py-6 px-8 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-4 border border-white/20",children:O?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"relative",children:[(0,s.jsx)(j.A,{className:"h-6 w-6"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-white/30 rounded-full blur-sm"})]}),(0,s.jsx)("span",{className:"text-lg",children:"Processing Research..."}),(0,s.jsx)("div",{className:"flex gap-1",children:[0,1,2].map(e=>(0,s.jsx)(i.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:.2*e},className:"w-2 h-2 bg-white/60 rounded-full"},e))})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N.A,{className:"h-6 w-6"}),(0,s.jsx)(i.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},className:"absolute inset-0 bg-white/30 rounded-full blur-sm"})]}),(0,s.jsx)("span",{className:"text-lg",children:"Launch AI Superagent"}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 text-yellow-300"}),(0,s.jsx)("span",{className:"text-sm text-white/80",children:"Enhanced"})]})]})}),(0,s.jsx)(i.P.div,{className:"absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300",whileHover:{scale:1.05}})]}),R.trim()&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-center gap-2 mt-4 text-white/60 text-sm",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 text-emerald-400"}),(0,s.jsx)("span",{children:"Ready to generate high-quality content"})]})]})]})]})}),(0,s.jsx)(r.N,{children:q&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},className:"max-w-5xl mx-auto mb-12",children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-cyan-500/10 animate-pulse"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6 mb-8",children:[(0,s.jsxs)(i.P.div,{animate:{rotate:360,scale:[1,1.1,1]},transition:{rotate:{duration:3,repeat:1/0,ease:"linear"},scale:{duration:2,repeat:1/0}},className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl blur-lg opacity-75"}),(0,s.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-white"})})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)(i.P.h3,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"text-2xl font-bold text-white mb-2",children:["analysis"===q.phase&&"\uD83E\uDDE0 Analyzing Topic","strategy"===q.phase&&"\uD83C\uDFAF Content Strategy","outline"===q.phase&&"\uD83D\uDCDD Content Outline","primary-research"===q.phase&&"\uD83D\uDD0D Primary Research","gap-analysis"===q.phase&&"\uD83D\uDCCA Gap Analysis","deep-research"===q.phase&&"\uD83C\uDFAF Deep Research","content-generation"===q.phase&&"✨ Content Generation","editing"===q.phase&&"✏️ Content Enhancement","fact-checking"===q.phase&&"\uD83D\uDD0D Fact Checking","optimization"===q.phase&&"\uD83D\uDE80 Final Optimization"]}),(0,s.jsx)(i.P.p,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},className:"text-white/80 text-lg",children:q.message})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)(i.P.div,{initial:{scale:0},animate:{scale:1},className:"text-4xl font-bold text-white mb-1",children:[q.progress,"%"]}),(0,s.jsx)("div",{className:"text-white/60 text-sm",children:"Complete"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-full bg-white/20 rounded-full h-4 mb-4 overflow-hidden",children:(0,s.jsx)(i.P.div,{className:"h-4 rounded-full bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 relative overflow-hidden",initial:{width:0},animate:{width:"".concat(q.progress,"%")},transition:{duration:.8,ease:"easeOut"},children:(0,s.jsx)(i.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent",animate:{x:[-100,300]},transition:{duration:2,repeat:1/0,ease:"linear"},style:{width:"100px"}})})}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(i.P.span,{initial:{opacity:0},animate:{opacity:1},className:"text-white/80 font-medium",children:q.step}),(0,s.jsx)("div",{className:"flex gap-2",children:["analysis","primary-research","gap-analysis","deep-research","content-generation"].map((e,t)=>(0,s.jsx)(i.P.div,{className:"w-3 h-3 rounded-full transition-all duration-300 ".concat(q.phase===e?"bg-white shadow-lg":t<["analysis","primary-research","gap-analysis","deep-research","content-generation"].indexOf(q.phase)?"bg-emerald-400":"bg-white/30"),animate:q.phase===e?{scale:[1,1.3,1]}:{},transition:{duration:1,repeat:1/0}},e))})]})]})]})]})})}),(0,s.jsx)(r.N,{children:B&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},className:"max-w-5xl mx-auto mb-12",children:(0,s.jsxs)("div",{className:"bg-red-500/10 backdrop-blur-xl border border-red-500/30 rounded-3xl p-8 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-500/5 to-pink-500/5"}),(0,s.jsxs)("div",{className:"relative flex items-center gap-4",children:[(0,s.jsx)(i.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0},className:"p-3 bg-red-500/20 rounded-2xl",children:(0,s.jsx)(A.A,{className:"h-8 w-8 text-red-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-red-300 mb-2",children:"Workflow Error"}),(0,s.jsx)("p",{className:"text-red-200 text-lg",children:B})]})]})]})})}),(0,s.jsx)(r.N,{children:I&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},className:"max-w-7xl mx-auto",children:[(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center mb-12",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-4 p-6 bg-emerald-500/10 backdrop-blur-xl rounded-3xl border border-emerald-500/30 mb-6",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,360]},transition:{duration:2,ease:"easeInOut"},className:"p-3 bg-emerald-500/20 rounded-2xl",children:(0,s.jsx)(C.A,{className:"h-8 w-8 text-emerald-400"})}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Content Generated Successfully!"}),(0,s.jsx)("p",{className:"text-white/80",children:"High-quality research-backed content ready for use"})]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:[{icon:x.A,value:I.qualityScore,label:"Quality Score",color:"from-emerald-500 to-green-600",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/30"},{icon:h.A,value:I.wordCount.toLocaleString(),label:"Words Generated",color:"from-blue-500 to-cyan-600",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/30"},{icon:S.A,value:I.sourcesUsed,label:"Sources Analyzed",color:"from-purple-500 to-pink-600",bgColor:"bg-purple-500/10",borderColor:"border-purple-500/30"},{icon:P.A,value:ee(I.metadata.totalResearchTime),label:"Processing Time",color:"from-orange-500 to-red-600",bgColor:"bg-orange-500/10",borderColor:"border-orange-500/30"}].map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"".concat(e.bgColor," backdrop-blur-xl rounded-3xl p-6 shadow-2xl border ").concat(e.borderColor," relative overflow-hidden group hover:scale-105 transition-transform duration-300"),children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"}),(0,s.jsxs)("div",{className:"relative flex items-center gap-4",children:[(0,s.jsx)("div",{className:"p-3 bg-gradient-to-r ".concat(e.color," rounded-2xl shadow-lg"),children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:e.value}),(0,s.jsx)("div",{className:"text-sm text-white/70",children:e.label})]})]})]},t))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Core Quality Metrics"}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Readability",value:I.qualityMetrics.readabilityScore,color:"bg-blue-500"},{label:"Coherence",value:I.qualityMetrics.coherenceScore,color:"bg-green-500"},{label:"Factual Accuracy",value:I.qualityMetrics.factualAccuracy,color:"bg-purple-500"},{label:"Source Reliability",value:I.qualityMetrics.sourceReliability,color:"bg-orange-500"},{label:"Comprehensiveness",value:I.qualityMetrics.comprehensiveness,color:"bg-cyan-500"},{label:"Originality",value:I.qualityMetrics.originalityScore,color:"bg-pink-500"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(e.value,"%")}})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-800 w-8",children:Math.round(e.value)})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Professional Writing"}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Engagement",value:I.qualityMetrics.engagementScore||75,color:"bg-yellow-500"},{label:"Storytelling",value:I.qualityMetrics.storytellingQuality||70,color:"bg-indigo-500"},{label:"Hook Effectiveness",value:I.qualityMetrics.hookEffectiveness||80,color:"bg-red-500"},{label:"CTA Strength",value:I.qualityMetrics.callToActionStrength||85,color:"bg-emerald-500"},{label:"SEO Optimization",value:I.qualityMetrics.seoOptimization||90,color:"bg-blue-600"},{label:"Brand Voice",value:I.qualityMetrics.brandVoiceConsistency||75,color:"bg-purple-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(e.value,"%")}})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-800 w-8",children:Math.round(e.value)})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Content Excellence"}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Expertise Level",value:I.qualityMetrics.expertiseLevel||85,color:"bg-teal-500"},{label:"Trustworthiness",value:I.qualityMetrics.trustworthiness||90,color:"bg-green-600"},{label:"Emotional Resonance",value:I.qualityMetrics.emotionalResonance||70,color:"bg-pink-600"},{label:"Actionability",value:I.qualityMetrics.actionability||80,color:"bg-orange-600"},{label:"Visual Appeal",value:I.qualityMetrics.visualAppeal||85,color:"bg-violet-500"},{label:"Scanability",value:I.qualityMetrics.scanability||90,color:"bg-cyan-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(e.value,"%")}})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-800 w-8",children:Math.round(e.value)})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Research Analytics"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Confidence Score"}),(0,s.jsxs)("span",{className:"text-lg font-bold text-gray-800",children:[(null==(e=I.metadata)?void 0:e.confidenceScore)||0,"%"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Average Source Quality"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-800",children:Math.round((null==(t=I.metadata)?void 0:t.averageSourceQuality)||0)})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Error Count"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-800",children:(null==(a=I.metadata)?void 0:a.errorCount)||0})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Retry Count"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-800",children:(null==(F=I.metadata)?void 0:F.retryCount)||0})]}),I.metadata.phaseTimings&&(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Phase Timings"}),Object.entries(I.metadata.phaseTimings).map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,s.jsx)("span",{className:"text-gray-600 capitalize",children:t.replace("-"," ")}),(0,s.jsx)("span",{className:"text-gray-800",children:ee(a)})]},t)})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Content Elements"}),I.hooks&&I.hooks.length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Hooks Identified"}),(0,s.jsx)("div",{className:"space-y-2",children:I.hooks.slice(0,3).map((e,t)=>(0,s.jsx)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-gray-700 italic",children:['"',e,'"']})},t))})]}),I.callToActions&&I.callToActions.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Call-to-Actions"}),(0,s.jsx)("div",{className:"space-y-2",children:I.callToActions.slice(0,3).map((e,t)=>(0,s.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-gray-700 font-medium",children:e})},t))})]})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Content Analysis"}),I.readabilityAnalysis&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Readability Analysis"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-blue-800",children:I.readabilityAnalysis.grade}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"Reading Level"})]}),(0,s.jsxs)("div",{className:"text-center p-3 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-lg font-bold text-purple-800",children:[I.readabilityAnalysis.readingTime," min"]}),(0,s.jsx)("div",{className:"text-xs text-purple-600",children:"Reading Time"})]})]}),(0,s.jsx)("div",{className:"mt-3 text-center",children:(0,s.jsxs)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("simple"===I.readabilityAnalysis.complexity?"bg-green-100 text-green-800":"moderate"===I.readabilityAnalysis.complexity?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:[I.readabilityAnalysis.complexity.charAt(0).toUpperCase()+I.readabilityAnalysis.complexity.slice(1)," Complexity"]})})]}),I.seoKeywords&&I.seoKeywords.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"SEO Keywords"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:I.seoKeywords.slice(0,8).map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs font-medium",children:e},t))})]}),I.factCheckResults&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Fact Check Results"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-center",children:[(0,s.jsxs)("div",{className:"p-2 bg-green-50 border border-green-200 rounded",children:[(0,s.jsx)("div",{className:"text-sm font-bold text-green-800",children:I.factCheckResults.verifiedClaims}),(0,s.jsx)("div",{className:"text-xs text-green-600",children:"Verified"})]}),(0,s.jsxs)("div",{className:"p-2 bg-yellow-50 border border-yellow-200 rounded",children:[(0,s.jsx)("div",{className:"text-sm font-bold text-yellow-800",children:I.factCheckResults.unverifiedClaims}),(0,s.jsx)("div",{className:"text-xs text-yellow-600",children:"Unverified"})]}),(0,s.jsxs)("div",{className:"p-2 bg-blue-50 border border-blue-200 rounded",children:[(0,s.jsxs)("div",{className:"text-sm font-bold text-blue-800",children:[Math.round(I.factCheckResults.overallTrustworthiness),"%"]}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"Trust Score"})]})]})]})]})]}),I.citations&&I.citations.length>0&&(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Source Citations"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:I.citations.slice(0,6).map((e,t)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-800 line-clamp-2",children:e.title}),(0,s.jsx)("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full ml-2",children:Math.round(e.relevanceScore)})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.domain}),e.usedInSections.length>0&&(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Used in: ",e.usedInSections.join(", ")]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden",children:[(0,s.jsx)("div",{className:"p-8 border-b border-white/20",children:(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:I.title}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:((null==(M=I.metadata)?void 0:M.keywordsTargeted)||[]).map((e,t)=>(0,s.jsx)("span",{className:"px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30",children:e},t))})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>et(I.article),className:"flex items-center gap-2 px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 rounded-xl border border-blue-500/30 transition-all duration-300",children:[(0,s.jsx)(T.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:G?"Copied!":"Copy"})]}),(0,s.jsxs)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center gap-2 px-4 py-2 bg-emerald-500/20 hover:bg-emerald-500/30 text-emerald-300 rounded-xl border border-emerald-500/30 transition-all duration-300",children:[(0,s.jsx)(E.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Export"})]})]})]})}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(ea,{content:I.article,onChange:e=>{console.log("Content updated:",e)},onSEOAnalysis:e=>{console.log("SEO Analysis:",e)},className:"border-none rounded-none"})})]})]})})]})]})}n.A,o.A,c.A,d.A,h.A},47092:(e,t,a)=>{Promise.resolve().then(a.bind(a,9158))}},e=>{var t=t=>e(e.s=t);e.O(0,[322,427,441,684,358],()=>t(47092)),_N_E=e.O()}]);