(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{20408:(e,t,a)=>{"use strict";a.d(t,{SettingsProvider:()=>l,t:()=>n});var r=a(95155),o=a(12115);let s={firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0},i=(0,o.createContext)(void 0),n=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},l=e=>{let{children:t}=e,[a,n]=(0,o.useState)(s),[l,c]=(0,o.useState)(!0),[d,m]=(0,o.useState)(null);(0,o.useEffect)(()=>{try{let e=localStorage.getItem("userSettings");if(e){let t=JSON.parse(e);n({...s,...t})}}catch(e){console.error("Failed to load settings:",e),m("Failed to load user settings")}finally{c(!1)}},[]),(0,o.useEffect)(()=>{if(!l)try{localStorage.setItem("userSettings",JSON.stringify(a))}catch(e){console.error("Failed to save settings:",e),m("Failed to save settings")}},[a,l]);let u=async()=>{try{m(null);let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to save settings")}catch(t){let e=t instanceof Error?t.message:"Failed to save settings";throw m(e),Error(e)}};return(0,r.jsx)(i.Provider,{value:{settings:a,updateSettings:e=>{n(t=>({...t,...e})),m(null)},resetSettings:()=>{n(s),m(null)},saveSettings:u,isLoading:l,error:d},children:t})}},30347:()=>{},38107:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,78346,23)),Promise.resolve().then(a.t.bind(a,82030,23)),Promise.resolve().then(a.t.bind(a,30347,23)),Promise.resolve().then(a.bind(a,20408)),Promise.resolve().then(a.bind(a,57740))},57740:(e,t,a)=>{"use strict";a.d(t,{D:()=>n,ThemeProvider:()=>l});var r=a(95155),o=a(12115),s=a(20408);let i=(0,o.createContext)(void 0),n=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},l=e=>{let{children:t}=e,{settings:a,updateSettings:n}=(0,s.t)(),l=()=>"auto"===a.theme?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a.theme,c=l();(0,o.useEffect)(()=>{let e=document.documentElement;e.classList.remove("dark","light"),e.classList.add(c);let t={blue:{primary:"#2563eb",secondary:"#1e40af",light:"#60a5fa"},purple:{primary:"#7c3aed",secondary:"#6d28d9",light:"#a78bfa"},green:{primary:"#059669",secondary:"#047857",light:"#34d399"},red:{primary:"#e11d48",secondary:"#be185d",light:"#fb7185"},orange:{primary:"#d97706",secondary:"#b45309",light:"#fbbf24"}},r=t[a.accentColor]||t.blue;e.style.setProperty("--accent-primary",r.primary),e.style.setProperty("--accent-secondary",r.secondary),e.style.setProperty("--accent-light",r.light),a.animationsEnabled?(e.style.setProperty("--animation-duration","0.3s"),e.style.setProperty("--transition-duration","0.2s")):(e.style.setProperty("--animation-duration","0s"),e.style.setProperty("--transition-duration","0s")),a.compactMode?e.classList.add("compact-mode"):e.classList.remove("compact-mode")},[c,a.accentColor,a.animationsEnabled,a.compactMode]),(0,o.useEffect)(()=>{if("auto"!==a.theme)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=()=>{let e=document.documentElement;e.classList.remove("dark","light"),e.classList.add(l())};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[a.theme]);let d={theme:c,accentColor:a.accentColor,animationsEnabled:a.animationsEnabled,compactMode:a.compactMode,setTheme:e=>{n({theme:e})},setAccentColor:e=>{n({accentColor:e})},toggleAnimations:()=>{n({animationsEnabled:!a.animationsEnabled})},toggleCompactMode:()=>{n({compactMode:!a.compactMode})}};return(0,r.jsx)(i.Provider,{value:d,children:t})}},78346:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},82030:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_0de778"}}},e=>{var t=t=>e(e.s=t);e.O(0,[769,690,441,684,358],()=>t(38107)),_N_E=e.O()}]);