(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{1243:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4229:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},22958:(e,a,t)=>{"use strict";t.d(a,{A:()=>h});var r=t(95155),l=t(60760),s=t(1978),i=t(40646),n=t(54861),c=t(1243),o=t(81284),d=t(54416);function h(e){let{type:a,title:t,message:h,show:u,onClose:p,autoClose:f=!1,duration:m=5e3,icon:x}=e,b={success:(0,r.jsx)(i.A,{className:"w-5 h-5"}),error:(0,r.jsx)(n.A,{className:"w-5 h-5"}),warning:(0,r.jsx)(c.A,{className:"w-5 h-5"}),info:(0,r.jsx)(o.A,{className:"w-5 h-5"})};return f&&u&&p&&setTimeout(()=>{p()},m),(0,r.jsx)(l.N,{children:u&&(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3},className:"alert-modern ".concat({success:"alert-modern-success",error:"alert-modern-error",warning:"alert-modern-warning",info:"alert-modern-info"}[a]),children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:x||b[a]}),(0,r.jsxs)("div",{className:"flex-1",children:[t&&(0,r.jsx)("h4",{className:"font-semibold mb-1",children:t}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:h})]}),p&&(0,r.jsx)("button",{onClick:p,className:"flex-shrink-0 ml-4 p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})})]})})}},33127:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},40646:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},53904:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54213:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},54416:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},54861:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},57740:(e,a,t)=>{"use strict";t.d(a,{D:()=>n,ThemeProvider:()=>c});var r=t(95155),l=t(12115),s=t(20408);let i=(0,l.createContext)(void 0),n=()=>{let e=(0,l.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},c=e=>{let{children:a}=e,{settings:t,updateSettings:n}=(0,s.t)(),c=()=>"auto"===t.theme?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":t.theme,o=c();(0,l.useEffect)(()=>{let e=document.documentElement;e.classList.remove("dark","light"),e.classList.add(o);let a={blue:{primary:"#2563eb",secondary:"#1e40af",light:"#60a5fa"},purple:{primary:"#7c3aed",secondary:"#6d28d9",light:"#a78bfa"},green:{primary:"#059669",secondary:"#047857",light:"#34d399"},red:{primary:"#e11d48",secondary:"#be185d",light:"#fb7185"},orange:{primary:"#d97706",secondary:"#b45309",light:"#fbbf24"}},r=a[t.accentColor]||a.blue;e.style.setProperty("--accent-primary",r.primary),e.style.setProperty("--accent-secondary",r.secondary),e.style.setProperty("--accent-light",r.light),t.animationsEnabled?(e.style.setProperty("--animation-duration","0.3s"),e.style.setProperty("--transition-duration","0.2s")):(e.style.setProperty("--animation-duration","0s"),e.style.setProperty("--transition-duration","0s")),t.compactMode?e.classList.add("compact-mode"):e.classList.remove("compact-mode")},[o,t.accentColor,t.animationsEnabled,t.compactMode]),(0,l.useEffect)(()=>{if("auto"!==t.theme)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),a=()=>{let e=document.documentElement;e.classList.remove("dark","light"),e.classList.add(c())};return e.addEventListener("change",a),()=>e.removeEventListener("change",a)},[t.theme]);let d={theme:o,accentColor:t.accentColor,animationsEnabled:t.animationsEnabled,compactMode:t.compactMode,setTheme:e=>{n({theme:e})},setAccentColor:e=>{n({accentColor:e})},toggleAnimations:()=>{n({animationsEnabled:!t.animationsEnabled})},toggleCompactMode:()=>{n({compactMode:!t.compactMode})}};return(0,r.jsx)(i.Provider,{value:d,children:a})}},61591:(e,a,t)=>{Promise.resolve().then(t.bind(t,72608))},72608:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>j});var r=t(95155),l=t(12115),s=t(1978),i=t(71007),n=t(381),c=t(54213),o=t(23861),d=t(33127),h=t(75525),u=t(53904),p=t(4229),f=t(39636),m=t(19910),x=t(80488),b=t(22958),g=t(96108),y=t(20408),v=t(57740);function j(){let[e,a]=(0,l.useState)("profile"),[t,j]=(0,l.useState)(!1),[w,N]=(0,l.useState)(""),[k,A]=(0,l.useState)(""),{settings:C,updateSettings:P,resetSettings:E,saveSettings:T,error:M}=(0,y.t)(),{setTheme:S,setAccentColor:L,toggleAnimations:z,toggleCompactMode:D}=(0,v.D)(),R=k||M,q=[{id:"profile",label:"Profile",icon:i.A},{id:"preferences",label:"Preferences",icon:n.A},{id:"content",label:"Content",icon:c.A},{id:"notifications",label:"Notifications",icon:o.A},{id:"appearance",label:"Appearance",icon:d.A},{id:"privacy",label:"Privacy",icon:h.A}],V=async()=>{j(!0),A(""),N("");try{await T(),N("Profile updated successfully!"),setTimeout(()=>N(""),3e3)}catch(e){A("Failed to save settings. Please try again.")}finally{j(!1)}};return(0,r.jsx)(g.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)(s.P.div,{className:"mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600",children:(0,r.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-2",children:"Account Settings"}),(0,r.jsx)("p",{className:"text-xl text-white/80",children:"Manage your profile, preferences, and account settings"})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsx)(s.P.div,{className:"lg:col-span-1",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.6},children:(0,r.jsx)(m.A,{variant:"elevated",children:(0,r.jsx)("nav",{className:"space-y-2",children:q.map(t=>{let l=t.icon;return(0,r.jsxs)("button",{onClick:()=>a(t.id),className:"\n                        w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-300\n                        ".concat(e===t.id?"bg-gradient-to-r from-blue-500 to-purple-600 text-white":"text-white/70 hover:text-white hover:bg-white/10","\n                      "),children:[(0,r.jsx)(l,{className:"w-5 h-5"}),t.label]},t.id)})})})}),(0,r.jsxs)(s.P.div,{className:"lg:col-span-3",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:[(0,r.jsxs)(m.A,{variant:"elevated",children:["profile"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Profile Information"}),(0,r.jsx)("p",{className:"text-white/70",children:"Update your personal information and profile details"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-6",children:[(0,r.jsx)("div",{className:"w-24 h-24 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"w-12 h-12 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Profile Picture"}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(f.A,{variant:"secondary",size:"sm",children:"Upload Photo"}),(0,r.jsx)(f.A,{variant:"secondary",size:"sm",children:"Remove"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(x.A,{label:"First Name",type:"text",value:C.firstName,onChange:e=>P({firstName:e}),placeholder:"Enter your first name",required:!0}),(0,r.jsx)(x.A,{label:"Last Name",type:"text",value:C.lastName,onChange:e=>P({lastName:e}),placeholder:"Enter your last name",required:!0})]}),(0,r.jsx)(x.A,{label:"Email Address",type:"email",value:C.email,onChange:e=>P({email:e}),placeholder:"Enter your email address",required:!0}),(0,r.jsx)(x.A,{label:"Bio",type:"textarea",value:C.bio,onChange:e=>P({bio:e}),placeholder:"Tell us about yourself...",rows:4})]}),"preferences"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"General Preferences"}),(0,r.jsx)("p",{className:"text-white/70",children:"Configure your account preferences and regional settings"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(x.A,{label:"Language",type:"select",value:C.defaultLanguage,onChange:e=>P({defaultLanguage:e}),options:[{value:"en",label:"English"},{value:"es",label:"Spanish"},{value:"fr",label:"French"},{value:"de",label:"German"},{value:"it",label:"Italian"},{value:"pt",label:"Portuguese"},{value:"ja",label:"Japanese"},{value:"ko",label:"Korean"}]}),(0,r.jsx)(x.A,{label:"Timezone",type:"select",value:C.timezone,onChange:e=>P({timezone:e}),options:[{value:"America/New_York",label:"Eastern Time (ET)"},{value:"America/Chicago",label:"Central Time (CT)"},{value:"America/Denver",label:"Mountain Time (MT)"},{value:"America/Los_Angeles",label:"Pacific Time (PT)"},{value:"Europe/London",label:"London (GMT)"},{value:"Europe/Paris",label:"Paris (CET)"},{value:"Asia/Tokyo",label:"Tokyo (JST)"},{value:"Asia/Shanghai",label:"Shanghai (CST)"},{value:"Australia/Sydney",label:"Sydney (AEDT)"},{value:"UTC",label:"UTC"}]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Auto-save Content"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Automatically save your work as you type"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.autoSaveEnabled,onChange:e=>P({autoSaveEnabled:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),"content"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Content Preferences"}),(0,r.jsx)("p",{className:"text-white/70",children:"Set your default preferences for content generation"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(x.A,{label:"Default Word Count",type:"number",value:C.defaultWordCount,onChange:e=>P({defaultWordCount:e}),placeholder:"1000"}),(0,r.jsx)(x.A,{label:"Default Tone",type:"select",value:C.defaultTone,onChange:e=>P({defaultTone:e}),options:[{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"authoritative",label:"Authoritative"},{value:"conversational",label:"Conversational"},{value:"technical",label:"Technical"},{value:"friendly",label:"Friendly"}]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Include Research by Default"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Automatically enable AI research for new content"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.includeResearchByDefault,onChange:e=>P({includeResearchByDefault:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),"notifications"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Notification Preferences"}),(0,r.jsx)("p",{className:"text-white/70",children:"Choose how and when you want to be notified"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Receive important updates via email"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.emailNotifications,onChange:e=>P({emailNotifications:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Push Notifications"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Receive browser notifications for real-time updates"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.pushNotifications,onChange:e=>P({pushNotifications:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Weekly Reports"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Get weekly summaries of your content activity"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.weeklyReports,onChange:e=>P({weeklyReports:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Marketing Emails"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Receive promotional content and feature updates"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.marketingEmails,onChange:e=>P({marketingEmails:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]}),"appearance"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Appearance & Display"}),(0,r.jsx)("p",{className:"text-white/70",children:"Customize how the interface looks and feels"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(x.A,{label:"Theme",type:"select",value:C.theme,onChange:e=>S(e),options:[{value:"dark",label:"Dark Theme"},{value:"light",label:"Light Theme"},{value:"auto",label:"Auto (System)"}]}),(0,r.jsx)(x.A,{label:"Accent Color",type:"select",value:C.accentColor,onChange:e=>L(e),options:[{value:"blue",label:"Professional Blue"},{value:"purple",label:"Executive Purple"},{value:"green",label:"Corporate Green"},{value:"red",label:"Business Red"},{value:"orange",label:"Enterprise Orange"}]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Enable Animations"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Show smooth transitions and visual effects"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.animationsEnabled,onChange:z,className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Compact Mode"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Use a more compact interface layout"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.compactMode,onChange:D,className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]}),"privacy"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Privacy & Data"}),(0,r.jsx)("p",{className:"text-white/70",children:"Control your privacy and data sharing preferences"})]}),(0,r.jsx)(x.A,{label:"Profile Visibility",type:"select",value:C.profileVisibility,onChange:e=>P({profileVisibility:e}),options:[{value:"private",label:"Private - Only visible to you"},{value:"team",label:"Team - Visible to team members"},{value:"public",label:"Public - Visible to everyone"}]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Data Sharing"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Allow anonymous usage data to improve the service"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.dataSharing,onChange:e=>P({dataSharing:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Analytics Tracking"}),(0,r.jsx)("p",{className:"text-white/60 text-sm",children:"Enable analytics to help us understand usage patterns"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:C.analyticsTracking,onChange:e=>P({analyticsTracking:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 text-blue-400"}),(0,r.jsx)("span",{className:"text-blue-400 font-medium",children:"Data Protection"})]}),(0,r.jsx)("p",{className:"text-blue-200 text-sm mb-3",children:"Your privacy is important to us. We follow industry best practices to protect your data."}),(0,r.jsxs)("div",{className:"space-y-2 text-white/70 text-sm",children:[(0,r.jsx)("div",{children:"• All data is encrypted in transit and at rest"}),(0,r.jsx)("div",{children:"• We never sell your personal information"}),(0,r.jsx)("div",{children:"• You can export or delete your data at any time"}),(0,r.jsx)("div",{children:"• Regular security audits and compliance checks"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-white/10 mt-8",children:[(0,r.jsx)(f.A,{variant:"secondary",onClick:()=>{confirm("Are you sure you want to reset all settings to default values?")&&(E(),N("Settings reset to defaults!"),setTimeout(()=>N(""),3e3))},icon:(0,r.jsx)(u.A,{className:"w-4 h-4"}),children:"Reset to Defaults"}),(0,r.jsx)(f.A,{variant:"primary",onClick:V,loading:t,icon:(0,r.jsx)(p.A,{className:"w-4 h-4"}),children:t?"Saving...":"Save Settings"})]})]}),(0,r.jsx)(b.A,{type:"success",message:w,show:!!w,onClose:()=>N(""),autoClose:!0}),(0,r.jsx)(b.A,{type:"error",message:R||"",show:!!R,onClose:()=>A(""),autoClose:!0})]})]})]})})}},75525:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},80488:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var r=t(95155),l=t(1978),s=t(12115);function i(e){let{label:a,type:t="text",value:i,onChange:n,placeholder:c="",required:o=!1,disabled:d=!1,error:h,icon:u,options:p=[],rows:f=4,className:m=""}=e,[x,b]=(0,s.useState)(!1),g=e=>{n("number"===t?Number(e.target.value):e.target.value)},y="\n    modern-input-field\n    ".concat(h?"border-red-400":"","\n    ").concat(d?"opacity-50 cursor-not-allowed":"","\n    ").concat(m,"\n  ").trim(),v="\n    modern-input-label\n    ".concat("modern-input-label-active","\n    ").concat(h?"text-red-400":"","\n  ").trim();return(0,r.jsxs)(l.P.div,{className:"modern-input",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsxs)("label",{className:v,children:[a," ",o&&(0,r.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[u&&(0,r.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 z-10 pointer-events-none",children:u}),"textarea"===t?(0,r.jsx)("textarea",{value:i,onChange:g,onFocus:()=>b(!0),onBlur:()=>b(!1),placeholder:c,required:o,disabled:d,rows:f,className:"".concat(y," ").concat(u?"pl-14":"")}):"select"===t?(0,r.jsxs)("select",{value:i,onChange:g,onFocus:()=>b(!0),onBlur:()=>b(!1),required:o,disabled:d,className:"".concat(y," ").concat(u?"pl-14":""),children:[(0,r.jsx)("option",{value:"",children:c||"Select ".concat(a)}),p.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,r.jsx)("input",{type:t,value:i,onChange:g,onFocus:()=>b(!0),onBlur:()=>b(!1),placeholder:c,required:o,disabled:d,className:"".concat(y," ").concat(u?"pl-14":"")})]}),h&&(0,r.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"text-red-400 text-sm mt-1 px-3",children:h})]})}},81284:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[322,309,503,483,441,684,358],()=>a(61591)),_N_E=e.O()}]);