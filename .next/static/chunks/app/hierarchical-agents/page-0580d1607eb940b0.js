(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[623],{2710:(e,t,a)=>{Promise.resolve().then(a.bind(a,11958))},11958:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>R});var r=a(95155),s=a(12115),i=a(1978),n=a(60760),l=a(49376),o=a(47924),d=a(72713),c=a(71539),m=a(57100),x=a(53311),g=a(16785),h=a(54213),b=a(70463),u=a(57434),p=a(14186),j=a(85690),N=a(40646),f=a(85339),w=a(44940),y=a(92657),v=a(91788),A=a(66516),k=a(47672),C=a(1243),S=a(54861),P=a(17580),L=a(33109),E=a(38564);function T(e){let t,{content:a,targetKeyword:s="",className:n=""}=e,l=(()=>{let e=a.split(/\s+/).filter(e=>e.length>0).length,t=Math.ceil(e/200),r=(a.match(/^#{1,6}\s/gm)||[]).length,i=0;s&&e>0&&(i=(a.toLowerCase().split(s.toLowerCase()).length-1)/e*100);let n=a.split(/[.!?]+/).filter(e=>e.trim().length>0),l=Math.max(0,Math.min(100,100-((n.length>0?e/n.length:0)-15)*2)),o=a.length>150;return{wordCount:e,readingTime:t,headingStructure:r,keywordDensity:i,readabilityScore:l,metaDescription:o,internalLinks:(a.match(/\[.*?\]\((?!http)/g)||[]).length,externalLinks:(a.match(/\[.*?\]\(http/g)||[]).length}})(),d=(t=0,l.wordCount>=1500&&l.wordCount<=3e3?t+=20:l.wordCount>=1e3?t+=15:l.wordCount>=500&&(t+=10),l.headingStructure>=5?t+=15:l.headingStructure>=3?t+=10:l.headingStructure>=1&&(t+=5),s?l.keywordDensity>=1&&l.keywordDensity<=3?t+=20:l.keywordDensity>0&&l.keywordDensity<5&&(t+=10):t+=10,l.readabilityScore>=80?t+=15:l.readabilityScore>=60?t+=10:l.readabilityScore>=40&&(t+=5),l.metaDescription&&(t+=10),l.internalLinks>0&&(t+=10),l.externalLinks>0&&(t+=10),Math.min(100,t)),c=d>=80?N.A:d>=60?C.A:S.A,m=[{label:"Word Count",value:l.wordCount.toLocaleString(),score:l.wordCount>=1500?100:l.wordCount>=1e3?75:l.wordCount>=500?50:25,ideal:"1,500-3,000 words",icon:u.A},{label:"Reading Time",value:"".concat(l.readingTime," min"),score:l.readingTime>=5&&l.readingTime<=15?100:l.readingTime>=3?75:50,ideal:"5-15 minutes",icon:p.A},{label:"Headings",value:l.headingStructure.toString(),score:l.headingStructure>=5?100:l.headingStructure>=3?75:50*(l.headingStructure>=1),ideal:"5+ headings",icon:g.A},{label:"Readability",value:"".concat(l.readabilityScore.toFixed(0),"%"),score:l.readabilityScore,ideal:"80%+ score",icon:P.A},{label:"Internal Links",value:l.internalLinks.toString(),score:100*(l.internalLinks>0),ideal:"2+ links",icon:L.A},{label:"External Links",value:l.externalLinks.toString(),score:100*(l.externalLinks>0),ideal:"1+ links",icon:o.A}];return s&&m.splice(3,0,{label:"Keyword Density",value:"".concat(l.keywordDensity.toFixed(1),"%"),score:l.keywordDensity>=1&&l.keywordDensity<=3?100:50*(l.keywordDensity>0),ideal:"1-3% density",icon:y.A}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-amber-50 to-orange-50 border border-orange-200 rounded-xl p-6 ".concat(n),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg",children:(0,r.jsx)(E.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"SEO Analysis"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Content optimization insights"})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)(c,{className:"h-5 w-5 ".concat(d>=80?"text-green-600":d>=60?"text-yellow-600":"text-red-600")}),(0,r.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:d}),(0,r.jsx)("span",{className:"text-gray-500",children:"/100"})]}),(0,r.jsx)("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:(0,r.jsx)(i.P.div,{initial:{width:0},animate:{width:"".concat(d,"%")},transition:{duration:1,ease:"easeOut"},className:"h-full bg-gradient-to-r ".concat(d>=80?"from-green-500 to-emerald-500":d>=60?"from-yellow-500 to-orange-500":"from-red-500 to-pink-500")})})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:m.map((e,t)=>(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},className:"bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-orange-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-1.5 rounded-md ".concat(e.score>=80?"bg-green-100 text-green-600":e.score>=60?"bg-yellow-100 text-yellow-600":"bg-red-100 text-red-600"),children:(0,r.jsx)(e.icon,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.label}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.ideal})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-lg font-bold text-gray-900",children:e.value}),(0,r.jsx)("div",{className:"w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden",children:(0,r.jsx)(i.P.div,{initial:{width:0},animate:{width:"".concat(e.score,"%")},transition:{duration:.8,delay:.1*t},className:"h-full ".concat(e.score>=80?"bg-green-500":e.score>=60?"bg-yellow-500":"bg-red-500")})})]})]},e.label))}),s&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-gradient-to-r from-orange-100 to-amber-100 rounded-lg border border-orange-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-orange-600"}),(0,r.jsx)("span",{className:"font-medium text-orange-800",children:"Target Keyword:"}),(0,r.jsx)("span",{className:"text-orange-700",children:s}),(0,r.jsxs)("span",{className:"text-orange-600",children:["(",l.keywordDensity.toFixed(1),"% density)"]})]})})]})}var W=a(19910),D=a(39636);function R(){let[e,t]=(0,s.useState)(""),[a,C]=(0,s.useState)(!1),[S,P]=(0,s.useState)(null),[L,E]=(0,s.useState)(null),[R,M]=(0,s.useState)([]),[O,G]=(0,s.useState)(""),[I,z]=(0,s.useState)("preview"),[_,F]=(0,s.useState)(!1),H=async t=>{if(t.preventDefault(),!e.trim())return void E("Please enter a topic");C(!0),E(null),P(null),M([]),G(""),F(!1);try{console.log("\uD83D\uDE80 Starting hierarchical agent workflow...");let t=await fetch("/api/hierarchical-agents",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({topic:e.trim(),options:{maxRetries:3,timeoutMs:3e5}})}),r=await t.json();if(r.success)P(r),M(r.metadata.progressLogs||[]),F(!0),console.log("✅ Workflow completed successfully");else{var a;E(r.error||"Workflow failed"),(null==(a=r.metadata)?void 0:a.progressLogs)&&M(r.metadata.progressLogs),console.error("❌ Workflow failed:",r.details)}}catch(e){E(e instanceof Error?e.message:"An error occurred"),console.error("API request failed:",e)}finally{C(!1)}},K=[{id:"topic-analyst",name:"Topic Analyst",description:"Keyword extraction + Qwen analysis",icon:l.A,color:"from-red-500 to-orange-500",bgColor:"from-red-50 to-orange-50",textColor:"text-red-700",borderColor:"border-red-200"},{id:"primary-research",name:"Primary Research",description:"Multi-query Google search",icon:o.A,color:"from-orange-500 to-amber-500",bgColor:"from-orange-50 to-amber-50",textColor:"text-orange-700",borderColor:"border-orange-200"},{id:"gap-analyst",name:"Gap Analyst",description:"Qwen gap analysis",icon:d.A,color:"from-amber-500 to-yellow-500",bgColor:"from-amber-50 to-yellow-50",textColor:"text-amber-700",borderColor:"border-amber-200"},{id:"deep-research",name:"Deep Research",description:"Targeted gap filling",icon:c.A,color:"from-yellow-500 to-orange-500",bgColor:"from-yellow-50 to-orange-50",textColor:"text-yellow-700",borderColor:"border-yellow-200"},{id:"content-writing",name:"Content Writing",description:"RAG-based Gemini generation",icon:m.A,color:"from-orange-500 to-red-500",bgColor:"from-orange-50 to-red-50",textColor:"text-orange-700",borderColor:"border-orange-200"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-600 via-orange-600 to-amber-600 text-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-12",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-3 bg-white/20 rounded-xl backdrop-blur-sm",children:(0,r.jsx)(x.A,{className:"h-8 w-8"})}),(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold",children:"Aayush Agent"})]}),(0,r.jsx)("p",{className:"text-xl text-orange-100 max-w-3xl mx-auto leading-relaxed",children:"Advanced hierarchical AI system with 5 specialized agents for comprehensive content research and generation"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,r.jsxs)(W.A,{className:"p-6 bg-gradient-to-br from-white to-red-50/50 border border-red-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,r.jsx)(l.A,{className:"h-6 w-6 text-red-600"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Intelligent Research"})]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Our AI agents conduct comprehensive research using advanced search strategies and content analysis to gather authoritative information."})]}),(0,r.jsxs)(W.A,{className:"p-6 bg-gradient-to-br from-white to-orange-50/50 border border-orange-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-orange-100 rounded-lg",children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Gap Analysis"})]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Advanced gap detection identifies missing information and ensures comprehensive coverage of your topic with targeted deep research."})]}),(0,r.jsxs)(W.A,{className:"p-6 bg-gradient-to-br from-white to-amber-50/50 border border-amber-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-amber-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-amber-600"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Human-like Writing"})]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"RAG-based content generation creates engaging, authoritative articles that mimic human writing styles while incorporating all research findings."})]})]}),(0,r.jsxs)(W.A,{className:"mb-8 p-8 bg-gradient-to-br from-white to-orange-50/50 border border-orange-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(g.A,{className:"h-6 w-6 text-orange-600"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"5-Agent Workflow Pipeline"}),(0,r.jsxs)("div",{className:"ml-auto flex items-center gap-2 text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Supervisor Managed"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-6",children:K.map((e,t)=>(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},className:"relative p-6 rounded-xl bg-gradient-to-br ".concat(e.bgColor," border ").concat(e.borderColor," hover:shadow-lg transition-all duration-300 group"),children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r ".concat(e.color," text-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300"),children:(0,r.jsx)(e.icon,{className:"h-8 w-8"})}),(0,r.jsx)("div",{className:"absolute -top-2 -left-2 w-8 h-8 bg-gradient-to-r from-orange-400 to-red-400 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:t+1}),(0,r.jsx)("h3",{className:"font-bold text-lg ".concat(e.textColor," mb-2"),children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]}),t<K.length-1&&(0,r.jsxs)("div",{className:"hidden md:block absolute -right-3 top-1/2 transform -translate-y-1/2 z-10",children:[(0,r.jsx)("div",{className:"w-6 h-0.5 bg-gradient-to-r from-orange-300 to-red-300"}),(0,r.jsx)("div",{className:"absolute -right-1 -top-1 w-2 h-2 bg-red-400 rounded-full"})]})]},e.id))}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-gradient-to-r from-orange-100 to-amber-100 rounded-lg border border-orange-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-orange-800",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:"Shared Memory:"}),(0,r.jsx)("span",{children:"All agents access the same knowledge base with structured data delimiters"})]})})]}),(0,r.jsxs)(W.A,{className:"mb-8 p-8 bg-gradient-to-br from-white to-amber-50/30 border border-amber-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(b.A,{className:"h-6 w-6 text-amber-600"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Start Your Research"})]}),(0,r.jsxs)("form",{onSubmit:H,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Enter a topic to research and write about... (e.g., 'artificial intelligence in healthcare')",disabled:a,className:"w-full px-6 py-4 text-lg bg-white/90 backdrop-blur-sm border border-orange-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 placeholder-gray-500 text-gray-900 shadow-sm"}),(0,r.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-orange-400"})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-orange-500"}),(0,r.jsx)("span",{children:"Estimated time: 3-5 minutes"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-amber-500"}),(0,r.jsx)("span",{children:"Sources: 15-25 pages"})]})]}),(0,r.jsx)(D.A,{type:"submit",disabled:a||!e.trim(),className:"px-8 py-3 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold",children:a?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"Processing..."]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Start Workflow"]})})]})]})]}),(0,r.jsx)(n.N,{children:(a||R.length>0)&&(0,r.jsx)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mb-8",children:(0,r.jsxs)(W.A,{className:"p-8 bg-gradient-to-br from-white to-blue-50/30 border border-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(d.A,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Workflow Progress"}),a&&(0,r.jsxs)("div",{className:"ml-auto flex items-center gap-2 text-blue-600",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Processing..."})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6",children:K.map((e,t)=>{let a=R.filter(t=>t.agent.toLowerCase().includes(e.id.split("-")[0])),s=a.length>0,i=a.some(e=>100===e.progress);return(0,r.jsx)("div",{className:"p-4 rounded-lg border transition-all duration-300 ".concat(i?"bg-gradient-to-br ".concat(e.bgColor," ").concat(e.borderColor," shadow-md"):s?"bg-blue-50 border-blue-200 shadow-sm":"bg-gray-50 border-gray-200"),children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(i?"bg-gradient-to-r ".concat(e.color," text-white"):s?"bg-blue-100 text-blue-600":"bg-gray-100 text-gray-400"),children:i?(0,r.jsx)(N.A,{className:"h-4 w-4"}):s?(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin"}):(0,r.jsx)(e.icon,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium ".concat(i?e.textColor:s?"text-blue-700":"text-gray-500"),children:e.name}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:i?"Completed":s?"Processing...":"Waiting"})]})]})},e.id)})}),R.length>0&&(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto border border-gray-200",children:(0,r.jsx)("div",{className:"space-y-2",children:R.slice(-10).map((e,t)=>(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center gap-3 text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"}),(0,r.jsxs)("span",{className:"font-medium text-blue-700",children:["[",e.agent,"]"]}),(0,r.jsx)("span",{className:"text-gray-700 flex-1",children:e.message}),(0,r.jsxs)("span",{className:"text-blue-600 font-medium",children:["(",e.progress,"%)"]})]},t))})})]})})}),(0,r.jsx)(n.N,{children:L&&(0,r.jsx)(i.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"mb-8",children:(0,r.jsx)(W.A,{className:"p-6 bg-gradient-to-br from-red-50 to-orange-50 border border-red-200",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-red-100 rounded-lg flex-shrink-0",children:(0,r.jsx)(f.A,{className:"h-5 w-5 text-red-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Workflow Error"}),(0,r.jsx)("p",{className:"text-red-700 leading-relaxed",children:L})]})]})})})}),(0,r.jsx)(n.N,{children:S&&_&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"space-y-8",children:[(0,r.jsxs)(W.A,{className:"p-8 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,r.jsx)(N.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-green-800",children:"Workflow Completed Successfully!"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-white/60 rounded-lg border border-green-200",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-800",children:[(S.metadata.executionTime/1e3).toFixed(1),"s"]}),(0,r.jsx)("div",{className:"text-sm text-green-600",children:"Execution Time"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-white/60 rounded-lg border border-green-200",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-green-800",children:S.metadata.memorySize}),(0,r.jsx)("div",{className:"text-sm text-green-600",children:"Memory Logs"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-white/60 rounded-lg border border-green-200",children:[(0,r.jsx)(N.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-green-800",children:S.metadata.completedPhases.length}),(0,r.jsx)("div",{className:"text-sm text-green-600",children:"Completed Phases"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-white/60 rounded-lg border border-green-200",children:[(0,r.jsx)(f.A,{className:"h-8 w-8 text-green-600 mx-auto mb-2"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-green-800",children:S.metadata.errors.length}),(0,r.jsx)("div",{className:"text-sm text-green-600",children:"Errors"})]})]})]}),(0,r.jsx)(T,{content:S.content,targetKeyword:S.metadata.topic,className:"mb-8"}),(0,r.jsxs)(W.A,{className:"overflow-hidden bg-white border border-orange-200 shadow-xl",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-orange-600 to-red-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 bg-white/20 rounded-lg backdrop-blur-sm",children:(0,r.jsx)(m.A,{className:"h-6 w-6"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Generated Article"}),(0,r.jsx)("p",{className:"text-orange-100",children:"Edit and refine your content with our enhanced WYSIWYG editor"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex bg-white/20 rounded-lg p-1 backdrop-blur-sm",children:[(0,r.jsxs)("button",{onClick:()=>z("edit"),className:"px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ".concat("edit"===I?"bg-white text-orange-600 shadow-sm":"text-white hover:bg-white/20"),children:[(0,r.jsx)(w.A,{className:"h-4 w-4 mr-2 inline"}),"Edit"]}),(0,r.jsxs)("button",{onClick:()=>z("preview"),className:"px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ".concat("preview"===I?"bg-white text-orange-600 shadow-sm":"text-white hover:bg-white/20"),children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2 inline"}),"Preview"]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{className:"p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors backdrop-blur-sm",onClick:()=>{let e=new Blob([S.content],{type:"text/markdown"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="".concat(S.metadata.topic.replace(/\s+/g,"-").toLowerCase(),".md"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(t)},title:"Download as Markdown",children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{className:"p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors backdrop-blur-sm",onClick:()=>{navigator.share?navigator.share({title:"Article: ".concat(S.metadata.topic),text:S.content.substring(0,200)+"...",url:window.location.href}):(navigator.clipboard.writeText(S.content),alert("Content copied to clipboard!"))},title:"Share or Copy",children:(0,r.jsx)(A.A,{className:"h-5 w-5"})})]})]})]})}),(0,r.jsx)("div",{className:"h-[800px]",children:(0,r.jsx)(k.A,{content:S.content,onChange:e=>{S&&P({...S,content:e})},viewMode:I})})]})]})}),(0,r.jsx)(n.N,{children:a&&(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"text-center py-16",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-20 h-20 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin mx-auto mb-6"}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-orange-600 animate-pulse"})})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Agents are working their magic..."}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Our intelligent agents are researching, analyzing, and crafting your content. This process typically takes 3-5 minutes."})]})})}),!a&&!_&&(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-16 text-center",children:(0,r.jsx)(W.A,{className:"p-8 bg-gradient-to-br from-white to-orange-50/30 border border-orange-200",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"How It Works"}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed mb-6",children:"Our hierarchical agent system uses a supervisor to coordinate 5 specialized AI agents. Each agent has a specific role in the content creation pipeline, from initial research to final content generation, ensuring comprehensive and high-quality results."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 text-gray-600",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:"Google Search Integration"}),(0,r.jsx)("div",{children:"Real-time web research"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 text-gray-600",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-orange-500 to-amber-500 text-white rounded-full flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:"Qwen + Gemini Models"}),(0,r.jsx)("div",{children:"Advanced AI reasoning"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 text-gray-600",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-amber-500 to-yellow-500 text-white rounded-full flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:"Shared Memory System"}),(0,r.jsx)("div",{children:"Structured data coordination"})]})]})]})]})})})]})]})}},19910:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155),s=a(1978);function i(e){let{children:t,variant:a="default",hover:i=!0,className:n="",onClick:l}=e,o="\n    ".concat({default:"card-modern",elevated:"card-modern-elevated",glass:"glass",neu:"card-neu"}[a],"\n    ").concat(l?"cursor-pointer":"","\n    ").concat(n,"\n  ").trim();return(0,r.jsx)(s.P.div,{className:o,onClick:l,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},...i?{whileHover:{y:-4,scale:1.02},transition:{duration:.3}}:{},children:t})}},39636:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155),s=a(1978);function i(e){let{children:t,variant:a="primary",size:i="md",disabled:n=!1,loading:l=!1,onClick:o,type:d="button",className:c="",icon:m}=e,x="\n    ".concat("btn-modern","\n    ").concat({primary:"btn-modern-primary",secondary:"btn-modern-secondary",accent:"btn-modern-accent"}[a],"\n    ").concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[i],"\n    ").concat(n||l?"opacity-50 cursor-not-allowed":"","\n    ").concat(c,"\n  ").trim();return(0,r.jsx)(s.P.button,{type:d,className:x,onClick:n||l?void 0:o,disabled:n||l,whileHover:n||l?{}:{scale:1.02},whileTap:n||l?{}:{scale:.98},transition:{duration:.2},children:l?(0,r.jsx)("div",{className:"loading-spinner"}):(0,r.jsxs)(r.Fragment,{children:[m&&(0,r.jsx)("span",{className:"flex-shrink-0",children:m}),t]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[322,850,905,672,441,684,358],()=>t(2710)),_N_E=e.O()}]);