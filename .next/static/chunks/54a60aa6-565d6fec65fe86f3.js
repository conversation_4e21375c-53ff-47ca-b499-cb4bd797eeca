"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[277],{4701:(e,t,n)=>{n.d(t,{BQ:()=>ev,CU:()=>I,FF:()=>eh,JJ:()=>eD,KE:()=>eI,KV:()=>y,Nx:()=>ec,OX:()=>eB,Op:()=>eb,T7:()=>ed,YY:()=>F,Zc:()=>eF,bP:()=>ez,gk:()=>k,gu:()=>eu,hO:()=>ef,iI:()=>h,jT:()=>eR,tG:()=>eL});var r=n(52571),o=n(42695),i=n(96770),s=n(10156),a=n(808),l=n(90290),d=n(61514);function c(e){let{state:t,transaction:n}=e,{selection:r}=n,{doc:o}=n,{storedMarks:i}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return i},get selection(){return r},get doc(){return o},get tr(){return r=n.selection,o=n.doc,i=n.storedMarks,n}}}class p{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:e,editor:t,state:n}=this,{view:r}=t,{tr:o}=n,i=this.buildProps(o);return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,(...e)=>{let n=t(...e)(i);return o.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(o),n}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){let{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s=[],a=!!e,l=e||o.tr,d={...Object.fromEntries(Object.entries(n).map(([e,n])=>[e,(...e)=>{let r=this.buildProps(l,t),o=n(...e)(r);return s.push(o),d}])),run:()=>(a||!t||l.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(l),s.every(e=>!0===e))};return d}createCan(e){let{rawCommands:t,state:n}=this,r=e||n.tr,o=this.buildProps(r,!1);return{...Object.fromEntries(Object.entries(t).map(([e,t])=>[e,(...e)=>t(...e)({...o,dispatch:void 0})])),chain:()=>this.createChain(r,!1)}}buildProps(e,t=!0){let{rawCommands:n,editor:r,state:o}=this,{view:i}=r,s={tr:e,editor:r,view:i,state:c({state:o,transaction:e}),dispatch:t?()=>void 0:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([e,t])=>[e,(...e)=>t(...e)(s)]))}};return s}}class u{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){let n=this.callbacks[e];return n&&n.forEach(e=>e.apply(this,t)),this}off(e,t){let n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(e=>e!==t):delete this.callbacks[e]),this}once(e,t){let n=(...r)=>{this.off(e,n),t.apply(this,r)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function h(e,t,n){return void 0===e.config[t]&&e.parent?h(e.parent,t,n):"function"==typeof e.config[t]?e.config[t].bind({...n,parent:e.parent?h(e.parent,t,n):null}):e.config[t]}function f(e){let t=e.filter(e=>"extension"===e.type);return{baseExtensions:t,nodeExtensions:e.filter(e=>"node"===e.type),markExtensions:e.filter(e=>"mark"===e.type)}}function m(e){let t=[],{nodeExtensions:n,markExtensions:r}=f(e),o=[...n,...r],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage,extensions:o},r=h(e,"addGlobalAttributes",n);r&&r().forEach(e=>{e.types.forEach(n=>{Object.entries(e.attributes).forEach(([e,r])=>{t.push({type:n,name:e,attribute:{...i,...r}})})})})}),o.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage},r=h(e,"addAttributes",n);r&&Object.entries(r()).forEach(([n,r])=>{let o={...i,...r};"function"==typeof(null==o?void 0:o.default)&&(o.default=o.default()),(null==o?void 0:o.isRequired)&&(null==o?void 0:o.default)===void 0&&delete o.default,t.push({type:e.name,name:n,attribute:o})})}),t}function g(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function y(...e){return e.filter(e=>!!e).reduce((e,t)=>{let n={...e};return Object.entries(t).forEach(([e,t])=>{if(!n[e]){n[e]=t;return}if("class"===e){let r=t?String(t).split(" "):[],o=n[e]?n[e].split(" "):[],i=r.filter(e=>!o.includes(e));n[e]=[...o,...i].join(" ")}else if("style"===e){let r=t?t.split(";").map(e=>e.trim()).filter(Boolean):[],o=n[e]?n[e].split(";").map(e=>e.trim()).filter(Boolean):[],i=new Map;o.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());i.set(t,n)}),r.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());i.set(t,n)}),n[e]=Array.from(i.entries()).map(([e,t])=>`${e}: ${t}`).join("; ")}else n[e]=t}),n},{})}function b(e,t){return t.filter(t=>t.type===e.type.name).filter(e=>e.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]}).reduce((e,t)=>y(e,t),{})}function v(e){return"function"==typeof e}function k(e,t,...n){return v(e)?t?e.bind(t)(...n):e(...n):e}function w(e,t){return"style"in e?e:{...e,getAttrs:n=>{let r=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===r)return!1;let o=t.reduce((e,t)=>{var r;let o=t.attribute.parseHTML?t.attribute.parseHTML(n):"string"!=typeof(r=n.getAttribute(t.name))?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):"true"===r||"false"!==r&&r;return null==o?e:{...e,[t.name]:o}},{});return{...r,...o}}}}function x(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>!("attrs"===e&&function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t))}function M(e,t){var n;let r=m(e),{nodeExtensions:o,markExtensions:i}=f(e),a=null==(n=o.find(e=>h(e,"topNode")))?void 0:n.name,l=Object.fromEntries(o.map(n=>{let o=r.filter(e=>e.type===n.name),i={name:n.name,options:n.options,storage:n.storage,editor:t},s=x({...e.reduce((e,t)=>{let r=h(t,"extendNodeSchema",i);return{...e,...r?r(n):{}}},{}),content:k(h(n,"content",i)),marks:k(h(n,"marks",i)),group:k(h(n,"group",i)),inline:k(h(n,"inline",i)),atom:k(h(n,"atom",i)),selectable:k(h(n,"selectable",i)),draggable:k(h(n,"draggable",i)),code:k(h(n,"code",i)),whitespace:k(h(n,"whitespace",i)),linebreakReplacement:k(h(n,"linebreakReplacement",i)),defining:k(h(n,"defining",i)),isolating:k(h(n,"isolating",i)),attrs:Object.fromEntries(o.map(e=>{var t;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default}]}))}),a=k(h(n,"parseHTML",i));a&&(s.parseDOM=a.map(e=>w(e,o)));let l=h(n,"renderHTML",i);l&&(s.toDOM=e=>l({node:e,HTMLAttributes:b(e,o)}));let d=h(n,"renderText",i);return d&&(s.toText=d),[n.name,s]})),d=Object.fromEntries(i.map(n=>{let o=r.filter(e=>e.type===n.name),i={name:n.name,options:n.options,storage:n.storage,editor:t},s=x({...e.reduce((e,t)=>{let r=h(t,"extendMarkSchema",i);return{...e,...r?r(n):{}}},{}),inclusive:k(h(n,"inclusive",i)),excludes:k(h(n,"excludes",i)),group:k(h(n,"group",i)),spanning:k(h(n,"spanning",i)),code:k(h(n,"code",i)),attrs:Object.fromEntries(o.map(e=>{var t;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default}]}))}),a=k(h(n,"parseHTML",i));a&&(s.parseDOM=a.map(e=>w(e,o)));let l=h(n,"renderHTML",i);return l&&(s.toDOM=e=>l({mark:e,HTMLAttributes:b(e,o)})),[n.name,s]}));return new s.Sj({topNode:a,nodes:l,marks:d})}function O(e,t){return t.nodes[e]||t.marks[e]||null}function E(e,t){return Array.isArray(t)?t.some(t=>("string"==typeof t?t:t.name)===e.name):t}function S(e,t){let n=s.ZF.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}let C=(e,t=500)=>{let n="",r=e.parentOffset;return e.parent.nodesBetween(Math.max(0,r-t),r,(e,t,o,i)=>{var s,a;let l=(null==(a=(s=e.type.spec).toText)?void 0:a.call(s,{node:e,pos:t,parent:o,index:i}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?l:l.slice(0,Math.max(0,r-t))}),n};function T(e){return"[object RegExp]"===Object.prototype.toString.call(e)}class P{constructor(e){this.find=e.find,this.handler=e.handler}}let A=(e,t)=>{if(T(t))return t.exec(e);let n=t(e);if(!n)return null;let r=[n.text];return r.index=n.index,r.input=e,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function j(e){var t;let{editor:n,from:r,to:o,text:i,rules:s,plugin:a}=e,{view:l}=n;if(l.composing)return!1;let d=l.state.doc.resolve(r);if(d.parent.type.spec.code||(null==(t=d.nodeBefore||d.nodeAfter)?void 0:t.marks.find(e=>e.type.spec.code)))return!1;let u=!1,h=C(d)+i;return s.forEach(e=>{if(u)return;let t=A(h,e.find);if(!t)return;let s=l.state.tr,d=c({state:l.state,transaction:s}),f={from:r-(t[0].length-i.length),to:o},{commands:m,chain:g,can:y}=new p({editor:n,state:d});null!==e.handler({state:d,range:f,match:t,commands:m,chain:g,can:y})&&s.steps.length&&(s.setMeta(a,{transform:s,from:r,to:o,text:i}),l.dispatch(s),u=!0)}),u}function $(e){return"Object"===Object.prototype.toString.call(e).slice(8,-1)&&e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype}function N(e,t){let n={...e};return $(e)&&$(t)&&Object.keys(t).forEach(r=>{$(t[r])&&$(e[r])?n[r]=N(e[r],t[r]):n[r]=t[r]}),n}class I{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=k(h(this,"addOptions",{name:this.name}))),this.storage=k(h(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new I(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>N(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new I(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=k(h(t,"addOptions",{name:t.name})),t.storage=k(h(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){let{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){let o=r.marks();if(!o.find(e=>(null==e?void 0:e.type.name)===t.name))return!1;let i=o.find(e=>(null==e?void 0:e.type.name)===t.name);return i&&n.removeStoredMark(i),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}}class B{constructor(e){this.find=e.find,this.handler=e.handler}}let R=(e,t,n)=>{if(T(t))return[...e.matchAll(t)];let r=t(e,n);return r?r.map(t=>{let n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n}):[]},D=null,L=e=>{var t;let n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null==(t=n.clipboardData)||t.setData("text/html",e),n};class z{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=z.resolve(e),this.schema=M(this.extensions,t),this.setupExtensions()}static resolve(e){var t;let n=z.sort(z.flatten(e)),r=Array.from(new Set((t=n.map(e=>e.name)).filter((e,n)=>t.indexOf(e)!==n)));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(e=>`'${e}'`).join(", ")}]. This can lead to issues.`),n}static flatten(e){return e.map(e=>{let t={name:e.name,options:e.options,storage:e.storage},n=h(e,"addExtensions",t);return n?[e,...this.flatten(n())]:e}).flat(10)}static sort(e){return e.sort((e,t)=>{let n=h(e,"priority")||100,r=h(t,"priority")||100;return n>r?-1:+(n<r)})}get commands(){return this.extensions.reduce((e,t)=>{let n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:O(t.name,this.schema)},r=h(t,"addCommands",n);return r?{...e,...r()}:e},{})}get plugins(){let{editor:e}=this,t=z.sort([...this.extensions].reverse()),n=[],o=[],a=t.map(t=>{let r={name:t.name,options:t.options,storage:t.storage,editor:e,type:O(t.name,this.schema)},s=[],a=h(t,"addKeyboardShortcuts",r),l={};if("mark"===t.type&&h(t,"exitable",r)&&(l.ArrowRight=()=>I.handleExit({editor:e,mark:t})),a){let t=Object.fromEntries(Object.entries(a()).map(([t,n])=>[t,()=>n({editor:e})]));l={...l,...t}}let d=(0,i.w)(l);s.push(d);let c=h(t,"addInputRules",r);E(t,e.options.enableInputRules)&&c&&n.push(...c());let p=h(t,"addPasteRules",r);E(t,e.options.enablePasteRules)&&p&&o.push(...p());let u=h(t,"addProseMirrorPlugins",r);if(u){let e=u();s.push(...e)}return s}).flat();return[function(e){let{editor:t,rules:n}=e,o=new r.k_({state:{init:()=>null,apply(e,r,i){let a=e.getMeta(o);if(a)return a;let l=e.getMeta("applyInputRules");return l&&setTimeout(()=>{let{text:e}=l;"string"==typeof e||(e=S(s.FK.from(e),i.schema));let{from:r}=l,a=r+e.length;j({editor:t,from:r,to:a,text:e,rules:n,plugin:o})}),e.selectionSet||e.docChanged?null:r}},props:{handleTextInput:(e,r,i,s)=>j({editor:t,from:r,to:i,text:s,rules:n,plugin:o}),handleDOMEvents:{compositionend:e=>(setTimeout(()=>{let{$cursor:r}=e.state.selection;r&&j({editor:t,from:r.pos,to:r.pos,text:"",rules:n,plugin:o})}),!1)},handleKeyDown(e,r){if("Enter"!==r.key)return!1;let{$cursor:i}=e.state.selection;return!!i&&j({editor:t,from:i.pos,to:i.pos,text:"\n",rules:n,plugin:o})}},isInputRules:!0});return o}({editor:e,rules:n}),...function(e){let t,{editor:n,rules:o}=e,i=null,a=!1,l=!1,d="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}let u=({state:e,from:r,to:o,rule:i,pasteEvt:s})=>{let a=e.tr;if(function(e){let{editor:t,state:n,from:r,to:o,rule:i,pasteEvent:s,dropEvent:a}=e,{commands:l,chain:d,can:c}=new p({editor:t,state:n}),u=[];return n.doc.nodesBetween(r,o,(e,t)=>{if(!e.isTextblock||e.type.spec.code)return;let p=Math.max(r,t),h=Math.min(o,t+e.content.size);R(e.textBetween(p-t,h-t,void 0,"￼"),i.find,s).forEach(e=>{if(void 0===e.index)return;let t=p+e.index+1,r=t+e[0].length,o={from:n.tr.mapping.map(t),to:n.tr.mapping.map(r)},h=i.handler({state:n,range:o,match:e,commands:l,chain:d,can:c,pasteEvent:s,dropEvent:a});u.push(h)})}),u.every(e=>null!==e)}({editor:n,state:c({state:e,transaction:a}),from:Math.max(r-1,0),to:o.b-1,rule:i,pasteEvent:s,dropEvent:t})&&a.steps.length){try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}return d="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,a}};return o.map(e=>new r.k_({view(e){let t=t=>{var r;(i=(null==(r=e.dom.parentElement)?void 0:r.contains(t.target))?e.dom.parentElement:null)&&(D=n)},r=()=>{D&&(D=null)};return window.addEventListener("dragstart",t),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",t),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(e,n)=>{if(l=i===e.dom.parentElement,t=n,!l){let e=D;e&&setTimeout(()=>{let t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})},10)}return!1},paste:(e,t)=>{var n;let r=null==(n=t.clipboardData)?void 0:n.getData("text/html");return d=t,a=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,r)=>{let o=t[0],i="paste"===o.getMeta("uiEvent")&&!a,c="drop"===o.getMeta("uiEvent")&&!l,p=o.getMeta("applyPasteRules"),h=!!p;if(!i&&!c&&!h)return;if(h){let{text:t}=p;"string"==typeof t||(t=S(s.FK.from(t),r.schema));let{from:n}=p,o=n+t.length;return u({rule:e,state:r,from:n,to:{b:o},pasteEvt:L(t)})}let f=n.doc.content.findDiffStart(r.doc.content),m=n.doc.content.findDiffEnd(r.doc.content);if("number"==typeof f&&m&&f!==m.b)return u({rule:e,state:r,from:f,to:m,pasteEvt:d})}}))}({editor:e,rules:o}),...a]}get attributes(){return m(this.extensions)}get nodeViews(){let{editor:e}=this,{nodeExtensions:t}=f(this.extensions);return Object.fromEntries(t.filter(e=>!!h(e,"addNodeView")).map(t=>{let n=this.attributes.filter(e=>e.type===t.name),r={name:t.name,options:t.options,storage:t.storage,editor:e,type:g(t.name,this.schema)},o=h(t,"addNodeView",r);return o?[t.name,(r,i,s,a,l)=>{let d=b(r,n);return o()({node:r,view:i,getPos:s,decorations:a,innerDecorations:l,editor:e,extension:t,HTMLAttributes:d})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;let n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:O(e.name,this.schema)};"mark"===e.type&&(null==(t=k(h(e,"keepOnSplit",n)))||t)&&this.splittableMarks.push(e.name);let r=h(e,"onBeforeCreate",n),o=h(e,"onCreate",n),i=h(e,"onUpdate",n),s=h(e,"onSelectionUpdate",n),a=h(e,"onTransaction",n),l=h(e,"onFocus",n),d=h(e,"onBlur",n),c=h(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),o&&this.editor.on("create",o),i&&this.editor.on("update",i),s&&this.editor.on("selectionUpdate",s),a&&this.editor.on("transaction",a),l&&this.editor.on("focus",l),d&&this.editor.on("blur",d),c&&this.editor.on("destroy",c)})}}class F{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=k(h(this,"addOptions",{name:this.name}))),this.storage=k(h(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new F(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>N(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new F({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=k(h(t,"addOptions",{name:t.name})),t.storage=k(h(t,"addStorage",{name:t.name,options:t.options})),t}}function _(e,t,n){let{from:r,to:o}=t,{blockSeparator:i="\n\n",textSerializers:s={}}=n||{},a="";return e.nodesBetween(r,o,(e,n,l,d)=>{var c;e.isBlock&&n>r&&(a+=i);let p=null==s?void 0:s[e.type.name];if(p)return l&&(a+=p({node:e,pos:n,parent:l,index:d,range:t})),!1;e.isText&&(a+=null==(c=null==e?void 0:e.text)?void 0:c.slice(Math.max(r,n)-n,o-n))}),a}function H(e){return Object.fromEntries(Object.entries(e.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}let K=F.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new r.k_({key:new r.hs("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:e}=this,{state:t,schema:n}=e,{doc:r,selection:o}=t,{ranges:i}=o,s=Math.min(...i.map(e=>e.$from.pos)),a=Math.max(...i.map(e=>e.$to.pos)),l=H(n);return _(r,{from:s,to:a},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:l})}}})]}});function U(e,t,n={strict:!0}){let r=Object.keys(t);return!r.length||r.every(r=>n.strict?t[r]===e[r]:T(t[r])?t[r].test(e[r]):t[r]===e[r])}function V(e,t,n={}){return e.find(e=>e.type===t&&U(Object.fromEntries(Object.keys(n).map(t=>[t,e.attrs[t]])),n))}function W(e,t,n={}){return!!V(e,t,n)}function G(e,t,n){var r;if(!e||!t)return;let o=e.parent.childAfter(e.parentOffset);if(o.node&&o.node.marks.some(e=>e.type===t)||(o=e.parent.childBefore(e.parentOffset)),!o.node||!o.node.marks.some(e=>e.type===t)||(n=n||(null==(r=o.node.marks[0])?void 0:r.attrs),!V([...o.node.marks],t,n)))return;let i=o.index,s=e.start()+o.offset,a=i+1,l=s+o.node.nodeSize;for(;i>0&&W([...e.parent.child(i-1).marks],t,n);)i-=1,s-=e.parent.child(i).nodeSize;for(;a<e.parent.childCount&&W([...e.parent.child(a).marks],t,n);)l+=e.parent.child(a).nodeSize,a+=1;return{from:s,to:l}}function q(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function J(e){return e instanceof r.U3}function Y(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function Z(e,t=null){if(!t)return null;let n=r.LN.atStart(e),o=r.LN.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return o;let i=n.from,s=o.to;return"all"===t?r.U3.create(e,Y(0,i,s),Y(e.content.size,i,s)):r.U3.create(e,Y(t,i,s),Y(t,i,s))}function Q(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}let X=e=>{let t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){let r=t[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?e.removeChild(r):1===r.nodeType&&X(r)}return e};function ee(e){let t=`<body>${e}</body>`;return X(new window.DOMParser().parseFromString(t,"text/html").body)}function et(e,t,n){if(e instanceof s.bP||e instanceof s.FK)return e;n={slice:!0,parseOptions:{},...n};let r="object"==typeof e&&null!==e,o="string"==typeof e;if(r)try{if(Array.isArray(e)&&e.length>0)return s.FK.fromArray(e.map(e=>t.nodeFromJSON(e)));let r=t.nodeFromJSON(e);return n.errorOnInvalidContent&&r.check(),r}catch(r){if(n.errorOnInvalidContent)throw Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",r),et("",t,n)}if(o){if(n.errorOnInvalidContent){let r=!1,o="",i=new s.Sj({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(r=!0,o="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?s.S4.fromSchema(i).parseSlice(ee(e),n.parseOptions):s.S4.fromSchema(i).parse(ee(e),n.parseOptions),n.errorOnInvalidContent&&r)throw Error("[tiptap error]: Invalid HTML content",{cause:Error(`Invalid element found: ${o}`)})}let r=s.S4.fromSchema(t);return n.slice?r.parseSlice(ee(e),n.parseOptions).content:r.parse(ee(e),n.parseOptions)}return et("",t,n)}let en=e=>!("type"in e);function er(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function eo(e,t,n={}){let{from:r,to:o,empty:i}=e.selection,s=t?g(t,e.schema):null,a=[];e.doc.nodesBetween(r,o,(e,t)=>{if(e.isText)return;let n=Math.max(r,t),i=Math.min(o,t+e.nodeSize);a.push({node:e,from:n,to:i})});let l=o-r,d=a.filter(e=>!s||s.name===e.node.type.name).filter(e=>U(e.node.attrs,n,{strict:!1}));return i?!!d.length:d.reduce((e,t)=>e+t.to-t.from,0)>=l}function ei(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function es(e,t){let n="string"==typeof t?[t]:t;return Object.keys(e).reduce((t,r)=>(n.includes(r)||(t[r]=e[r]),t),{})}function ea(e,t,n={},r={}){return et(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function el(e,t){let n=q(t,e.schema),{from:r,to:o,empty:i}=e.selection,s=[];i?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(r,o,e=>{s.push(...e.marks)});let a=s.find(e=>e.type.name===n.name);return a?{...a.attrs}:{}}function ed(e,t){let n=new a.dL(e);return t.forEach(e=>{e.steps.forEach(e=>{n.step(e)})}),n}function ec(e,t,n){let r=[];return e.nodesBetween(t.from,t.to,(e,t)=>{n(e)&&r.push({node:e,pos:t})}),r}function ep(e){return t=>(function(e,t){for(let n=e.depth;n>0;n-=1){let r=e.node(n);if(t(r))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:r}}})(t.$from,e)}function eu(e,t){let n=ei("string"==typeof t?t:t.name,e.schema);if("node"===n){let n=g(t,e.schema),{from:r,to:o}=e.selection,i=[];e.doc.nodesBetween(r,o,e=>{i.push(e)});let s=i.reverse().find(e=>e.type.name===n.name);return s?{...s.attrs}:{}}return"mark"===n?el(e,t):{}}function eh(e){let{mapping:t,steps:n}=e,r=[];t.maps.forEach((e,o)=>{let i=[];if(e.ranges.length)e.forEach((e,t)=>{i.push({from:e,to:t})});else{let{from:e,to:t}=n[o];if(void 0===e||void 0===t)return;i.push({from:e,to:t})}i.forEach(({from:e,to:n})=>{let i=t.slice(o).map(e,-1),s=t.slice(o).map(n),a=t.invert().map(i,-1),l=t.invert().map(s);r.push({oldRange:{from:a,to:l},newRange:{from:i,to:s}})})});let o=function(e,t=JSON.stringify){let n={};return e.filter(e=>{let r=t(e);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)})}(r);return 1===o.length?o:o.filter((e,t)=>!o.filter((e,n)=>n!==t).some(t=>e.oldRange.from>=t.oldRange.from&&e.oldRange.to<=t.oldRange.to&&e.newRange.from>=t.newRange.from&&e.newRange.to<=t.newRange.to))}function ef(e,t,n){let r=[];return e===t?n.resolve(e).marks().forEach(t=>{let o=G(n.resolve(e),t.type);o&&r.push({mark:t,...o})}):n.nodesBetween(e,t,(e,t)=>{e&&(null==e?void 0:e.nodeSize)!==void 0&&r.push(...e.marks.map(n=>({from:t,to:t+e.nodeSize,mark:n})))}),r}function em(e,t,n){return Object.fromEntries(Object.entries(n).filter(([n])=>{let r=e.find(e=>e.type===t&&e.name===n);return!!r&&r.attribute.keepOnSplit}))}function eg(e,t,n={}){let{empty:r,ranges:o}=e.selection,i=t?q(t,e.schema):null;if(r)return!!(e.storedMarks||e.selection.$from.marks()).filter(e=>!i||i.name===e.type.name).find(e=>U(e.attrs,n,{strict:!1}));let s=0,a=[];if(o.forEach(({$from:t,$to:n})=>{let r=t.pos,o=n.pos;e.doc.nodesBetween(r,o,(e,t)=>{if(!e.isText&&!e.marks.length)return;let n=Math.max(r,t),i=Math.min(o,t+e.nodeSize);s+=i-n,a.push(...e.marks.map(e=>({mark:e,from:n,to:i})))})}),0===s)return!1;let l=a.filter(e=>!i||i.name===e.mark.type.name).filter(e=>U(e.mark.attrs,n,{strict:!1})).reduce((e,t)=>e+t.to-t.from,0),d=a.filter(e=>!i||e.mark.type!==i&&e.mark.type.excludes(i)).reduce((e,t)=>e+t.to-t.from,0);return(l>0?l+d:l)>=s}function ey(e,t){let{nodeExtensions:n}=f(t),r=n.find(t=>t.name===e);if(!r)return!1;let o={name:r.name,options:r.options,storage:r.storage},i=k(h(r,"group",o));return"string"==typeof i&&i.split(" ").includes("list")}function eb(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!=(r=e.text)?r:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let r=!0;return e.content.forEach(e=>{!1!==r&&(eb(e,{ignoreWhitespace:n,checkChildren:t})||(r=!1))}),r}return!1}function ev(e){return e instanceof r.nh}function ek(e,t){let n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){let r=n.filter(e=>null==t?void 0:t.includes(e.type.name));e.tr.ensureMarks(r)}}let ew=(e,t)=>{let n=ep(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;let o=e.doc.nodeAt(r);return!(n.node.type===(null==o?void 0:o.type)&&(0,a.n9)(e.doc,n.pos))||(e.join(n.pos),!0)},ex=(e,t)=>{let n=ep(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;let o=e.doc.nodeAt(r);return!(n.node.type===(null==o?void 0:o.type)&&(0,a.n9)(e.doc,r))||(e.join(r),!0)};var eM=Object.freeze({__proto__:null,blur:()=>({editor:e,view:t})=>(requestAnimationFrame(()=>{var n;e.isDestroyed||(t.dom.blur(),null==(n=null==window?void 0:window.getSelection())||n.removeAllRanges())}),!0),clearContent:(e=!1)=>({commands:t})=>t.setContent("",e),clearNodes:()=>({state:e,tr:t,dispatch:n})=>{let{selection:r}=t,{ranges:o}=r;return!n||(o.forEach(({$from:n,$to:r})=>{e.doc.nodesBetween(n.pos,r.pos,(e,n)=>{if(e.type.isText)return;let{doc:r,mapping:o}=t,i=r.resolve(o.map(n)),s=r.resolve(o.map(n+e.nodeSize)),l=i.blockRange(s);if(!l)return;let d=(0,a.jP)(l);if(e.type.isTextblock){let{defaultType:e}=i.parent.contentMatchAt(i.index());t.setNodeMarkup(l.start,e)}(d||0===d)&&t.lift(l,d)})}),!0)},command:e=>t=>e(t),createParagraphNear:()=>({state:e,dispatch:t})=>(0,l.Z1)(e,t),cut:(e,t)=>({editor:n,tr:o})=>{let{state:i}=n,s=i.doc.slice(e.from,e.to);o.deleteRange(e.from,e.to);let a=o.mapping.map(t);return o.insert(a,s.content),o.setSelection(new r.U3(o.doc.resolve(a-1))),!0},deleteCurrentNode:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,r=n.$anchor.node();if(r.content.size>0)return!1;let o=e.selection.$anchor;for(let n=o.depth;n>0;n-=1)if(o.node(n).type===r.type){if(t){let t=o.before(n),r=o.after(n);e.delete(t,r).scrollIntoView()}return!0}return!1},deleteNode:e=>({tr:t,state:n,dispatch:r})=>{let o=g(e,n.schema),i=t.selection.$anchor;for(let e=i.depth;e>0;e-=1)if(i.node(e).type===o){if(r){let n=i.before(e),r=i.after(e);t.delete(n,r).scrollIntoView()}return!0}return!1},deleteRange:e=>({tr:t,dispatch:n})=>{let{from:r,to:o}=e;return n&&t.delete(r,o),!0},deleteSelection:()=>({state:e,dispatch:t})=>(0,l.ic)(e,t),enter:()=>({commands:e})=>e.keyboardShortcut("Enter"),exitCode:()=>({state:e,dispatch:t})=>(0,l.I$)(e,t),extendMarkRange:(e,t={})=>({tr:n,state:o,dispatch:i})=>{let s=q(e,o.schema),{doc:a,selection:l}=n,{$from:d,from:c,to:p}=l;if(i){let e=G(d,s,t);if(e&&e.from<=c&&e.to>=p){let t=r.U3.create(a,e.from,e.to);n.setSelection(t)}}return!0},first:e=>t=>{let n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1},focus:(e=null,t={})=>({editor:n,view:r,tr:o,dispatch:i})=>{t={scrollIntoView:!0,...t};let s=()=>{(Q()||"Android"===navigator.platform||/android/i.test(navigator.userAgent))&&r.dom.focus(),requestAnimationFrame(()=>{!n.isDestroyed&&(r.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())})};if(r.hasFocus()&&null===e||!1===e)return!0;if(i&&null===e&&!J(n.state.selection))return s(),!0;let a=Z(o.doc,e)||n.state.selection,l=n.state.selection.eq(a);return i&&(l||o.setSelection(a),l&&o.storedMarks&&o.setStoredMarks(o.storedMarks),s()),!0},forEach:(e,t)=>n=>e.every((e,r)=>t(e,{...n,index:r})),insertContent:(e,t)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),insertContentAt:(e,t,n)=>({tr:o,dispatch:i,editor:l})=>{var d;if(i){let i,c;n={parseOptions:l.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};let p=e=>{l.emit("contentError",{editor:l,error:e,disableCollaboration:()=>{l.storage.collaboration&&(l.storage.collaboration.isDisabled=!0)}})},u={preserveWhitespace:"full",...n.parseOptions};if(!n.errorOnInvalidContent&&!l.options.enableContentCheck&&l.options.emitContentError)try{et(t,l.schema,{parseOptions:u,errorOnInvalidContent:!0})}catch(e){p(e)}try{i=et(t,l.schema,{parseOptions:u,errorOnInvalidContent:null!=(d=n.errorOnInvalidContent)?d:l.options.enableContentCheck})}catch(e){return p(e),!1}let{from:h,to:f}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},m=!0,g=!0;if((en(i)?i:[i]).forEach(e=>{e.check(),m=!!m&&e.isText&&0===e.marks.length,g=!!g&&e.isBlock}),h===f&&g){let{parent:e}=o.doc.resolve(h);!e.isTextblock||e.type.spec.code||e.childCount||(h-=1,f+=1)}if(m){if(Array.isArray(t))c=t.map(e=>e.text||"").join("");else if(t instanceof s.FK){let e="";t.forEach(t=>{t.text&&(e+=t.text)}),c=e}else c="object"==typeof t&&t&&t.text?t.text:t;o.insertText(c,h,f)}else c=i,o.replaceWith(h,f,c);n.updateSelection&&function(e,t,n){let o=e.steps.length-1;if(o<t)return;let i=e.steps[o];if(!(i instanceof a.Ln||i instanceof a.Wg))return;let s=e.mapping.maps[o],l=0;s.forEach((e,t,n,r)=>{0===l&&(l=r)}),e.setSelection(r.LN.near(e.doc.resolve(l),-1))}(o,o.steps.length-1,0),n.applyInputRules&&o.setMeta("applyInputRules",{from:h,text:c}),n.applyPasteRules&&o.setMeta("applyPasteRules",{from:h,text:c})}return!0},joinBackward:()=>({state:e,dispatch:t})=>(0,l.Qv)(e,t),joinDown:()=>({state:e,dispatch:t})=>(0,l.bh)(e,t),joinForward:()=>({state:e,dispatch:t})=>(0,l.eT)(e,t),joinItemBackward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=(0,a.N0)(e.doc,e.selection.$from.pos,-1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinItemForward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=(0,a.N0)(e.doc,e.selection.$from.pos,1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinTextblockBackward:()=>({state:e,dispatch:t})=>(0,l.eB)(e,t),joinTextblockForward:()=>({state:e,dispatch:t})=>(0,l._G)(e,t),joinUp:()=>({state:e,dispatch:t})=>(0,l.G2)(e,t),keyboardShortcut:e=>({editor:t,view:n,tr:r,dispatch:o})=>{let i=(function(e){let t,n,r,o,i=e.split(/-(?!$)/),s=i[i.length-1];"Space"===s&&(s=" ");for(let e=0;e<i.length-1;e+=1){let s=i[e];if(/^(cmd|meta|m)$/i.test(s))o=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))Q()||er()?o=!0:n=!0;else throw Error(`Unrecognized modifier name: ${s}`)}return t&&(s=`Alt-${s}`),n&&(s=`Ctrl-${s}`),o&&(s=`Meta-${s}`),r&&(s=`Shift-${s}`),s})(e).split(/-(?!$)/),s=i.find(e=>!["Alt","Ctrl","Meta","Shift"].includes(e)),a=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),l=t.captureTransaction(()=>{n.someProp("handleKeyDown",e=>e(n,a))});return null==l||l.steps.forEach(e=>{let t=e.map(r.mapping);t&&o&&r.maybeStep(t)}),!0},lift:(e,t={})=>({state:n,dispatch:r})=>{let o=g(e,n.schema);return!!eo(n,o,t)&&(0,l.yY)(n,r)},liftEmptyBlock:()=>({state:e,dispatch:t})=>(0,l.iz)(e,t),liftListItem:e=>({state:t,dispatch:n})=>{let r=g(e,t.schema);return(0,d.T2)(r)(t,n)},newlineInCode:()=>({state:e,dispatch:t})=>(0,l.pC)(e,t),resetAttributes:(e,t)=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null,a=ei("string"==typeof e?e:e.name,r.schema);return!!a&&("node"===a&&(i=g(e,r.schema)),"mark"===a&&(s=q(e,r.schema)),o&&n.selection.ranges.forEach(e=>{r.doc.nodesBetween(e.$from.pos,e.$to.pos,(e,r)=>{i&&i===e.type&&n.setNodeMarkup(r,void 0,es(e.attrs,t)),s&&e.marks.length&&e.marks.forEach(o=>{s===o.type&&n.addMark(r,r+e.nodeSize,s.create(es(o.attrs,t)))})})}),!0)},scrollIntoView:()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),selectAll:()=>({tr:e,dispatch:t})=>{if(t){let t=new r.i5(e.doc);e.setSelection(t)}return!0},selectNodeBackward:()=>({state:e,dispatch:t})=>(0,l._e)(e,t),selectNodeForward:()=>({state:e,dispatch:t})=>(0,l.Sd)(e,t),selectParentNode:()=>({state:e,dispatch:t})=>(0,l.hy)(e,t),selectTextblockEnd:()=>({state:e,dispatch:t})=>(0,l.ec)(e,t),selectTextblockStart:()=>({state:e,dispatch:t})=>(0,l.$f)(e,t),setContent:(e,t=!1,n={},r={})=>({editor:o,tr:i,dispatch:s,commands:a})=>{var l,d;let{doc:c}=i;if("full"!==n.preserveWhitespace){let a=ea(e,o.schema,n,{errorOnInvalidContent:null!=(l=r.errorOnInvalidContent)?l:o.options.enableContentCheck});return s&&i.replaceWith(0,c.content.size,a).setMeta("preventUpdate",!t),!0}return s&&i.setMeta("preventUpdate",!t),a.insertContentAt({from:0,to:c.content.size},e,{parseOptions:n,errorOnInvalidContent:null!=(d=r.errorOnInvalidContent)?d:o.options.enableContentCheck})},setMark:(e,t={})=>({tr:n,state:r,dispatch:o})=>{let{selection:i}=n,{empty:s,ranges:a}=i,l=q(e,r.schema);if(o)if(s){let e=el(r,l);n.addStoredMark(l.create({...e,...t}))}else a.forEach(e=>{let o=e.$from.pos,i=e.$to.pos;r.doc.nodesBetween(o,i,(e,r)=>{let s=Math.max(r,o),a=Math.min(r+e.nodeSize,i);e.marks.find(e=>e.type===l)?e.marks.forEach(e=>{l===e.type&&n.addMark(s,a,l.create({...e.attrs,...t}))}):n.addMark(s,a,l.create(t))})});return function(e,t,n){var r;let{selection:o}=t,i=null;if(J(o)&&(i=o.$cursor),i){let t=null!=(r=e.storedMarks)?r:i.marks();return!!n.isInSet(t)||!t.some(e=>e.type.excludes(n))}let{ranges:s}=o;return s.some(({$from:t,$to:r})=>{let o=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,r.pos,(e,t,r)=>{if(o)return!1;if(e.isInline){let t=!r||r.type.allowsMarkType(n),i=!!n.isInSet(e.marks)||!e.marks.some(e=>e.type.excludes(n));o=t&&i}return!o}),o})}(r,n,l)},setMeta:(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),setNode:(e,t={})=>({state:n,dispatch:r,chain:o})=>{let i,s=g(e,n.schema);return(n.selection.$anchor.sameParent(n.selection.$head)&&(i=n.selection.$anchor.parent.attrs),s.isTextblock)?o().command(({commands:e})=>!!(0,l.y_)(s,{...i,...t})(n)||e.clearNodes()).command(({state:e})=>(0,l.y_)(s,{...i,...t})(e,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,o=Y(e,0,n.content.size),i=r.nh.create(n,o);t.setSelection(i)}return!0},setTextSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,{from:o,to:i}="number"==typeof e?{from:e,to:e}:e,s=r.U3.atStart(n).from,a=r.U3.atEnd(n).to,l=Y(o,s,a),d=Y(i,s,a),c=r.U3.create(n,l,d);t.setSelection(c)}return!0},sinkListItem:e=>({state:t,dispatch:n})=>{let r=g(e,t.schema);return(0,d.$B)(r)(t,n)},splitBlock:({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:o,editor:i})=>{let{selection:s,doc:l}=t,{$from:d,$to:c}=s,p=em(i.extensionManager.attributes,d.node().type.name,d.node().attrs);if(s instanceof r.nh&&s.node.isBlock)return!!d.parentOffset&&!!(0,a.zy)(l,d.pos)&&(o&&(e&&ek(n,i.extensionManager.splittableMarks),t.split(d.pos).scrollIntoView()),!0);if(!d.parent.isBlock)return!1;let u=c.parentOffset===c.parent.content.size,h=0===d.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(d.node(-1).contentMatchAt(d.indexAfter(-1))),f=u&&h?[{type:h,attrs:p}]:void 0,m=(0,a.zy)(t.doc,t.mapping.map(d.pos),1,f);if(!f&&!m&&(0,a.zy)(t.doc,t.mapping.map(d.pos),1,h?[{type:h}]:void 0)&&(m=!0,f=h?[{type:h,attrs:p}]:void 0),o){if(m&&(s instanceof r.U3&&t.deleteSelection(),t.split(t.mapping.map(d.pos),1,f),h&&!u&&!d.parentOffset&&d.parent.type!==h)){let e=t.mapping.map(d.before()),n=t.doc.resolve(e);d.node(-1).canReplaceWith(n.index(),n.index()+1,h)&&t.setNodeMarkup(t.mapping.map(d.before()),h)}e&&ek(n,i.extensionManager.splittableMarks),t.scrollIntoView()}return m},splitListItem:(e,t={})=>({tr:n,state:o,dispatch:i,editor:l})=>{var d;let c=g(e,o.schema),{$from:p,$to:u}=o.selection,h=o.selection.node;if(h&&h.isBlock||p.depth<2||!p.sameParent(u))return!1;let f=p.node(-1);if(f.type!==c)return!1;let m=l.extensionManager.attributes;if(0===p.parent.content.size&&p.node(-1).childCount===p.indexAfter(-1)){if(2===p.depth||p.node(-3).type!==c||p.index(-2)!==p.node(-2).childCount-1)return!1;if(i){let e=s.FK.empty,o=p.index(-1)?1:p.index(-2)?2:3;for(let t=p.depth-o;t>=p.depth-3;t-=1)e=s.FK.from(p.node(t).copy(e));let i=p.indexAfter(-1)<p.node(-2).childCount?1:p.indexAfter(-2)<p.node(-3).childCount?2:3,a={...em(m,p.node().type.name,p.node().attrs),...t},l=(null==(d=c.contentMatch.defaultType)?void 0:d.createAndFill(a))||void 0;e=e.append(s.FK.from(c.createAndFill(null,l)||void 0));let u=p.before(p.depth-(o-1));n.replace(u,p.after(-i),new s.Ji(e,4-o,0));let h=-1;n.doc.nodesBetween(u,n.doc.content.size,(e,t)=>{if(h>-1)return!1;e.isTextblock&&0===e.content.size&&(h=t+1)}),h>-1&&n.setSelection(r.U3.near(n.doc.resolve(h))),n.scrollIntoView()}return!0}let y=u.pos===p.end()?f.contentMatchAt(0).defaultType:null,b={...em(m,f.type.name,f.attrs),...t},v={...em(m,p.node().type.name,p.node().attrs),...t};n.delete(p.pos,u.pos);let k=y?[{type:c,attrs:b},{type:y,attrs:v}]:[{type:c,attrs:b}];if(!(0,a.zy)(n.doc,p.pos,2))return!1;if(i){let{selection:e,storedMarks:t}=o,{splittableMarks:r}=l.extensionManager,s=t||e.$to.parentOffset&&e.$from.marks();if(n.split(p.pos,2,k).scrollIntoView(),!s||!i)return!0;let a=s.filter(e=>r.includes(e.type.name));n.ensureMarks(a)}return!0},toggleList:(e,t,n,r={})=>({editor:o,tr:i,state:s,dispatch:a,chain:l,commands:d,can:c})=>{let{extensions:p,splittableMarks:u}=o.extensionManager,h=g(e,s.schema),f=g(t,s.schema),{selection:m,storedMarks:y}=s,{$from:b,$to:v}=m,k=b.blockRange(v),w=y||m.$to.parentOffset&&m.$from.marks();if(!k)return!1;let x=ep(e=>ey(e.type.name,p))(m);if(k.depth>=1&&x&&k.depth-x.depth<=1){if(x.node.type===h)return d.liftListItem(f);if(ey(x.node.type.name,p)&&h.validContent(x.node.content)&&a)return l().command(()=>(i.setNodeMarkup(x.pos,h),!0)).command(()=>ew(i,h)).command(()=>ex(i,h)).run()}return n&&w&&a?l().command(()=>{let e=c().wrapInList(h,r),t=w.filter(e=>u.includes(e.type.name));return i.ensureMarks(t),!!e||d.clearNodes()}).wrapInList(h,r).command(()=>ew(i,h)).command(()=>ex(i,h)).run():l().command(()=>!!c().wrapInList(h,r)||d.clearNodes()).wrapInList(h,r).command(()=>ew(i,h)).command(()=>ex(i,h)).run()},toggleMark:(e,t={},n={})=>({state:r,commands:o})=>{let{extendEmptyMarkRange:i=!1}=n,s=q(e,r.schema);return eg(r,s,t)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,t)},toggleNode:(e,t,n={})=>({state:r,commands:o})=>{let i,s=g(e,r.schema),a=g(t,r.schema),l=eo(r,s,n);return(r.selection.$anchor.sameParent(r.selection.$head)&&(i=r.selection.$anchor.parent.attrs),l)?o.setNode(a,i):o.setNode(s,{...i,...n})},toggleWrap:(e,t={})=>({state:n,commands:r})=>{let o=g(e,n.schema);return eo(n,o,t)?r.lift(o):r.wrapIn(o,t)},undoInputRule:()=>({state:e,dispatch:t})=>{let n=e.plugins;for(let r=0;r<n.length;r+=1){let o,i=n[r];if(i.spec.isInputRules&&(o=i.getState(e))){if(t){let t=e.tr,n=o.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(o.text){let n=t.doc.resolve(o.from).marks();t.replaceWith(o.from,o.to,e.schema.text(o.text,n))}else t.delete(o.from,o.to)}return!0}}return!1},unsetAllMarks:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,{empty:r,ranges:o}=n;return!!r||(t&&o.forEach(t=>{e.removeMark(t.$from.pos,t.$to.pos)}),!0)},unsetMark:(e,t={})=>({tr:n,state:r,dispatch:o})=>{var i;let{extendEmptyMarkRange:s=!1}=t,{selection:a}=n,l=q(e,r.schema),{$from:d,empty:c,ranges:p}=a;if(!o)return!0;if(c&&s){let{from:e,to:t}=a,r=null==(i=d.marks().find(e=>e.type===l))?void 0:i.attrs,o=G(d,l,r);o&&(e=o.from,t=o.to),n.removeMark(e,t,l)}else p.forEach(e=>{n.removeMark(e.$from.pos,e.$to.pos,l)});return n.removeStoredMark(l),!0},updateAttributes:(e,t={})=>({tr:n,state:r,dispatch:o})=>{let i=null,s=null,a=ei("string"==typeof e?e:e.name,r.schema);return!!a&&("node"===a&&(i=g(e,r.schema)),"mark"===a&&(s=q(e,r.schema)),o&&n.selection.ranges.forEach(e=>{let o,a,l,d,c=e.$from.pos,p=e.$to.pos;n.selection.empty?r.doc.nodesBetween(c,p,(e,t)=>{i&&i===e.type&&(l=Math.max(t,c),d=Math.min(t+e.nodeSize,p),o=t,a=e)}):r.doc.nodesBetween(c,p,(e,r)=>{r<c&&i&&i===e.type&&(l=Math.max(r,c),d=Math.min(r+e.nodeSize,p),o=r,a=e),r>=c&&r<=p&&(i&&i===e.type&&n.setNodeMarkup(r,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach(o=>{if(s===o.type){let i=Math.max(r,c),a=Math.min(r+e.nodeSize,p);n.addMark(i,a,s.create({...o.attrs,...t}))}}))}),a&&(void 0!==o&&n.setNodeMarkup(o,void 0,{...a.attrs,...t}),s&&a.marks.length&&a.marks.forEach(e=>{s===e.type&&n.addMark(l,d,s.create({...e.attrs,...t}))}))}),!0)},wrapIn:(e,t={})=>({state:n,dispatch:r})=>{let o=g(e,n.schema);return(0,l.Im)(o,t)(n,r)},wrapInList:(e,t={})=>({state:n,dispatch:r})=>{let o=g(e,n.schema);return(0,d.Sd)(o,t)(n,r)}});let eO=F.create({name:"commands",addCommands:()=>({...eM})}),eE=F.create({name:"drop",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tiptapDrop"),props:{handleDrop:(e,t,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:r})}}})]}}),eS=F.create({name:"editable",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("editable"),props:{editable:()=>this.editor.options.editable}})]}}),eC=new r.hs("focusEvents"),eT=F.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:e}=this;return[new r.k_({key:eC,props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;let r=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1},blur:(t,n)=>{e.isFocused=!1;let r=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1}}}})]}}),eP=F.create({name:"keymap",addKeyboardShortcuts(){let e=()=>this.editor.commands.first(({commands:e})=>[()=>e.undoInputRule(),()=>e.command(({tr:t})=>{let{selection:n,doc:o}=t,{empty:i,$anchor:s}=n,{pos:a,parent:l}=s,d=s.parent.isTextblock&&a>0?t.doc.resolve(a-1):s,c=d.parent.type.spec.isolating,p=s.pos-s.parentOffset,u=c&&1===d.parent.childCount?p===s.pos:r.LN.atStart(o).from===a;return!!i&&!!l.type.isTextblock&&!l.textContent.length&&!!u&&(!u||"paragraph"!==s.parent.type.name)&&e.clearNodes()}),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()]),t=()=>this.editor.commands.first(({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},o={...n},i={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Q()||er()?i:o},addProseMirrorPlugins(){return[new r.k_({key:new r.hs("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some(e=>e.getMeta("composition")))return;let o=e.some(e=>e.docChanged)&&!t.doc.eq(n.doc),i=e.some(e=>e.getMeta("preventClearDocument"));if(!o||i)return;let{empty:s,from:a,to:l}=t.selection,d=r.LN.atStart(t.doc).from,u=r.LN.atEnd(t.doc).to;if(s||a!==d||l!==u||!eb(n.doc))return;let h=n.tr,f=c({state:n,transaction:h}),{commands:m}=new p({editor:this.editor,state:f});if(m.clearNodes(),h.steps.length)return h}})]}}),eA=F.create({name:"paste",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),ej=F.create({name:"tabindex",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class e${get name(){return this.node.type.name}constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!=(e=this.actualDepth)?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+ +!this.node.isText}get parent(){if(0===this.depth)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1);return new e$(this.resolvedPos.doc.resolve(e),this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new e$(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new e$(e,this.editor)}get children(){let e=[];return this.node.content.forEach((t,n)=>{let r=t.isBlock&&!t.isTextblock,o=t.isAtom&&!t.isText,i=this.pos+n+ +!o,s=this.resolvedPos.doc.resolve(i);if(!r&&s.depth<=this.depth)return;let a=new e$(s,this.editor,r,r?t:null);r&&(a.actualDepth=this.depth+1),e.push(new e$(s,this.editor,r,r?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e)if(Object.keys(t).length>0){let e=r.node.attrs,n=Object.keys(t);for(let r=0;r<n.length;r+=1){let o=n[r];if(e[o]!==t[o])break}}else n=r;r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;let o=Object.keys(t);return this.children.forEach(i=>{(!n||!(r.length>0))&&(i.node.type.name===e&&o.every(e=>t[e]===i.node.attrs[e])&&r.push(i),n&&r.length>0||(r=r.concat(i.querySelectorAll(e,t,n))))}),r}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}let eN=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;class eI extends u{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n)),this.on("paste",({event:e,slice:t})=>this.options.onPaste(e,t)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){let r=document.querySelector("style[data-tiptap-style]");if(null!==r)return r;let o=document.createElement("style");return t&&o.setAttribute("nonce",t),o.setAttribute("data-tiptap-style",""),o.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(o),o}(eN,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){let n=v(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(e){if(this.isDestroyed)return;let t=this.state.plugins,n=t;if([].concat(e).forEach(e=>{let t="string"==typeof e?`${e}$`:e.key;n=n.filter(e=>!e.key.startsWith(t))}),t.length===n.length)return;let r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var e,t;let n=[...this.options.enableCoreExtensions?[eS,K.configure({blockSeparator:null==(t=null==(e=this.options.coreExtensionOptions)?void 0:e.clipboardTextSerializer)?void 0:t.blockSeparator}),eO,eT,eP,ej,eE,eA].filter(e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name]):[],...this.options.extensions].filter(e=>["extension","node","mark"].includes(null==e?void 0:e.type));this.extensionManager=new z(n,this)}createCommandManager(){this.commandManager=new p({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=ea(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(e){if(!(e instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(e.message))throw e;this.emit("contentError",{editor:this,error:e,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(e=>"collaboration"!==e.name),this.createExtensionManager()}}),t=ea(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}let n=Z(t,this.options.autofocus);this.view=new o.Lz(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null==(e=this.options.editorProps)?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:r.$t.create({doc:t,selection:n||void 0})});let i=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(i),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;let t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(e=>{var t;return null==(t=this.capturedTransaction)?void 0:t.step(e)});return}let t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});let r=e.getMeta("focus"),o=e.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:e}),o&&this.emit("blur",{editor:this,event:o.event,transaction:e}),!e.docChanged||e.getMeta("preventUpdate")||this.emit("update",{editor:this,transaction:e})}getAttributes(e){return eu(this.state,e)}isActive(e,t){let n="string"==typeof e?e:null,r="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return eo(e,null,n)||eg(e,null,n);let r=ei(t,e.schema);return"node"===r?eo(e,t,n):"mark"===r&&eg(e,t,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return S(this.state.doc.content,this.schema)}getText(e){let{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){let n={from:0,to:e.content.size};return _(e,n,t)}(this.state.doc,{blockSeparator:t,textSerializers:{...H(this.schema),...n}})}get isEmpty(){return eb(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){let e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(null==(e=this.view)?void 0:e.docView)}$node(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelectorAll(e,t))||null}$pos(e){return new e$(this.state.doc.resolve(e),this)}get $doc(){return this.$pos(0)}}function eB(e){return new P({find:e.find,handler:({state:t,range:n,match:r})=>{let o=k(e.getAttributes,void 0,r);if(!1===o||null===o)return null;let{tr:i}=t,s=r[r.length-1],a=r[0];if(s){let r=a.search(/\S/),l=n.from+a.indexOf(s),d=l+s.length;if(ef(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>l).length)return null;d<n.to&&i.delete(d,n.to),l>n.from&&i.delete(n.from+r,l);let c=n.from+r+s.length;i.addMark(n.from+r,c,e.type.create(o||{})),i.removeStoredMark(e.type)}}})}function eR(e){return new P({find:e.find,handler:({state:t,range:n,match:r})=>{let o=k(e.getAttributes,void 0,r)||{},{tr:i}=t,s=n.from,a=n.to,l=e.type.create(o);if(r[1]){let e=s+r[0].lastIndexOf(r[1]);e>a?e=a:a=e+r[1].length;let t=r[0][r[0].length-1];i.insertText(t,s+r[0].length-1),i.replaceWith(e,a,l)}else if(r[0]){let t=e.type.isInline?s:s-1;i.insert(t,e.type.create(o)).delete(i.mapping.map(s),i.mapping.map(a))}i.scrollIntoView()}})}function eD(e){return new P({find:e.find,handler:({state:t,range:n,match:r})=>{let o=t.doc.resolve(n.from),i=k(e.getAttributes,void 0,r)||{};if(!o.node(-1).canReplaceWith(o.index(-1),o.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,i)}})}function eL(e){return new P({find:e.find,handler:({state:t,range:n,match:r,chain:o})=>{let i=k(e.getAttributes,void 0,r)||{},s=t.tr.delete(n.from,n.to),l=s.doc.resolve(n.from).blockRange(),d=l&&(0,a.oM)(l,e.type,i);if(!d)return null;if(s.wrap(l,d),e.keepMarks&&e.editor){let{selection:n,storedMarks:r}=t,{splittableMarks:o}=e.editor.extensionManager,i=r||n.$to.parentOffset&&n.$from.marks();if(i){let e=i.filter(e=>o.includes(e.type.name));s.ensureMarks(e)}}if(e.keepAttributes){let t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";o().updateAttributes(t,i).run()}let c=s.doc.resolve(n.from-1).nodeBefore;c&&c.type===e.type&&(0,a.n9)(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(r,c))&&s.join(n.from-1)}})}class ez{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=k(h(this,"addOptions",{name:this.name}))),this.storage=k(h(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new ez(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>N(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new ez(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=k(h(t,"addOptions",{name:t.name})),t.storage=k(h(t,"addStorage",{name:t.name,options:t.options})),t}}function eF(e){return new B({find:e.find,handler:({state:t,range:n,match:r,pasteEvent:o})=>{let i=k(e.getAttributes,void 0,r,o);if(!1===i||null===i)return null;let{tr:s}=t,a=r[r.length-1],l=r[0],d=n.to;if(a){let r=l.search(/\S/),o=n.from+l.indexOf(a),c=o+a.length;if(ef(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>o).length)return null;c<n.to&&s.delete(c,n.to),o>n.from&&s.delete(n.from+r,o),d=n.from+r+a.length,s.addMark(n.from+r,d,e.type.create(i||{})),s.removeStoredMark(e.type)}}})}}}]);