(()=>{var e={};e.id=229,e.ids=[229],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30976:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{POST:()=>u});var n=r(96559),o=r(48088),s=r(37719),i=r(32190),c=r(93356);async function u(e){try{let{topic:t,duration:r,style:a,targetAudience:n}=await e.json();if(!t)return i.NextResponse.json({error:"Topic is required"},{status:400});let o=new c.p;console.log("\uD83C\uDFA5 Generating YouTube script...");let s=await o.generateYouTubeScript(t,r||"5-10 minutes",a||"educational",n||"general audience");return console.log("✅ YouTube script generated successfully"),i.NextResponse.json({success:!0,content:s})}catch(e){return console.error("YouTube script generation error:",e),i.NextResponse.json({error:"Failed to generate YouTube script"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/generate/youtube/route",pathname:"/api/generate/youtube",filename:"route",bundlePath:"app/api/generate/youtube/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/generate/youtube/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:h}=l;function g(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},93356:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});let a=new(r(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class n{constructor(e="gemini-2.0-flash-lite"){this.model=a.getGenerativeModel({model:e})}async generateContent(e,t={}){try{let r={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40},a=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:r});return(await a.response).text()}catch(e){throw console.error("Gemini generation error:",e),Error("Failed to generate content with Gemini")}}async generateBlogPost(e,t,r,a,n){let o=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${r}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${n?.title?`Article Title: ${n.title}
`:""}
${n?.targetKeyword?`Target Keyword: ${n.targetKeyword} (use naturally throughout the content)
`:""}
${n?.targetAudience?`Target Audience: ${n.targetAudience} (tailor content for this audience)
`:""}
${n?.competitors?`Competitors to outperform: ${n.competitors} (create content that surpasses these sources)
`:""}

${a?`Research Data to incorporate:
${a}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return this.generateContent(o,{temperature:.7,maxOutputTokens:8e3})}async generateEmail(e,t,r,a){let n=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${r}
Key Points to Include: ${a.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return this.generateContent(n,{temperature:.6,maxOutputTokens:1500})}async generateTweet(e,t,r=!0){let a=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${r}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${r?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return this.generateContent(a,{temperature:.8,maxOutputTokens:500})}async extractKeywords(e){let t=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return this.generateContent(t,{temperature:.1,maxOutputTokens:50})}async generateYouTubeScript(e,t,r,a){let n=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${r}
Target Audience: ${a}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return this.generateContent(n,{temperature:.7,maxOutputTokens:5e3})}async extractKeywordsFromContent(e){let t=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return this.generateContent(t,{temperature:.2,maxOutputTokens:200})}}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580,830],()=>r(30976));module.exports=a})();