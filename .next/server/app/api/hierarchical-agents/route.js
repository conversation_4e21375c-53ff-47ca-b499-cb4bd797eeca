/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/hierarchical-agents/route";
exports.ids = ["app/api/hierarchical-agents/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhierarchical-agents%2Froute&page=%2Fapi%2Fhierarchical-agents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhierarchical-agents%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhierarchical-agents%2Froute&page=%2Fapi%2Fhierarchical-agents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhierarchical-agents%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_hierarchical_agents_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/hierarchical-agents/route.ts */ \"(rsc)/./src/app/api/hierarchical-agents/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/hierarchical-agents/route\",\n        pathname: \"/api/hierarchical-agents\",\n        filename: \"route\",\n        bundlePath: \"app/api/hierarchical-agents/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/hierarchical-agents/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_hierarchical_agents_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhierarchical-agents%2Froute&page=%2Fapi%2Fhierarchical-agents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhierarchical-agents%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/hierarchical-agents/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/hierarchical-agents/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_agents_hierarchical_agent_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/agents/hierarchical-agent-system */ \"(rsc)/./src/lib/agents/hierarchical-agent-system.ts\");\n/**\n * API Route for Aayush Agent (Hierarchical Agent System)\n *\n * Endpoint to execute the Aayush agent workflow for intelligent content generation\n */ \n\nasync function POST(request) {\n    try {\n        const { topic, options = {} } = await request.json();\n        if (!topic || typeof topic !== 'string' || topic.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Topic is required and must be a non-empty string'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`🚀 Starting Aayush agent workflow for topic: \"${topic}\"`);\n        // Progress tracking\n        const progressLogs = [];\n        const onProgress = (progress)=>{\n            progressLogs.push(progress);\n            console.log(`📊 [Aayush:${progress.agent}] ${progress.phase}: ${progress.message} (${progress.progress}%)`);\n        };\n        // Initialize supervisor with all agents\n        const supervisor = new _lib_agents_hierarchical_agent_system__WEBPACK_IMPORTED_MODULE_1__.SupervisorAgent(topic, onProgress, {\n            maxRetries: options.maxRetries || 3,\n            timeoutMs: options.timeoutMs || 300000,\n            enableDetailedLogging: true\n        });\n        // Execute the complete workflow\n        const result = await supervisor.executeWorkflow();\n        if (result.success) {\n            console.log(`✅ Workflow completed successfully in ${result.executionTime}ms`);\n            // Safely extract metadata with fallbacks\n            const metadata = result.memory?.metadata || {};\n            const agentLogs = metadata.agentLogs || [];\n            const completedPhases = metadata.completedPhases || [];\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                content: result.content,\n                metadata: {\n                    topic,\n                    executionTime: result.executionTime,\n                    progressLogs: progressLogs.slice(-10),\n                    memorySize: agentLogs.length,\n                    completedPhases: completedPhases,\n                    errors: result.errors || []\n                }\n            });\n        } else {\n            console.error(`❌ Workflow failed:`, result.errors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Workflow execution failed',\n                details: result.errors || [],\n                metadata: {\n                    topic,\n                    executionTime: result.executionTime,\n                    progressLogs: progressLogs.slice(-10),\n                    errors: result.errors || []\n                }\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Hierarchical agent API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Aayush Agent API',\n        description: 'POST to this endpoint with a topic to start the Aayush agent workflow',\n        agent_name: 'Aayush',\n        version: '1.0.0',\n        usage: {\n            method: 'POST',\n            body: {\n                topic: 'string (required) - The topic to research and write about',\n                options: {\n                    maxRetries: 'number (optional) - Maximum retry attempts per agent (default: 3)',\n                    timeoutMs: 'number (optional) - Timeout in milliseconds (default: 300000)'\n                }\n            }\n        },\n        agents: [\n            'TopicAnalyst - Extracts keyword and analyzes topic with Qwen',\n            'PrimaryResearch - Conducts multi-query research with Google Search',\n            'GapAnalyst - Identifies content gaps using Qwen analysis',\n            'DeepResearch - Fills gaps with targeted research',\n            'ContentWriting - RAG-based content generation with Gemini'\n        ],\n        features: [\n            'Advanced hierarchical agent coordination',\n            'Shared memory system across all agents',\n            'Structured data with delimiters (no JSON)',\n            'Supervisor coordination and error handling',\n            'Progress tracking and detailed logging',\n            'Retry logic with exponential backoff',\n            'WYSIWYG editor integration',\n            'Real-time SEO analysis'\n        ]\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/hierarchical-agents/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/hierarchical-agent-system.ts":
/*!*****************************************************!*\
  !*** ./src/lib/agents/hierarchical-agent-system.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseAgent: () => (/* binding */ BaseAgent),\n/* harmony export */   ContentWritingAgent: () => (/* binding */ ContentWritingAgent),\n/* harmony export */   DeepResearchAgent: () => (/* binding */ DeepResearchAgent),\n/* harmony export */   GapAnalystAgent: () => (/* binding */ GapAnalystAgent),\n/* harmony export */   MemoryManager: () => (/* binding */ MemoryManager),\n/* harmony export */   PrimaryResearchAgent: () => (/* binding */ PrimaryResearchAgent),\n/* harmony export */   SupervisorAgent: () => (/* binding */ SupervisorAgent),\n/* harmony export */   TopicAnalystAgent: () => (/* binding */ TopicAnalystAgent)\n/* harmony export */ });\n/* harmony import */ var _gemini__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/* harmony import */ var _openrouter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../openrouter */ \"(rsc)/./src/lib/openrouter.ts\");\n/* harmony import */ var _search__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../search */ \"(rsc)/./src/lib/search.ts\");\n/**\n * Aayush Agent - Hierarchical Agent System\n *\n * An advanced supervisor-managed multi-agent system for content research and generation.\n * Features shared memory, structured data with delimiters, and coordinated execution.\n * Named \"Aayush\" - the intelligent content creation assistant.\n */ \n\n\nclass MemoryManager {\n    constructor(topic){\n        this.memory = {\n            topic,\n            topicAnalysis: '',\n            primaryResearch: '',\n            gapAnalysis: '',\n            deepResearch: '',\n            contentGeneration: '',\n            metadata: {\n                startTime: new Date(),\n                currentPhase: 'initialization',\n                completedPhases: [],\n                errors: [],\n                agentLogs: []\n            }\n        };\n    }\n    // Store data with structured delimiters\n    store(section, data) {\n        if (section === 'metadata') return; // Metadata handled separately\n        this.memory[section] = data;\n        this.logAction('MemoryManager', 'store', `Stored data in ${section}`);\n    }\n    // Retrieve data from memory\n    retrieve(section) {\n        if (section === 'metadata') return JSON.stringify(this.memory.metadata);\n        return this.memory[section] || '';\n    }\n    // Get all research data for RAG\n    getAllResearchData() {\n        const sections = [\n            this.memory.topicAnalysis,\n            this.memory.primaryResearch,\n            this.memory.gapAnalysis,\n            this.memory.deepResearch\n        ].filter((section)=>section.length > 0);\n        return sections.join('\\n\\n===MEMORY_SEPARATOR===\\n\\n');\n    }\n    // Update current phase\n    updatePhase(phase) {\n        if (this.memory.metadata.currentPhase !== 'initialization') {\n            this.memory.metadata.completedPhases.push(this.memory.metadata.currentPhase);\n        }\n        this.memory.metadata.currentPhase = phase;\n        this.logAction('MemoryManager', 'updatePhase', `Phase updated to: ${phase}`);\n    }\n    // Log agent actions\n    logAction(agent, action, result) {\n        this.memory.metadata.agentLogs.push({\n            agent,\n            timestamp: new Date(),\n            action,\n            result: result.substring(0, 200) // Truncate for memory efficiency\n        });\n    }\n    // Log errors\n    logError(error) {\n        this.memory.metadata.errors.push(`${new Date().toISOString()}: ${error}`);\n    }\n    // Get memory summary\n    getSummary() {\n        const { metadata } = this.memory;\n        return `\n===MEMORY_SUMMARY===\nTopic: ${this.memory.topic}\nCurrent Phase: ${metadata.currentPhase}\nCompleted Phases: ${metadata.completedPhases.join(', ')}\nTotal Logs: ${metadata.agentLogs.length}\nErrors: ${metadata.errors.length}\nRuntime: ${Date.now() - metadata.startTime.getTime()}ms\n===END_SUMMARY===\n    `.trim();\n    }\n}\n// ============================================================================\n// BASE AGENT CLASS\n// ============================================================================\nclass BaseAgent {\n    constructor(name, memory, onProgress){\n        this.name = name;\n        this.memory = memory;\n        this.onProgress = onProgress;\n    }\n    updateProgress(phase, step, progress, message) {\n        if (this.onProgress) {\n            this.onProgress({\n                agent: this.name,\n                phase,\n                step,\n                progress,\n                message,\n                timestamp: new Date()\n            });\n        }\n    }\n    logAction(action, result) {\n        this.memory.logAction(this.name, action, result);\n    }\n    logError(error) {\n        this.memory.logError(`${this.name}: ${error}`);\n    }\n    // Parse structured data with delimiters\n    parseStructuredData(content, delimiter) {\n        const result = {};\n        try {\n            // Split by the main delimiter\n            const sections = content.split(`===${delimiter}===`);\n            if (sections.length < 2) {\n                console.log(`⚠️ No ${delimiter} sections found in content`);\n                return result;\n            }\n            // Process each section\n            for(let i = 1; i < sections.length; i += 2){\n                if (i + 1 < sections.length) {\n                    const key = sections[i].trim();\n                    let value = sections[i + 1];\n                    // Find the next delimiter to properly extract the value\n                    const nextDelimiterIndex = value.indexOf('===');\n                    if (nextDelimiterIndex !== -1) {\n                        value = value.substring(0, nextDelimiterIndex);\n                    }\n                    result[key] = value.trim();\n                }\n            }\n            console.log(`🔍 Parsed ${Object.keys(result).length} keys from ${delimiter}:`, Object.keys(result));\n        } catch (error) {\n            console.error(`❌ Error parsing structured data for ${delimiter}:`, error);\n        }\n        return result;\n    }\n    // Create structured data with delimiters\n    createStructuredData(data, prefix = 'DATA') {\n        let result = `===${prefix}_START===\\n`;\n        for (const [key, value] of Object.entries(data)){\n            result += `===${key.toUpperCase()}===\\n${value}\\n`;\n        }\n        result += `===${prefix}_END===`;\n        return result;\n    }\n}\n// ============================================================================\n// TOPIC ANALYST AGENT\n// ============================================================================\nclass TopicAnalystAgent extends BaseAgent {\n    constructor(memory, onProgress){\n        super('TopicAnalyst', memory, onProgress);\n        this.qwen = new _openrouter__WEBPACK_IMPORTED_MODULE_1__.OpenRouterService();\n        this.searchService = new _search__WEBPACK_IMPORTED_MODULE_2__.GoogleSearchService();\n    }\n    async execute() {\n        this.updateProgress('topic-analysis', 'keyword-extraction', 10, 'Extracting single keyword from topic...');\n        const topic = this.memory.retrieve('topic');\n        // Step 1: Extract single keyword using Qwen\n        const keywordPrompt = `\nExtract the most important single keyword from this topic for Google search: \"${topic}\"\n\nRules:\n- Return only ONE keyword that best represents the core concept\n- Choose the most specific and searchable term\n- Do not include multiple words unless it's a compound term\n- Focus on the main subject or concept\n\nReturn using structured format:\n\n===KEYWORD===\n[single keyword here]\n\n===REASONING===\n[brief explanation of why this keyword was chosen]\n`;\n        try {\n            const keywordResult = await this.qwen.generateThinkingContent(keywordPrompt, {\n                temperature: 0.1,\n                maxTokens: 200\n            });\n            const keywordData = this.parseStructuredData(keywordResult, 'KEYWORD');\n            const keyword = keywordData.KEYWORD || topic.split(' ')[0];\n            this.logAction('keyword-extraction', `Extracted keyword: ${keyword}`);\n            this.updateProgress('topic-analysis', 'search-execution', 30, `Searching for top 5 pages with keyword: ${keyword}`);\n            // Step 2: Search and extract top 5 pages\n            const searchResults = await this.searchService.searchAndExtract(keyword, 5);\n            if (!searchResults.extractedContent.length) {\n                throw new Error(`No search results found for keyword: ${keyword}`);\n            }\n            this.updateProgress('topic-analysis', 'content-analysis', 60, 'Analyzing extracted content with Qwen...');\n            // Step 3: Analyze content with Qwen\n            const contentSummary = searchResults.extractedContent.map((item, index)=>`===PAGE_${index + 1}===\\nURL: ${item.url}\\nContent: ${item.content.substring(0, 800)}...`).join('\\n\\n');\n            const analysisPrompt = `\nAnalyze the following search results for the topic \"${topic}\" using keyword \"${keyword}\":\n\n${contentSummary}\n\nProvide comprehensive analysis using structured format:\n\n===MAIN_TOPIC===\n[refined main topic based on search results]\n\n===SUBTOPICS===\n[subtopic1]\n[subtopic2]\n[subtopic3]\n[subtopic4]\n[subtopic5]\n\n===KEY_INSIGHTS===\n[insight1]\n[insight2]\n[insight3]\n\n===CONTENT_PATTERNS===\n[pattern1]\n[pattern2]\n[pattern3]\n\n===RESEARCH_QUERIES===\n[query1 for primary research]\n[query2 for primary research]\n[query3 for primary research]\n[query4 for primary research]\n\n===ANALYSIS_SUMMARY===\n[comprehensive summary of findings and recommendations for next research phase]\n`;\n            const analysisResult = await this.qwen.generateAnalysisContent(analysisPrompt, contentSummary, {\n                temperature: 0.3,\n                maxTokens: 2000\n            });\n            this.updateProgress('topic-analysis', 'completion', 100, 'Topic analysis completed');\n            this.logAction('analysis-completion', 'Successfully analyzed topic and generated research queries');\n            // Store in memory\n            const finalResult = this.createStructuredData({\n                keyword,\n                search_results_count: searchResults.extractedContent.length,\n                analysis: analysisResult\n            }, 'TOPIC_ANALYSIS');\n            this.memory.store('topicAnalysis', finalResult);\n            return finalResult;\n        } catch (error) {\n            const errorMsg = `Topic analysis failed: ${error}`;\n            this.logError(errorMsg);\n            throw new Error(errorMsg);\n        }\n    }\n}\n// ============================================================================\n// PRIMARY RESEARCH AGENT\n// ============================================================================\nclass PrimaryResearchAgent extends BaseAgent {\n    constructor(memory, onProgress){\n        super('PrimaryResearch', memory, onProgress);\n        this.searchService = new _search__WEBPACK_IMPORTED_MODULE_2__.GoogleSearchService();\n    }\n    async execute() {\n        this.updateProgress('primary-research', 'query-extraction', 10, 'Extracting research queries from topic analysis...');\n        const topicAnalysis = this.memory.retrieve('topicAnalysis');\n        if (!topicAnalysis) {\n            throw new Error('Topic analysis not found in memory');\n        }\n        // Extract research queries from topic analysis\n        this.logAction('debug-parsing', `Topic analysis data length: ${topicAnalysis.length}`);\n        const analysisData = this.parseStructuredData(topicAnalysis, 'TOPIC_ANALYSIS');\n        this.logAction('debug-parsing', `Parsed keys: ${Object.keys(analysisData).join(', ')}`);\n        // Try to extract the analysis section which contains the research queries\n        let queriesSection = '';\n        if (analysisData.ANALYSIS) {\n            // Parse the nested analysis content\n            const nestedAnalysis = this.parseStructuredData(analysisData.ANALYSIS, 'RESEARCH_QUERIES');\n            queriesSection = nestedAnalysis.RESEARCH_QUERIES || '';\n            this.logAction('debug-parsing', `Found nested queries: ${queriesSection.length} chars`);\n        }\n        // If no queries found, try direct parsing\n        if (!queriesSection) {\n            const directParse = this.parseStructuredData(topicAnalysis, 'RESEARCH_QUERIES');\n            queriesSection = directParse.RESEARCH_QUERIES || '';\n            this.logAction('debug-parsing', `Found direct queries: ${queriesSection.length} chars`);\n        }\n        const queries = queriesSection.split('\\n').filter((q)=>q.trim().length > 0 && !q.includes('===') && !q.includes('[query')).slice(0, 4);\n        this.logAction('debug-parsing', `Final queries count: ${queries.length}`);\n        queries.forEach((q, i)=>this.logAction('debug-query', `Query ${i + 1}: ${q}`));\n        if (queries.length === 0) {\n            // Fallback: Generate basic research queries from the topic\n            const topic = this.memory.retrieve('topic');\n            const fallbackQueries = [\n                `${topic} overview`,\n                `${topic} benefits`,\n                `${topic} challenges`,\n                `${topic} future trends`\n            ];\n            this.logAction('fallback-queries', `No queries found in analysis, using fallback queries for topic: ${topic}`);\n            // Use fallback queries\n            const allResearchData = [];\n            // Execute fallback queries\n            for(let i = 0; i < fallbackQueries.length; i++){\n                const query = fallbackQueries[i].trim();\n                this.updateProgress('primary-research', 'search-execution', 30 + i * 15, `Searching: ${query}`);\n                try {\n                    const searchResults = await this.searchService.searchAndExtract(query, 5);\n                    allResearchData.push({\n                        query,\n                        results: searchResults.extractedContent\n                    });\n                    this.logAction('search-execution', `Query \"${query}\" returned ${searchResults.extractedContent.length} results`);\n                    // Brief pause to avoid rate limiting\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                } catch (error) {\n                    this.logError(`Search failed for query \"${query}\": ${error}`);\n                    continue;\n                }\n            }\n            this.updateProgress('primary-research', 'data-compilation', 90, 'Compiling research data...');\n            // Compile all research data with structured format\n            let compiledData = '===PRIMARY_RESEARCH_START===\\n';\n            allResearchData.forEach((queryData, queryIndex)=>{\n                compiledData += `===QUERY_${queryIndex + 1}===\\n${queryData.query}\\n`;\n                queryData.results.forEach((result, resultIndex)=>{\n                    compiledData += `===RESULT_${queryIndex + 1}_${resultIndex + 1}===\\n`;\n                    compiledData += `URL: ${result.url}\\n`;\n                    compiledData += `CONTENT: ${result.content.substring(0, 1500)}...\\n`;\n                });\n            });\n            compiledData += '===PRIMARY_RESEARCH_END===';\n            this.updateProgress('primary-research', 'completion', 100, 'Primary research completed with fallback queries');\n            this.logAction('research-completion', `Compiled research from ${allResearchData.length} fallback queries`);\n            // Store in memory\n            this.memory.store('primaryResearch', compiledData);\n            return compiledData;\n        }\n        this.logAction('query-extraction', `Extracted ${queries.length} research queries`);\n        this.updateProgress('primary-research', 'multi-search', 30, `Executing ${queries.length} research queries...`);\n        const allResearchData = [];\n        // Execute each query and get top 5 pages\n        for(let i = 0; i < queries.length; i++){\n            const query = queries[i].trim();\n            this.updateProgress('primary-research', 'search-execution', 30 + i * 15, `Searching: ${query}`);\n            try {\n                const searchResults = await this.searchService.searchAndExtract(query, 5);\n                allResearchData.push({\n                    query,\n                    results: searchResults.extractedContent\n                });\n                this.logAction('search-execution', `Query \"${query}\" returned ${searchResults.extractedContent.length} results`);\n                // Brief pause to avoid rate limiting\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            } catch (error) {\n                this.logError(`Search failed for query \"${query}\": ${error}`);\n                continue;\n            }\n        }\n        this.updateProgress('primary-research', 'data-compilation', 90, 'Compiling research data...');\n        // Compile all research data with structured format\n        let compiledData = '===PRIMARY_RESEARCH_START===\\n';\n        allResearchData.forEach((queryData, queryIndex)=>{\n            compiledData += `===QUERY_${queryIndex + 1}===\\n${queryData.query}\\n`;\n            queryData.results.forEach((result, resultIndex)=>{\n                compiledData += `===RESULT_${queryIndex + 1}_${resultIndex + 1}===\\n`;\n                compiledData += `URL: ${result.url}\\n`;\n                compiledData += `CONTENT: ${result.content.substring(0, 1500)}...\\n`;\n            });\n        });\n        compiledData += '===PRIMARY_RESEARCH_END===';\n        this.updateProgress('primary-research', 'completion', 100, 'Primary research completed');\n        this.logAction('research-completion', `Compiled research from ${allResearchData.length} queries`);\n        // Store in memory\n        this.memory.store('primaryResearch', compiledData);\n        return compiledData;\n    }\n}\n// ============================================================================\n// GAP ANALYST AGENT\n// ============================================================================\nclass GapAnalystAgent extends BaseAgent {\n    constructor(memory, onProgress){\n        super('GapAnalyst', memory, onProgress);\n        this.qwen = new _openrouter__WEBPACK_IMPORTED_MODULE_1__.OpenRouterService();\n    }\n    async execute() {\n        this.updateProgress('gap-analysis', 'data-retrieval', 10, 'Retrieving research data from memory...');\n        const topicAnalysis = this.memory.retrieve('topicAnalysis');\n        const primaryResearch = this.memory.retrieve('primaryResearch');\n        if (!topicAnalysis || !primaryResearch) {\n            throw new Error('Required data not found in memory for gap analysis');\n        }\n        this.updateProgress('gap-analysis', 'content-analysis', 30, 'Analyzing content gaps with Qwen...');\n        const gapAnalysisPrompt = `\nAnalyze the research data to identify content gaps and areas that need deeper research.\n\nTOPIC ANALYSIS:\n${topicAnalysis}\n\nPRIMARY RESEARCH DATA:\n${primaryResearch}\n\nIdentify gaps, missing information, and areas that need deeper research using structured format:\n\n===CONTENT_GAPS===\n[gap1: description of missing content area]\n[gap2: description of missing content area]\n[gap3: description of missing content area]\n\n===MISSING_PERSPECTIVES===\n[perspective1: what viewpoint is missing]\n[perspective2: what viewpoint is missing]\n[perspective3: what viewpoint is missing]\n\n===DEPTH_NEEDED===\n[area1: topic that needs deeper coverage]\n[area2: topic that needs deeper coverage]\n[area3: topic that needs deeper coverage]\n\n===DEEP_RESEARCH_QUERIES===\n[query1 for deep research to fill gaps]\n[query2 for deep research to fill gaps]\n[query3 for deep research to fill gaps]\n\n===GAP_ANALYSIS_SUMMARY===\n[comprehensive summary of gaps found and recommendations for deep research phase]\n\n===COVERAGE_ASSESSMENT===\n[assessment of how well the current research covers the topic - percentage and explanation]\n`;\n        try {\n            const gapAnalysisResult = await this.qwen.generateAnalysisContent(gapAnalysisPrompt, '', {\n                temperature: 0.2,\n                maxTokens: 2000\n            });\n            this.updateProgress('gap-analysis', 'completion', 100, 'Gap analysis completed');\n            this.logAction('gap-analysis-completion', 'Successfully identified content gaps and deep research needs');\n            // Store in memory\n            const finalResult = this.createStructuredData({\n                analysis: gapAnalysisResult,\n                timestamp: new Date().toISOString()\n            }, 'GAP_ANALYSIS');\n            this.memory.store('gapAnalysis', finalResult);\n            return finalResult;\n        } catch (error) {\n            const errorMsg = `Gap analysis failed: ${error}`;\n            this.logError(errorMsg);\n            throw new Error(errorMsg);\n        }\n    }\n}\n// ============================================================================\n// DEEP RESEARCH AGENT\n// ============================================================================\nclass DeepResearchAgent extends BaseAgent {\n    constructor(memory, onProgress){\n        super('DeepResearch', memory, onProgress);\n        this.searchService = new _search__WEBPACK_IMPORTED_MODULE_2__.GoogleSearchService();\n    }\n    async execute() {\n        this.updateProgress('deep-research', 'gap-analysis-review', 10, 'Reviewing gap analysis for deep research needs...');\n        const gapAnalysis = this.memory.retrieve('gapAnalysis');\n        if (!gapAnalysis) {\n            throw new Error('Gap analysis not found in memory');\n        }\n        // Extract deep research queries from gap analysis\n        const gapData = this.parseStructuredData(gapAnalysis, 'GAP_ANALYSIS');\n        // Try to extract the analysis section which contains the deep research queries\n        let queriesSection = '';\n        if (gapData.ANALYSIS) {\n            // Parse the nested analysis content\n            const nestedAnalysis = this.parseStructuredData(gapData.ANALYSIS, 'DEEP_RESEARCH_QUERIES');\n            queriesSection = nestedAnalysis.DEEP_RESEARCH_QUERIES || '';\n        }\n        // If no queries found, try direct parsing\n        if (!queriesSection) {\n            const directParse = this.parseStructuredData(gapAnalysis, 'DEEP_RESEARCH_QUERIES');\n            queriesSection = directParse.DEEP_RESEARCH_QUERIES || '';\n        }\n        const deepQueries = queriesSection.split('\\n').filter((q)=>q.trim().length > 0 && !q.includes('===') && !q.includes('[query')).slice(0, 3);\n        if (deepQueries.length === 0) {\n            this.logAction('deep-research-skip', 'No deep research queries found, skipping deep research');\n            return this.createStructuredData({\n                status: 'skipped',\n                reason: 'No deep research needed based on gap analysis'\n            }, 'DEEP_RESEARCH');\n        }\n        this.updateProgress('deep-research', 'targeted-search', 30, `Executing ${deepQueries.length} targeted deep research queries...`);\n        const deepResearchData = [];\n        // Execute deep research queries\n        for(let i = 0; i < deepQueries.length; i++){\n            const query = deepQueries[i].trim();\n            this.updateProgress('deep-research', 'search-execution', 30 + i * 20, `Deep search: ${query}`);\n            try {\n                const searchResults = await this.searchService.searchAndExtract(query, 3);\n                deepResearchData.push({\n                    query,\n                    results: searchResults.extractedContent\n                });\n                this.logAction('deep-search-execution', `Deep query \"${query}\" returned ${searchResults.extractedContent.length} results`);\n                // Brief pause to avoid rate limiting\n                await new Promise((resolve)=>setTimeout(resolve, 1200));\n            } catch (error) {\n                this.logError(`Deep search failed for query \"${query}\": ${error}`);\n                continue;\n            }\n        }\n        this.updateProgress('deep-research', 'data-compilation', 90, 'Compiling deep research data...');\n        // Compile deep research data with structured format\n        let compiledData = '===DEEP_RESEARCH_START===\\n';\n        deepResearchData.forEach((queryData, queryIndex)=>{\n            compiledData += `===DEEP_QUERY_${queryIndex + 1}===\\n${queryData.query}\\n`;\n            queryData.results.forEach((result, resultIndex)=>{\n                compiledData += `===DEEP_RESULT_${queryIndex + 1}_${resultIndex + 1}===\\n`;\n                compiledData += `URL: ${result.url}\\n`;\n                compiledData += `CONTENT: ${result.content.substring(0, 1200)}...\\n`;\n            });\n        });\n        compiledData += '===DEEP_RESEARCH_END===';\n        this.updateProgress('deep-research', 'completion', 100, 'Deep research completed');\n        this.logAction('deep-research-completion', `Compiled deep research from ${deepResearchData.length} targeted queries`);\n        // Store in memory\n        this.memory.store('deepResearch', compiledData);\n        return compiledData;\n    }\n}\n// ============================================================================\n// CONTENT WRITING AGENT\n// ============================================================================\nclass ContentWritingAgent extends BaseAgent {\n    constructor(memory, onProgress){\n        super('ContentWriting', memory, onProgress);\n        this.gemini = new _gemini__WEBPACK_IMPORTED_MODULE_0__.GeminiService();\n        this.searchService = new _search__WEBPACK_IMPORTED_MODULE_2__.GoogleSearchService();\n    }\n    async execute() {\n        this.updateProgress('content-writing', 'keyword-search', 10, 'Performing keyword search for writing style analysis...');\n        const topic = this.memory.retrieve('topic');\n        // Step 1: Extract keyword and search for top 5 pages for style analysis\n        const keyword = await this.extractKeywordFromTopic(topic);\n        const styleAnalysisResults = await this.searchService.searchAndExtract(keyword, 5);\n        this.updateProgress('content-writing', 'style-analysis', 30, 'Analyzing writing styles from top 5 pages...');\n        // Step 2: Analyze writing styles\n        const writingStyleAnalysis = await this.analyzeWritingStyles(styleAnalysisResults.extractedContent);\n        this.updateProgress('content-writing', 'rag-preparation', 50, 'Preparing RAG knowledge base from memory...');\n        // Step 3: Prepare RAG knowledge base from all memory data\n        const knowledgeBase = this.memory.getAllResearchData();\n        this.updateProgress('content-writing', 'content-generation', 70, 'Generating content using RAG approach...');\n        // Step 4: Generate content using RAG approach\n        const generatedContent = await this.generateContentWithRAG(topic, knowledgeBase, writingStyleAnalysis);\n        this.updateProgress('content-writing', 'completion', 100, 'Content generation completed');\n        // Debug: Log the generated content\n        console.log('📝 Generated content length:', generatedContent.length);\n        console.log('📝 Generated content preview:', generatedContent.substring(0, 200));\n        // Store in memory\n        const finalResult = this.createStructuredData({\n            keyword_used: keyword,\n            style_analysis: writingStyleAnalysis,\n            generated_content: generatedContent,\n            knowledge_base_size: knowledgeBase.length\n        }, 'CONTENT_GENERATION');\n        console.log('📝 Final result length:', finalResult.length);\n        console.log('📝 Final result preview:', finalResult.substring(0, 300));\n        this.memory.store('contentGeneration', finalResult);\n        return finalResult;\n    }\n    async extractKeywordFromTopic(topic) {\n        try {\n            const keyword = await this.gemini.extractKeywords(topic);\n            this.logAction('keyword-extraction', `Extracted keyword: ${keyword}`);\n            return keyword.trim();\n        } catch (error) {\n            this.logError(`Keyword extraction failed: ${error}`);\n            return topic.split(' ')[0] // Fallback to first word\n            ;\n        }\n    }\n    async analyzeWritingStyles(pages) {\n        const contentSummary = pages.map((page, index)=>`===STYLE_PAGE_${index + 1}===\\nURL: ${page.url}\\nContent: ${page.content.substring(0, 1000)}...`).join('\\n\\n');\n        const styleAnalysisPrompt = `\nAnalyze the writing styles from these top 5 pages to understand how to write in a human, engaging way:\n\n${contentSummary}\n\nProvide analysis using structured format:\n\n===WRITING_TONE===\n[description of the overall tone used across these pages]\n\n===SENTENCE_STRUCTURE===\n[analysis of sentence patterns and structure]\n\n===ENGAGEMENT_TECHNIQUES===\n[techniques used to engage readers]\n\n===CONTENT_ORGANIZATION===\n[how content is typically organized and structured]\n\n===STYLE_RECOMMENDATIONS===\n[specific recommendations for mimicking this human writing style]\n`;\n        try {\n            return await this.gemini.generateContent(styleAnalysisPrompt, {\n                temperature: 0.3,\n                maxOutputTokens: 1500\n            });\n        } catch (error) {\n            this.logError(`Style analysis failed: ${error}`);\n            return 'Style analysis unavailable - using default professional writing approach';\n        }\n    }\n    async generateContentWithRAG(topic, knowledgeBase, styleAnalysis) {\n        const ragPrompt = `\nYou are a world-class content writer using RAG (Retrieval-Augmented Generation) approach. Create a comprehensive, engaging article about \"${topic}\".\n\nKNOWLEDGE BASE (from research):\n${knowledgeBase.substring(0, 8000)} // Limit to prevent token overflow\n\nWRITING STYLE ANALYSIS:\n${styleAnalysis}\n\nCONTENT REQUIREMENTS:\n- Write in clean, valid markdown format\n- Use # for main title, ## for major sections, ### for subsections\n- Create engaging, human-like content that mimics the analyzed writing style\n- Include all important details from the knowledge base\n- Make this a comprehensive, go-to article for the topic\n- Use the research data to support all claims and statements\n- Include relevant examples, statistics, and insights from the knowledge base\n- Write in a way that feels natural and human, not AI-generated\n- Target 2000+ words for comprehensive coverage\n- Include compelling introduction and strong conclusion\n\nSTRUCTURE:\n1. Compelling title and introduction\n2. Well-organized sections with clear headings\n3. Use of bullet points and numbered lists where appropriate\n4. Integration of research findings throughout\n5. Practical insights and actionable advice\n6. Strong conclusion with key takeaways\n\nCreate content that demonstrates deep expertise and provides exceptional value to readers. Use the knowledge base extensively to support your writing.\n\nReturn ONLY the markdown content, no additional text or formatting.\n`;\n        try {\n            return await this.gemini.generateContent(ragPrompt, {\n                temperature: 0.7,\n                maxOutputTokens: 8000\n            });\n        } catch (error) {\n            this.logError(`Content generation failed: ${error}`);\n            throw new Error(`Content generation failed: ${error}`);\n        }\n    }\n}\nclass SupervisorAgent {\n    constructor(topic, onProgress, options = {}){\n        this.memory = new MemoryManager(topic);\n        this.onProgress = onProgress;\n        this.options = {\n            maxRetries: 3,\n            timeoutMs: 300000,\n            enableDetailedLogging: true,\n            ...options\n        };\n        // Initialize all agents with shared memory\n        this.agents = {\n            topicAnalyst: new TopicAnalystAgent(this.memory, onProgress),\n            primaryResearch: new PrimaryResearchAgent(this.memory, onProgress),\n            gapAnalyst: new GapAnalystAgent(this.memory, onProgress),\n            deepResearch: new DeepResearchAgent(this.memory, onProgress),\n            contentWriting: new ContentWritingAgent(this.memory, onProgress)\n        };\n    }\n    async executeWorkflow() {\n        const startTime = Date.now();\n        const errors = [];\n        try {\n            this.updateProgress('supervisor', 'initialization', 0, 'Starting hierarchical agent workflow...');\n            // Phase 1: Topic Analysis\n            this.memory.updatePhase('topic-analysis');\n            await this.executeWithRetry('topicAnalyst', 'Topic Analysis');\n            // Phase 2: Primary Research\n            this.memory.updatePhase('primary-research');\n            await this.executeWithRetry('primaryResearch', 'Primary Research');\n            // Phase 3: Gap Analysis\n            this.memory.updatePhase('gap-analysis');\n            await this.executeWithRetry('gapAnalyst', 'Gap Analysis');\n            // Phase 4: Deep Research\n            this.memory.updatePhase('deep-research');\n            await this.executeWithRetry('deepResearch', 'Deep Research');\n            // Phase 5: Content Writing\n            this.memory.updatePhase('content-writing');\n            const contentResult = await this.executeWithRetry('contentWriting', 'Content Writing');\n            // Extract final content\n            this.updateProgress('supervisor', 'content-extraction', 95, 'Extracting generated content...');\n            // Debug: Log the content result structure\n            console.log('🔍 Content result length:', contentResult.length);\n            console.log('🔍 Content result preview:', contentResult.substring(0, 200));\n            const contentData = this.parseStructuredData(contentResult, 'CONTENT_GENERATION');\n            console.log('🔍 Parsed content keys:', Object.keys(contentData));\n            // Try multiple extraction methods\n            let finalContent = contentData.GENERATED_CONTENT || contentData.generated_content;\n            // If still not found, try extracting from the raw content\n            if (!finalContent || finalContent === 'Content generation failed') {\n                // Try to extract content directly from the structured data\n                const allKeys = Object.keys(contentData);\n                console.log('🔍 All available keys:', allKeys);\n                // Look for any key that might contain the content\n                for (const key of allKeys){\n                    if (key.includes('CONTENT') || key.includes('GENERATED')) {\n                        finalContent = contentData[key];\n                        console.log(`🔍 Found content in key: ${key}`);\n                        break;\n                    }\n                }\n            }\n            // Final fallback: if still no content, return a meaningful message\n            if (!finalContent || finalContent.trim().length === 0) {\n                finalContent = 'Content generation completed but content extraction failed. Please check the logs.';\n                console.log('❌ Content extraction failed, using fallback message');\n            } else {\n                console.log('✅ Content successfully extracted, length:', finalContent.length);\n            }\n            this.memory.updatePhase('completed');\n            this.updateProgress('supervisor', 'completion', 100, 'Workflow completed successfully');\n            return {\n                success: true,\n                content: finalContent,\n                memory: {\n                    topic: this.memory.retrieve('topic'),\n                    topicAnalysis: this.memory.retrieve('topicAnalysis'),\n                    primaryResearch: this.memory.retrieve('primaryResearch'),\n                    gapAnalysis: this.memory.retrieve('gapAnalysis'),\n                    deepResearch: this.memory.retrieve('deepResearch'),\n                    contentGeneration: this.memory.retrieve('contentGeneration'),\n                    metadata: this.memory.memory?.metadata || {\n                        startTime: new Date(),\n                        currentPhase: 'completed',\n                        completedPhases: [],\n                        errors: [],\n                        agentLogs: []\n                    }\n                },\n                executionTime: Date.now() - startTime,\n                errors: this.memory.memory?.metadata?.errors || []\n            };\n        } catch (error) {\n            const errorMsg = `Workflow failed: ${error}`;\n            errors.push(errorMsg);\n            this.memory.logError(errorMsg);\n            return {\n                success: false,\n                content: '',\n                memory: {\n                    topic: this.memory.retrieve('topic'),\n                    topicAnalysis: this.memory.retrieve('topicAnalysis'),\n                    primaryResearch: this.memory.retrieve('primaryResearch'),\n                    gapAnalysis: this.memory.retrieve('gapAnalysis'),\n                    deepResearch: this.memory.retrieve('deepResearch'),\n                    contentGeneration: this.memory.retrieve('contentGeneration'),\n                    metadata: this.memory.memory?.metadata || {\n                        startTime: new Date(),\n                        currentPhase: 'failed',\n                        completedPhases: [],\n                        errors: [],\n                        agentLogs: []\n                    }\n                },\n                executionTime: Date.now() - startTime,\n                errors\n            };\n        }\n    }\n    async executeWithRetry(agentKey, phaseName) {\n        const agent = this.agents[agentKey];\n        let lastError = null;\n        for(let attempt = 1; attempt <= this.options.maxRetries; attempt++){\n            try {\n                this.updateProgress('supervisor', 'agent-execution', 0, `Executing ${phaseName} (attempt ${attempt})`);\n                const result = await Promise.race([\n                    agent.execute(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error('Agent execution timeout')), this.options.timeoutMs))\n                ]);\n                this.updateProgress('supervisor', 'agent-completion', 0, `${phaseName} completed successfully`);\n                return result;\n            } catch (error) {\n                lastError = error;\n                this.memory.logError(`${phaseName} attempt ${attempt} failed: ${error}`);\n                if (attempt < this.options.maxRetries) {\n                    const delay = Math.pow(2, attempt) * 1000 // Exponential backoff\n                    ;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        throw lastError || new Error(`${phaseName} failed after ${this.options.maxRetries} attempts`);\n    }\n    updateProgress(agent, phase, progress, message) {\n        if (this.onProgress) {\n            this.onProgress({\n                agent,\n                phase,\n                step: phase,\n                progress,\n                message,\n                timestamp: new Date()\n            });\n        }\n    }\n    parseStructuredData(content, delimiter) {\n        const result = {};\n        const sections = content.split(`===${delimiter}===`);\n        for(let i = 1; i < sections.length; i += 2){\n            if (i + 1 < sections.length) {\n                const key = sections[i].trim();\n                const value = sections[i + 1].split('===')[0].trim();\n                result[key] = value;\n            }\n        }\n        return result;\n    }\n    // Get memory summary for debugging\n    getMemorySummary() {\n        return this.memory.getSummary();\n    }\n    // Get specific memory section\n    getMemorySection(section) {\n        return this.memory.retrieve(section);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/hierarchical-agent-system.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(\"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\");\nclass GeminiService {\n    constructor(modelName = 'gemini-2.0-flash-lite'){\n        this.model = genAI.getGenerativeModel({\n            model: modelName\n        });\n    }\n    async generateContent(prompt, config = {}) {\n        try {\n            const generationConfig = {\n                temperature: config.temperature || 0.7,\n                maxOutputTokens: config.maxOutputTokens || 4000,\n                topP: config.topP || 0.95,\n                topK: config.topK || 40\n            };\n            const result = await this.model.generateContent({\n                contents: [\n                    {\n                        role: 'user',\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig\n            });\n            const response = await result.response;\n            return response.text();\n        } catch (error) {\n            console.error('Gemini generation error:', error);\n            throw new Error('Failed to generate content with Gemini');\n        }\n    }\n    async generateBlogPost(topic, wordCount, tone, researchData, competitionData) {\n        const prompt = `\nYou are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about \"${topic}\".\n\nCONTENT REQUIREMENTS:\n- Target word count: ${wordCount} words\n- Tone: ${tone}\n- Format: Professional markdown with proper headings, lists, and structure\n- Include compelling hook and engaging introduction\n- Use narrative storytelling and real-world examples\n- Include strategic call-to-action at the end\n- Write as a primary authoritative source\n- Use confident, authoritative language (avoid hedging)\n\nPROFESSIONAL WRITING STANDARDS:\n- Start with an attention-grabbing hook (question, statistic, or bold statement)\n- Create emotional connection with readers through storytelling\n- Use scannable formatting with headings, subheadings, and bullet points\n- Include actionable insights and practical advice\n- Incorporate relevant statistics and data points\n- Use active voice and strong verbs\n- Create smooth transitions between sections\n- End with a powerful conclusion and clear next steps\n\n${competitionData?.title ? `Article Title: ${competitionData.title}\\n` : ''}\n${competitionData?.targetKeyword ? `Target Keyword: ${competitionData.targetKeyword} (use naturally throughout the content)\\n` : ''}\n${competitionData?.targetAudience ? `Target Audience: ${competitionData.targetAudience} (tailor content for this audience)\\n` : ''}\n${competitionData?.competitors ? `Competitors to outperform: ${competitionData.competitors} (create content that surpasses these sources)\\n` : ''}\n\n${researchData ? `Research Data to incorporate:\\n${researchData}\\n` : ''}\n\nCONTENT STRUCTURE:\n1. Compelling Hook (question, statistic, or bold statement)\n2. Introduction with context and thesis\n3. Main sections with clear headings and subheadings\n4. Practical examples and case studies\n5. Actionable takeaways and recommendations\n6. Powerful conclusion with call-to-action\n\nCreate content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.7,\n            maxOutputTokens: 8000\n        });\n    }\n    async generateEmail(purpose, audience, tone, keyPoints) {\n        const prompt = `\nCreate a professional email for the following:\n\nPurpose: ${purpose}\nTarget Audience: ${audience}\nTone: ${tone}\nKey Points to Include: ${keyPoints.join(', ')}\n\nRequirements:\n- Include compelling subject line\n- Professional email structure (greeting, body, closing)\n- Clear call-to-action\n- Appropriate tone and language for the audience\n- Concise but comprehensive\n\nFormat the response as:\nSubject: [Subject Line]\n\n[Email Body]\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.6,\n            maxOutputTokens: 1500\n        });\n    }\n    async generateTweet(topic, style, includeHashtags = true) {\n        const prompt = `\nCreate an engaging Twitter/X tweet about \"${topic}\".\n\nStyle: ${style}\nInclude hashtags: ${includeHashtags}\n\nRequirements:\n- Maximum 280 characters\n- Engaging and shareable\n- Include relevant emojis if appropriate\n- ${includeHashtags ? 'Include 2-3 relevant hashtags' : 'No hashtags'}\n- Hook the reader's attention\n- Encourage engagement (likes, retweets, replies)\n\nCreate a tweet that stands out in the feed and drives engagement.\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.8,\n            maxOutputTokens: 500\n        });\n    }\n    async extractKeywords(topic) {\n        const prompt = `\nExtract the most important keywords from this topic for Google search: \"${topic}\"\n\nRequirements:\n- If the topic is a single word or simple phrase, use it as the main keyword\n- For complex topics, extract 3-5 key terms that best represent the topic\n- Focus on the main concepts and important terms\n- Use words that would be effective for Google search\n- Return only the keywords separated by spaces, nothing else\n- Do not include common words like \"the\", \"and\", \"of\", etc.\n- Do not add words like \"meaning\", \"definition\", \"example\" unless they are part of the original topic\n- Focus on specific, searchable terms from the original topic\n\nExamples:\nTopic: \"magistral\"\nKeywords: magistral\n\nTopic: \"How to build a React application with TypeScript\"\nKeywords: React TypeScript application build development\n\nTopic: \"artificial intelligence in healthcare\"\nKeywords: artificial intelligence healthcare\n\nReturn only the keywords:\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.1,\n            maxOutputTokens: 50\n        });\n    }\n    async generateYouTubeScript(topic, duration, style, targetAudience) {\n        const prompt = `\nCreate a YouTube video script about \"${topic}\".\n\nVideo Duration: ${duration}\nStyle: ${style}\nTarget Audience: ${targetAudience}\n\nRequirements:\n- Include compelling hook in first 15 seconds\n- Clear structure with timestamps\n- Engaging storytelling throughout\n- Include call-to-action for likes, subscribes, comments\n- Natural speaking rhythm and flow\n- Include cues for visuals/graphics where appropriate\n- End with strong conclusion and next video teaser\n\nFormat:\n[HOOK - 0:00-0:15]\n[INTRODUCTION - 0:15-0:45]\n[MAIN CONTENT - Sections with timestamps]\n[CONCLUSION & CTA - Final section]\n\nCreate a script that keeps viewers engaged throughout the entire video.\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.7,\n            maxOutputTokens: 5000\n        });\n    }\n    async extractKeywordsFromContent(content) {\n        const prompt = `\nAnalyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:\n\nContent:\n${content.substring(0, 3000)}\n\nRules:\n- Extract 8-12 high-value keywords and phrases\n- Focus on terms that appear frequently and seem important\n- Include both single keywords and 2-3 word phrases\n- Prioritize terms that would be good for SEO targeting\n- Separate keywords with commas\n- Don't include common words like \"the\", \"and\", \"or\", etc.\n\nReturn only the keywords separated by commas:\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.2,\n            maxOutputTokens: 200\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openrouter.ts":
/*!*******************************!*\
  !*** ./src/lib/openrouter.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterService: () => (/* binding */ OpenRouterService)\n/* harmony export */ });\n/**\n * OpenRouter Service for Qwen3-235B Model Integration\n * Specialized for thinking and reasoning tasks\n */ class OpenRouterService {\n    constructor(config){\n        this.defaultModel = 'qwen/qwen3-235b-a22b-04-28';\n        this.config = {\n            apiKey: \"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\" || 0,\n            baseURL: 'https://openrouter.ai/api/v1',\n            model: config?.model || this.defaultModel,\n            maxRetries: config?.maxRetries || 3,\n            timeout: config?.timeout || 60000,\n            ...config\n        };\n        if (!this.config.apiKey) {\n            throw new Error('OpenRouter API key is required. Set OPENROUTER_API_KEY environment variable.');\n        }\n    }\n    /**\n   * Generate content for thinking tasks using Qwen3-235B\n   */ async generateThinkingContent(prompt, options = {}) {\n        const messages = [\n            {\n                role: 'system',\n                content: this.buildThinkingSystemPrompt(options.thinkingDepth || 'medium')\n            },\n            {\n                role: 'user',\n                content: this.enhancePromptForThinking(prompt, options)\n            }\n        ];\n        const response = await this.makeRequest(messages, {\n            temperature: options.temperature || 0.7,\n            max_tokens: options.maxTokens || 4000,\n            top_p: options.topP || 0.9,\n            frequency_penalty: options.frequencyPenalty || 0.1,\n            presence_penalty: options.presencePenalty || 0.1\n        });\n        return response.choices[0]?.message?.content || '';\n    }\n    /**\n   * Generate content for analysis tasks\n   */ async generateAnalysisContent(prompt, context, options = {}) {\n        const messages = [\n            {\n                role: 'system',\n                content: `You are an expert analyst with deep reasoning capabilities. Provide comprehensive, well-structured analysis with clear reasoning and evidence-based conclusions.`\n            }\n        ];\n        if (context) {\n            messages.push({\n                role: 'user',\n                content: `Context: ${context}`\n            });\n        }\n        messages.push({\n            role: 'user',\n            content: prompt\n        });\n        const response = await this.makeRequest(messages, {\n            temperature: options.temperature || 0.3,\n            max_tokens: options.maxTokens || 4000,\n            top_p: options.topP || 0.9\n        });\n        return response.choices[0]?.message?.content || '';\n    }\n    /**\n   * Generate content for strategic planning tasks\n   */ async generateStrategyContent(prompt, requirements, options = {}) {\n        const messages = [\n            {\n                role: 'system',\n                content: `You are a strategic planning expert with advanced reasoning capabilities. Create comprehensive, actionable strategies with detailed implementation guidance.`\n            },\n            {\n                role: 'user',\n                content: `Requirements: ${requirements.join(', ')}\\n\\n${prompt}`\n            }\n        ];\n        const response = await this.makeRequest(messages, {\n            temperature: options.temperature || 0.5,\n            max_tokens: options.maxTokens || 5000,\n            top_p: options.topP || 0.9\n        });\n        return response.choices[0]?.message?.content || '';\n    }\n    /**\n   * Generate content for quality assessment tasks\n   */ async generateQualityAssessment(content, criteria, options = {}) {\n        const messages = [\n            {\n                role: 'system',\n                content: `You are a quality assessment expert with meticulous attention to detail. Provide thorough, objective evaluations with specific recommendations for improvement.`\n            },\n            {\n                role: 'user',\n                content: `Assess the following content against these criteria: ${criteria.join(', ')}\\n\\nContent to assess:\\n${content}`\n            }\n        ];\n        const response = await this.makeRequest(messages, {\n            temperature: options.temperature || 0.2,\n            max_tokens: options.maxTokens || 3000,\n            top_p: options.topP || 0.8\n        });\n        return response.choices[0]?.message?.content || '';\n    }\n    buildThinkingSystemPrompt(depth) {\n        const basePrompt = `You are Qwen3-235B, an advanced AI model with exceptional reasoning capabilities. You excel at deep thinking, analysis, and problem-solving.`;\n        const depthInstructions = {\n            shallow: `Provide clear, concise analysis with basic reasoning steps.`,\n            medium: `Think through problems systematically, showing your reasoning process and considering multiple perspectives.`,\n            deep: `Engage in deep, thorough analysis. Break down complex problems into components, consider multiple angles, evaluate evidence, and provide comprehensive reasoning with detailed explanations.`\n        };\n        return `${basePrompt}\\n\\n${depthInstructions[depth]}\\n\\nAlways structure your responses clearly and provide actionable insights.`;\n    }\n    enhancePromptForThinking(prompt, options) {\n        if (!options.enableThinking) {\n            return prompt;\n        }\n        return `<thinking>\nLet me think through this systematically:\n\n1. Understanding the task and requirements\n2. Analyzing the key components and relationships\n3. Considering different approaches and perspectives\n4. Evaluating potential solutions and outcomes\n5. Synthesizing insights into actionable recommendations\n</thinking>\n\n${prompt}\n\nPlease provide a comprehensive response with clear reasoning and detailed analysis.`;\n    }\n    async makeRequest(messages, parameters) {\n        const requestBody = {\n            model: this.config.model,\n            messages,\n            ...parameters\n        };\n        let lastError = null;\n        for(let attempt = 1; attempt <= this.config.maxRetries; attempt++){\n            try {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), this.config.timeout);\n                const response = await fetch(`${this.config.baseURL}/chat/completions`, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${this.config.apiKey}`,\n                        'Content-Type': 'application/json',\n                        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n                        'X-Title': 'AI Content System'\n                    },\n                    body: JSON.stringify(requestBody),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);\n                }\n                const data = await response.json();\n                return data;\n            } catch (error) {\n                lastError = error instanceof Error ? error : new Error('Unknown error');\n                console.error(`OpenRouter request attempt ${attempt} failed:`, lastError.message);\n                if (attempt < this.config.maxRetries) {\n                    // Exponential backoff\n                    const delay = Math.pow(2, attempt) * 1000;\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        throw lastError || new Error('All OpenRouter request attempts failed');\n    }\n    /**\n   * Test the connection to OpenRouter\n   */ async testConnection() {\n        try {\n            const response = await this.generateThinkingContent('Test connection. Respond with \"Connection successful\" if you can read this.', {\n                maxTokens: 50,\n                temperature: 0.1\n            });\n            return response.toLowerCase().includes('connection successful');\n        } catch (error) {\n            console.error('OpenRouter connection test failed:', error);\n            return false;\n        }\n    }\n    /**\n   * Get model information\n   */ getModelInfo() {\n        return {\n            model: this.config.model,\n            capabilities: [\n                'Advanced reasoning and thinking',\n                'Complex problem solving',\n                'Strategic analysis',\n                'Quality assessment',\n                'Multi-perspective evaluation',\n                'Detailed explanations'\n            ]\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openrouter.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/search.ts":
/*!***************************!*\
  !*** ./src/lib/search.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleSearchService: () => (/* binding */ GoogleSearchService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n\nclass GoogleSearchService {\n    constructor(){\n        this.apiKey = \"AIzaSyBlQ7HhWbY37GYbeV9ZJZmTUucspF2KbXE\";\n        this.searchEngineId = \"830840f1a0eaf4acf\";\n    }\n    async search(query, numResults = 10) {\n        try {\n            console.log(`🔍 Searching for: ${query}`);\n            // Check if API keys are configured\n            if (!this.apiKey || !this.searchEngineId) {\n                console.error('❌ Google Search API credentials not configured');\n                throw new Error('Google Search API credentials not configured');\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get('https://www.googleapis.com/customsearch/v1', {\n                params: {\n                    key: this.apiKey,\n                    cx: this.searchEngineId,\n                    q: query,\n                    num: Math.min(numResults, 10)\n                },\n                timeout: 15000\n            });\n            const results = response.data.items?.map((item)=>({\n                    title: item.title,\n                    link: item.link,\n                    snippet: item.snippet,\n                    displayLink: item.displayLink\n                })) || [];\n            console.log(`📊 Found ${results.length} results`);\n            if (results.length === 0) {\n                console.log(`⚠️ No results found for query: \"${query}\"`);\n                console.log(`📊 Total results available: ${response.data.searchInformation?.totalResults || '0'}`);\n            } else {\n                results.forEach((result, index)=>{\n                    console.log(`${index + 1}. ${result.link}`);\n                });\n            }\n            return {\n                items: results,\n                searchInformation: {\n                    totalResults: response.data.searchInformation?.totalResults || '0',\n                    searchTime: response.data.searchInformation?.searchTime || 0\n                }\n            };\n        } catch (error) {\n            console.error('Google Search API error:', error.response?.data || error.message);\n            // Check for specific API errors\n            if (error.response?.status === 403) {\n                console.error('❌ API key invalid or quota exceeded');\n            } else if (error.response?.status === 400) {\n                console.error('❌ Invalid search parameters');\n            }\n            throw new Error(`Failed to perform search: ${error.response?.data?.error?.message || error.message}`);\n        }\n    }\n    async extractContent(url) {\n        try {\n            console.log(`📄 Extracting content from: ${url}`);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(url, {\n                timeout: 10000,\n                headers: {\n                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n                }\n            });\n            const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(response.data);\n            // Remove unwanted elements\n            $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();\n            // Extract main content\n            let content = '';\n            // Try common content selectors\n            const contentSelectors = [\n                'article',\n                '.content',\n                '.post-content',\n                '.entry-content',\n                '.article-content',\n                'main',\n                '.main-content',\n                '#content',\n                '.post-body',\n                '.article-body'\n            ];\n            for (const selector of contentSelectors){\n                const element = $(selector);\n                if (element.length > 0 && element.text().trim().length > content.length) {\n                    content = element.text().trim();\n                }\n            }\n            // Fallback to body if no content found\n            if (!content) {\n                content = $('body').text().trim();\n            }\n            // Clean up the content\n            content = content.replace(/\\s+/g, ' ').replace(/\\n\\s*\\n/g, '\\n').trim();\n            console.log(`✅ Extracted ${content.length} characters from ${url}`);\n            return content;\n        } catch (error) {\n            console.error(`❌ Failed to extract content from ${url}:`, error);\n            return '';\n        }\n    }\n    async searchAndExtract(query, numResults = 5) {\n        try {\n            // Perform search\n            const searchResponse = await this.search(query, numResults);\n            // Extract content from URLs in parallel\n            const extractionPromises = searchResponse.items.map(async (result)=>({\n                    url: result.link,\n                    content: await this.extractContent(result.link)\n                }));\n            const extractedContent = await Promise.all(extractionPromises);\n            // Filter out empty content\n            const validContent = extractedContent.filter((item)=>item.content.length > 100);\n            console.log(`📚 Successfully extracted content from ${validContent.length}/${searchResponse.items.length} URLs`);\n            return {\n                searchResults: searchResponse.items,\n                extractedContent: validContent\n            };\n        } catch (error) {\n            console.error('Search and extract error:', error);\n            throw new Error('Failed to search and extract content');\n        }\n    }\n    formatResearchData(extractedContent) {\n        return extractedContent.map((item, index)=>{\n            const truncatedContent = item.content.length > 2000 ? item.content.substring(0, 2000) + '...' : item.content;\n            return `=== SOURCE ${index + 1}: ${item.url} ===\\n${truncatedContent}\\n`;\n        }).join('\\n');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/search.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/undici","vendor-chunks/axios","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/asynckit","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/math-intrinsics","vendor-chunks/htmlparser2","vendor-chunks/es-errors","vendor-chunks/whatwg-mimetype","vendor-chunks/call-bind-apply-helpers","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/get-proto","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/@google","vendor-chunks/safer-buffer","vendor-chunks/proxy-from-env","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fhierarchical-agents%2Froute&page=%2Fapi%2Fhierarchical-agents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhierarchical-agents%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();