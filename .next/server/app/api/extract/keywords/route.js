(()=>{var e={};e.id=292,e.ids=[292],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51644:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{POST:()=>u});var o=r(96559),n=r(48088),a=r(37719),i=r(32190),c=r(99475),l=r(93356);async function u(e){try{let{urls:t}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return i.NextResponse.json({error:"URLs array is required"},{status:400});console.log("\uD83D\uDD0D Extracting keywords from competition pages...");let r=new c.J,s=new l.p,o=t.map(async e=>{try{let t=await r.extractContent(e);return{url:e,content:t,success:!0}}catch(t){return console.error(`Failed to extract from ${e}:`,t),{url:e,content:"",success:!1}}}),n=(await Promise.all(o)).filter(e=>e.success&&e.content.length>100);if(0===n.length)return i.NextResponse.json({error:"Failed to extract content from any of the provided URLs"},{status:400});let a=n.map(e=>e.content.substring(0,1e3)).join("\n\n"),u=await s.extractKeywordsFromContent(a);return console.log(`🎯 Extracted keywords: ${u}`),i.NextResponse.json({success:!0,keywords:u.trim(),extractedFrom:n.length,totalUrls:t.length})}catch(e){return console.error("Keyword extraction error:",e),i.NextResponse.json({error:"Failed to extract keywords from competition pages"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/extract/keywords/route",pathname:"/api/extract/keywords",filename:"route",bundlePath:"app/api/extract/keywords/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/extract/keywords/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:h,serverHooks:m}=d;function g(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:h})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93356:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});let s=new(r(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class o{constructor(e="gemini-2.0-flash-lite"){this.model=s.getGenerativeModel({model:e})}async generateContent(e,t={}){try{let r={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40},s=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:r});return(await s.response).text()}catch(e){throw console.error("Gemini generation error:",e),Error("Failed to generate content with Gemini")}}async generateBlogPost(e,t,r,s,o){let n=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${r}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${o?.title?`Article Title: ${o.title}
`:""}
${o?.targetKeyword?`Target Keyword: ${o.targetKeyword} (use naturally throughout the content)
`:""}
${o?.targetAudience?`Target Audience: ${o.targetAudience} (tailor content for this audience)
`:""}
${o?.competitors?`Competitors to outperform: ${o.competitors} (create content that surpasses these sources)
`:""}

${s?`Research Data to incorporate:
${s}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return this.generateContent(n,{temperature:.7,maxOutputTokens:8e3})}async generateEmail(e,t,r,s){let o=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${r}
Key Points to Include: ${s.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return this.generateContent(o,{temperature:.6,maxOutputTokens:1500})}async generateTweet(e,t,r=!0){let s=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${r}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${r?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return this.generateContent(s,{temperature:.8,maxOutputTokens:500})}async extractKeywords(e){let t=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return this.generateContent(t,{temperature:.1,maxOutputTokens:50})}async generateYouTubeScript(e,t,r,s){let o=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${r}
Target Audience: ${s}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return this.generateContent(o,{temperature:.7,maxOutputTokens:5e3})}async extractKeywordsFromContent(e){let t=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return this.generateContent(t,{temperature:.2,maxOutputTokens:200})}}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99475:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(94612),o=r(68281);class n{constructor(){this.apiKey="AIzaSyBlQ7HhWbY37GYbeV9ZJZmTUucspF2KbXE",this.searchEngineId="830840f1a0eaf4acf"}async search(e,t=10){try{if(console.log(`🔍 Searching for: ${e}`),!this.apiKey||!this.searchEngineId)throw console.error("❌ Google Search API credentials not configured"),Error("Google Search API credentials not configured");let r=await s.A.get("https://www.googleapis.com/customsearch/v1",{params:{key:this.apiKey,cx:this.searchEngineId,q:e,num:Math.min(t,10)},timeout:15e3}),o=r.data.items?.map(e=>({title:e.title,link:e.link,snippet:e.snippet,displayLink:e.displayLink}))||[];return console.log(`📊 Found ${o.length} results`),0===o.length?(console.log(`⚠️ No results found for query: "${e}"`),console.log(`📊 Total results available: ${r.data.searchInformation?.totalResults||"0"}`)):o.forEach((e,t)=>{console.log(`${t+1}. ${e.link}`)}),{items:o,searchInformation:{totalResults:r.data.searchInformation?.totalResults||"0",searchTime:r.data.searchInformation?.searchTime||0}}}catch(e){throw console.error("Google Search API error:",e.response?.data||e.message),e.response?.status===403?console.error("❌ API key invalid or quota exceeded"):e.response?.status===400&&console.error("❌ Invalid search parameters"),Error(`Failed to perform search: ${e.response?.data?.error?.message||e.message}`)}}async extractContent(e){try{console.log(`📄 Extracting content from: ${e}`);let t=await s.A.get(e,{timeout:1e4,headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}),r=o.Hh(t.data);r("script, style, nav, header, footer, aside, .advertisement, .ads, .social-share").remove();let n="";for(let e of["article",".content",".post-content",".entry-content",".article-content","main",".main-content","#content",".post-body",".article-body"]){let t=r(e);t.length>0&&t.text().trim().length>n.length&&(n=t.text().trim())}return n||(n=r("body").text().trim()),n=n.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n").trim(),console.log(`✅ Extracted ${n.length} characters from ${e}`),n}catch(t){return console.error(`❌ Failed to extract content from ${e}:`,t),""}}async searchAndExtract(e,t=5){try{let r=await this.search(e,t),s=r.items.map(async e=>({url:e.link,content:await this.extractContent(e.link)})),o=(await Promise.all(s)).filter(e=>e.content.length>100);return console.log(`📚 Successfully extracted content from ${o.length}/${r.items.length} URLs`),{searchResults:r.items,extractedContent:o}}catch(e){throw console.error("Search and extract error:",e),Error("Failed to search and extract content")}}formatResearchData(e){return e.map((e,t)=>{let r=e.content.length>2e3?e.content.substring(0,2e3)+"...":e.content;return`=== SOURCE ${t+1}: ${e.url} ===
${r}
`}).join("\n")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,830,772],()=>r(51644));module.exports=s})();