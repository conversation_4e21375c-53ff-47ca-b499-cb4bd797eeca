(()=>{var e={};e.id=177,e.ids=[177],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61941:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var r={};s.r(r),s.d(r,{GET:()=>u,POST:()=>c,PUT:()=>l});var a=s(96559),n=s(48088),o=s(37719),i=s(32190);async function u(e){try{return i.NextResponse.json({success:!0,settings:{firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0}})}catch(e){return console.error("Error fetching settings:",e),i.NextResponse.json({success:!1,error:"Failed to fetch settings"},{status:500})}}async function c(e){try{let t=await e.json();if(!t.firstName||!t.lastName||!t.email)return i.NextResponse.json({success:!1,error:"Missing required profile fields"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.email))return i.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if(t.defaultWordCount<100||t.defaultWordCount>1e4)return i.NextResponse.json({success:!1,error:"Word count must be between 100 and 10,000"},{status:400});if(!["professional","casual","authoritative","conversational","technical","friendly"].includes(t.defaultTone))return i.NextResponse.json({success:!1,error:"Invalid tone selection"},{status:400});if(!["dark","light","auto"].includes(t.theme))return i.NextResponse.json({success:!1,error:"Invalid theme selection"},{status:400});if(!["blue","purple","green","red","orange"].includes(t.accentColor))return i.NextResponse.json({success:!1,error:"Invalid professional accent color selection"},{status:400});if(!["private","team","public"].includes(t.profileVisibility))return i.NextResponse.json({success:!1,error:"Invalid profile visibility setting"},{status:400});return await new Promise(e=>setTimeout(e,500)),i.NextResponse.json({success:!0,message:"Settings saved successfully",settings:t})}catch(e){return console.error("Error saving settings:",e),i.NextResponse.json({success:!1,error:"Failed to save settings"},{status:500})}}async function l(e){try{let{action:t}=await e.json();if("reset"===t)return await new Promise(e=>setTimeout(e,300)),i.NextResponse.json({success:!0,message:"Settings reset to defaults",settings:{firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0}});return i.NextResponse.json({success:!1,error:"Invalid action"},{status:400})}catch(e){return console.error("Error updating settings:",e),i.NextResponse.json({success:!1,error:"Failed to update settings"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/settings/route",pathname:"/api/settings",filename:"route",bundlePath:"app/api/settings/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/settings/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:f}=d;function g(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580],()=>s(61941));module.exports=r})();