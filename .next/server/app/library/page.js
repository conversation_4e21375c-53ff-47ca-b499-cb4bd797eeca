(()=>{var e={};e.id=382,e.ids=[382],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18063:(e,t,s)=>{Promise.resolve().then(s.bind(s,31072))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},26311:(e,t,s)=>{Promise.resolve().then(s.bind(s,95059))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31072:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/library/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/library/page.tsx","default")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},67760:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70396:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>o});var a=s(65239),i=s(48088),r=s(88170),l=s.n(r),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let o={children:["",{children:["library",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,31072)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/library/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/library/page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/library/page",pathname:"/library",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},70615:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},95059:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(60687),i=s(43210),r=s(97905),l=s(62688);let n=(0,l.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var c=s(13861),o=s(67760),d=s(63143),h=s(70615),p=s(31158);let x=(0,l.A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var u=s(25366),m=s(99270),g=s(10022),y=s(79216),v=s(81822),b=s(16337);let w=[{id:1,title:"The Future of AI in Healthcare",type:"Blog Post",status:"Published",createdAt:"2024-01-15",views:5200,engagement:12.3,tags:["AI","Healthcare","Technology"],thumbnail:"/api/placeholder/300/200"},{id:2,title:"Welcome to Our Newsletter",type:"Email",status:"Sent",createdAt:"2024-01-14",views:2100,engagement:15.7,tags:["Newsletter","Welcome"],thumbnail:"/api/placeholder/300/200"},{id:3,title:"Top 10 AI Tools for Content Creation",type:"Blog Post",status:"Draft",createdAt:"2024-01-13",views:0,engagement:0,tags:["AI","Tools","Content"],thumbnail:"/api/placeholder/300/200"},{id:4,title:"Quick Tips for Better Writing",type:"Tweet Thread",status:"Published",createdAt:"2024-01-12",views:1900,engagement:7.4,tags:["Writing","Tips"],thumbnail:"/api/placeholder/300/200"},{id:5,title:"How to Create Viral YouTube Content",type:"YouTube Script",status:"Published",createdAt:"2024-01-11",views:3400,engagement:9.8,tags:["YouTube","Viral","Content"],thumbnail:"/api/placeholder/300/200"},{id:6,title:"Email Marketing Best Practices",type:"Email",status:"Scheduled",createdAt:"2024-01-10",views:0,engagement:0,tags:["Email","Marketing"],thumbnail:"/api/placeholder/300/200"}],j=["All","Blog Post","Email","Tweet Thread","YouTube Script"],f=["All","Published","Draft","Sent","Scheduled"];function N(){let[e,t]=(0,i.useState)(!1),[s,l]=(0,i.useState)("grid"),[N,A]=(0,i.useState)(""),[k,P]=(0,i.useState)("All"),[M,C]=(0,i.useState)("All"),[S,T]=(0,i.useState)(w);if(!e)return null;let _=e=>{switch(e){case"Published":return"text-green-400 bg-green-500/20";case"Draft":return"text-yellow-400 bg-yellow-500/20";case"Sent":return"text-blue-400 bg-blue-500/20";case"Scheduled":return"text-purple-400 bg-purple-500/20";default:return"text-gray-400 bg-gray-500/20"}},z=({item:e})=>(0,a.jsx)(y.A,{variant:"glass",className:"p-6 group cursor-pointer",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-blue-400 transition-colors",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsx)("span",{className:"text-white/60 text-sm",children:e.type}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${_(e.status)}`,children:e.status})]})]}),(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors opacity-0 group-hover:opacity-100",children:(0,a.jsx)(n,{className:"w-4 h-4 text-white/70"})})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-white/10 text-white/70 text-xs rounded-lg",children:e},e))}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-white/10",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-white/60",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),e.views.toLocaleString()]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),e.engagement,"%"]})]}),(0,a.jsx)("span",{className:"text-white/50 text-sm",children:e.createdAt})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)(v.A,{variant:"secondary",size:"sm",icon:(0,a.jsx)(d.A,{className:"w-3 h-3"}),children:"Edit"}),(0,a.jsx)(v.A,{variant:"secondary",size:"sm",icon:(0,a.jsx)(h.A,{className:"w-3 h-3"}),children:"Copy"}),(0,a.jsx)(v.A,{variant:"secondary",size:"sm",icon:(0,a.jsx)(p.A,{className:"w-3 h-3"}),children:"Export"})]})]})}),E=({item:e})=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors group",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-white font-medium group-hover:text-blue-400 transition-colors",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-1",children:[(0,a.jsx)("span",{className:"text-white/60 text-sm",children:e.type}),(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${_(e.status)}`,children:e.status}),(0,a.jsx)("span",{className:"text-white/50 text-sm",children:e.createdAt})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-white/60",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),e.views.toLocaleString()]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),e.engagement,"%"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(d.A,{className:"w-4 h-4 text-white/70"})}),(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(h.A,{className:"w-4 h-4 text-white/70"})}),(0,a.jsx)("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(n,{className:"w-4 h-4 text-white/70"})})]})]})]});return(0,a.jsx)(b.A,{children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Content Library"}),(0,a.jsx)("p",{className:"text-white/70",children:"Manage and organize all your generated content"})]}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 p-1 bg-white/10 rounded-xl",children:[(0,a.jsx)("button",{onClick:()=>l("grid"),className:`p-2 rounded-lg transition-colors ${"grid"===s?"bg-white/20 text-white":"text-white/60 hover:text-white"}`,children:(0,a.jsx)(x,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>l("list"),className:`p-2 rounded-lg transition-colors ${"list"===s?"bg-white/20 text-white":"text-white/60 hover:text-white"}`,children:(0,a.jsx)(u.A,{className:"w-4 h-4"})})]})})]}),(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50"}),(0,a.jsx)("input",{type:"text",placeholder:"Search content...",value:N,onChange:e=>A(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-400"})]}),(0,a.jsx)("select",{value:k,onChange:e=>P(e.target.value),className:"px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-400",children:j.map(e=>(0,a.jsx)("option",{value:e,children:e},e))}),(0,a.jsx)("select",{value:M,onChange:e=>C(e.target.value),className:"px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-400",children:f.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:"grid"===s?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:S.map((e,t)=>(0,a.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},children:(0,a.jsx)(z,{item:e})},e.id))}):(0,a.jsx)("div",{className:"space-y-4",children:S.map((e,t)=>(0,a.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},children:(0,a.jsx)(E,{item:e})},e.id))})}),0===S.length&&(0,a.jsxs)(r.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-16",children:[(0,a.jsx)(g.A,{className:"w-16 h-16 text-white/40 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white/80 mb-2",children:"No content found"}),(0,a.jsx)("p",{className:"text-white/60",children:"Try adjusting your search or filters"})]})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,135,951,15,141,556],()=>s(70396));module.exports=a})();