(()=>{var t={};t.id=105,t.ids=[105],t.modules={22:(t,e,r)=>{var n=r(75254),i=r(20623),o=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?o:"object"==typeof t?a(t)?i(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),i=r(658),o=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(i),m=l(o),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||i&&x(new i)!=s||o&&x(o.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),i=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,o=e.length;null!=t&&r<o;)t=t[i(e[r++])];return r&&r==o?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),i=r(59467);t.exports=function(t,e){return null!=t&&i(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4315:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>ym});var n={};r.r(n),r.d(n,{scaleBand:()=>nz,scaleDiverging:()=>function t(){var e=oP(cF()(ou));return e.copy=function(){return cz(e,t())},nC.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=oI(cF()).domain([.1,1,10]);return e.copy=function(){return cz(e,t()).base(e.base())},nC.apply(e,arguments)},scaleDivergingPow:()=>cW,scaleDivergingSqrt:()=>cq,scaleDivergingSymlog:()=>function t(){var e=oR(cF());return e.copy=function(){return cz(e,t()).constant(e.constant())},nC.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,oa),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,oa):[0,1],oP(n)},scaleImplicit:()=>nL,scaleLinear:()=>oE,scaleLog:()=>function t(){let e=oI(oh()).domain([1,10]);return e.copy=()=>op(e,t()).base(e.base()),nN.apply(e,arguments),e},scaleOrdinal:()=>nR,scalePoint:()=>nU,scalePow:()=>oW,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=iA){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[iE(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(iw),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nN.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[iE(o,t,0,i)]:e}function u(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nN.apply(oP(c),arguments)},scaleRadial:()=>function t(){var e,r=od(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(oX(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,oa)).map(oX)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},nN.apply(o,arguments),oP(o)},scaleSequential:()=>function t(){var e=oP(cR()(ou));return e.copy=function(){return cz(e,t())},nC.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=oI(cR()).domain([1,10]);return e.copy=function(){return cz(e,t()).base(e.base())},nC.apply(e,arguments)},scaleSequentialPow:()=>cU,scaleSequentialQuantile:()=>function t(){var e=[],r=ou;function n(t){if(null!=t&&!isNaN(t*=1))return r((iE(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(iw),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return oH(t);if(e>=1)return oG(t);var n,i=(n-1)*e,o=Math.floor(i),a=oG((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?oV:function(t=iw){if(t===iw)return oV;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(i,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,o)}let a=e[r],c=n,u=i;for(oY(e,n,r),o(e[i],a)>0&&oY(e,n,i);c<u;){for(oY(e,c,u),++c,--u;0>o(e[c],a);)++c;for(;o(e[u],a)>0;)--u}0===o(e[n],a)?oY(e,n,u):oY(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,o).subarray(0,o+1));return a+(oH(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nC.apply(n,arguments)},scaleSequentialSqrt:()=>c$,scaleSequentialSymlog:()=>function t(){var e=oR(cR());return e.copy=function(){return cz(e,t()).constant(e.constant())},nC.apply(e,arguments)},scaleSqrt:()=>oq,scaleSymlog:()=>function t(){var e=oR(oh());return e.copy=function(){return op(e,t()).constant(e.constant())},nN.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[iE(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},nN.apply(o,arguments)},scaleTime:()=>cB,scaleUtc:()=>cL,tickFormat:()=>oA});var i=r(60687),o=r(43210),a=r.n(o),c=r(97905),u=r(85814),l=r.n(u),s=r(45583),f=r(74606),p=r(41550),h=r(72575),d=r(2943),y=r(10022),v=r(48730),m=r(25541),b=r(96474),g=r(70334),x=r(28947),w=r(53411),O=r(64398),j=r(79216),S=r(81822),A=r(16337);let P=(0,r(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var E=r(13861),k=r(41312);let M=function(){for(var t,e,r=0,n="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=function t(e){var r,n,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(r=0;r<o;r++)e[r]&&(n=t(e[r]))&&(i&&(i+=" "),i+=n)}else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}(t))&&(n&&(n+=" "),n+=e);return n};var _=r(45603),T=r.n(_),N=r(63866),C=r.n(N),D=r(77822),I=r.n(D),B=r(40491),L=r.n(B),R=r(93490),z=r.n(R),U=function(t){return 0===t?0:t>0?1:-1},$=function(t){return C()(t)&&t.indexOf("%")===t.length-1},F=function(t){return z()(t)&&!I()(t)},W=function(t){return F(t)||C()(t)},q=0,X=function(t){var e=++q;return"".concat(t||"").concat(e)},G=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!F(t)&&!C()(t))return n;if($(t)){var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return I()(r)&&(r=n),i&&r>e&&(r=e),r},H=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},V=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},Y=function(t,e){return F(t)&&F(e)?function(r){return t+r*(e-t)}:function(){return e}};function K(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):L()(t,e))===r}):null}var Z=function(t,e){return F(t)&&F(e)?t-e:C()(t)&&C()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},J=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},Q=r(37456),tt=r.n(Q),te=r(5231),tr=r.n(te),tn=r(55048),ti=r.n(tn),to=r(93780);function ta(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function tc(t){return(tc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tu=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],tl=["points","pathLength"],ts={svg:["viewBox","children"],polygon:tl,polyline:tl},tf=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],tp=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,o.isValidElement)(t)&&(r=t.props),!ti()(r))return null;var n={};return Object.keys(r).forEach(function(t){tf.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},th=function(t,e,r){if(!ti()(t)||"object"!==tc(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];tf.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n},td=["children"],ty=["children"];function tv(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function tm(t){return(tm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tb={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tg=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tx=null,tw=null,tO=function t(e){if(e===tx&&Array.isArray(tw))return tw;var r=[];return o.Children.forEach(e,function(e){tt()(e)||((0,to.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tw=r,tx=e,r};function tj(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tg(t)}):[tg(e)],tO(t).forEach(function(t){var e=L()(t,"type.displayName")||L()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tS(t,e){var r=tj(t,e);return r&&r[0]}var tA=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!F(r)&&!(r<=0)&&!!F(n)&&!(n<=0)},tP=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tE=function(t){return t&&"object"===tm(t)&&"clipDot"in t},tk=function(t,e,r,n){var i,o=null!=(i=null==ts?void 0:ts[n])?i:[];return e.startsWith("data-")||!tr()(t)&&(n&&o.includes(e)||tu.includes(e))||r&&tf.includes(e)},tM=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,o.isValidElement)(t)&&(n=t.props),!ti()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;tk(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},t_=function t(e,r){if(e===r)return!0;var n=o.Children.count(e);if(n!==o.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tT(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],c=r[i];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tT(a,c))return!1}return!0},tT=function(t,e){if(tt()(t)&&tt()(e))return!0;if(!tt()(t)&&!tt()(e)){var r=t.props||{},n=r.children,i=tv(r,td),o=e.props||{},a=o.children,c=tv(o,ty);if(n&&a)return ta(i,c)&&t_(n,a);if(!n&&!a)return ta(i,c)}return!1},tN=function(t,e){var r=[],n={};return tO(t).forEach(function(t,i){var o;if((o=t)&&o.type&&C()(o.type)&&tP.indexOf(o.type)>=0)r.push(t);else if(t){var a=tg(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,i);r.push(s),n[a]=!0}}}),r},tC=function(t){var e=t&&t.type;return e&&tb[e]?tb[e]:null};function tD(t){return(tD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tI(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tD(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tL(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tR=(0,o.forwardRef)(function(t,e){var r,n=t.aspect,i=t.initialDimension,c=void 0===i?{width:-1,height:-1}:i,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,O=t.style,j=(0,o.useRef)(null),S=(0,o.useRef)();S.current=w,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var A=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tL(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tL(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),P=A[0],E=A[1],k=(0,o.useCallback)(function(t,e){E(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;k(n,i),null==(e=S.current)||e.call(S,n,i)};b>0&&(t=T()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect();return k(r.width,r.height),e.observe(j.current),function(){e.disconnect()}},[k,b]);var _=(0,o.useMemo)(function(){var t=P.containerWidth,e=P.containerHeight;if(t<0||e<0)return null;J($(l)||$(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),J(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=$(l)?t:l,i=$(f)?e:f;n&&n>0&&(r?i=r/n:i&&(r=i*n),y&&i>y&&(i=y)),J(r>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,i,l,f,h,d,n);var c=!Array.isArray(v)&&tg(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,o.cloneElement)(t,tB({width:r,height:i},c?{style:tB({height:"100%",width:"100%",maxHeight:i,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,d,h,P,l]);return a().createElement("div",{id:g?"".concat(g):void 0,className:M("recharts-responsive-container",x),style:tB(tB({},void 0===O?{}:O),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:y}),ref:j},_)}),tz=r(34990),tU=r.n(tz),t$=r(85938),tF=r.n(t$);function tW(t,e){if(!t)throw Error("Invariant failed")}var tq=["children","width","height","viewBox","className","style","title","desc"];function tX(){return(tX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tG(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,o=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tq),f=i||{width:r,height:n,x:0,y:0},p=M("recharts-surface",o);return a().createElement("svg",tX({},tM(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var tH=["children","className"];function tV(){return(tV=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tY=a().forwardRef(function(t,e){var r=t.children,n=t.className,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tH),o=M("recharts-layer",n);return a().createElement("g",tV({className:o},tM(i,!0),{ref:e}),r)});function tK(t){return(tK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tZ(){return(tZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tQ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tK(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t1(t){return Array.isArray(t)&&W(t[0])&&W(t[1])?t.join(" ~ "):t}var t2=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,i=t.itemStyle,o=void 0===i?{}:i,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,h=t.label,d=t.labelFormatter,y=t.accessibilityLayer,v=t0({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=t0({margin:0},void 0===c?{}:c),b=!tt()(h),g=b?h:"",x=M("recharts-default-tooltip",f),w=M("recharts-tooltip-label",p);return b&&d&&null!=u&&(g=d(h,u)),a().createElement("div",tZ({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(s?tF()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=t0({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},o),i=t.formatter||l||t1,c=t.value,s=t.name,f=c,p=s;if(i&&null!=f&&null!=p){var h=i(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tJ(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tJ(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},W(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,W(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function t5(t){return(t5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t4(t,e,r){var n;return(n=function(t,e){if("object"!=t5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==t5(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var t3="recharts-tooltip-wrapper",t6={visibility:"hidden"};function t8(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(o&&F(o[n]))return o[n];var s=r[n]-c-i,f=r[n]+i;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function t7(t){return(t7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t9(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function et(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t9(Object(r),!0).forEach(function(e){ei(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t9(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ee(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ee=function(){return!!t})()}function er(t){return(er=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function en(t,e){return(en=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ei(t,e,r){return(e=eo(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eo(t){var e=function(t,e){if("object"!=t7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t7(e)?e:e+""}var ea=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=er(e),ei(t=function(t,e){if(e&&("object"===t7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ee()?Reflect.construct(e,n||[],er(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ei(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&en(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,i,o,c,u,l,s,f,p,h,d,y,v,m,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,A=w.animationEasing,P=w.children,E=w.coordinate,k=w.hasPayload,_=w.isAnimationActive,T=w.offset,N=w.position,C=w.reverseDirection,D=w.useTranslate3d,I=w.viewBox,B=w.wrapperStyle,L=(p=(t={allowEscapeViewBox:j,coordinate:E,offsetTopLeft:T,position:N,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:I}).allowEscapeViewBox,h=t.coordinate,d=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&h?(r=(e={translateX:s=t8({allowEscapeViewBox:p,coordinate:h,key:"x",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=t8({allowEscapeViewBox:p,coordinate:h,key:"y",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=t6,{cssProperties:l,cssClasses:(o=(i={translateX:s,translateY:f,coordinate:h}).coordinate,c=i.translateX,u=i.translateY,M(t3,t4(t4(t4(t4({},"".concat(t3,"-right"),F(c)&&o&&F(o.x)&&c>=o.x),"".concat(t3,"-left"),F(c)&&o&&F(o.x)&&c<o.x),"".concat(t3,"-bottom"),F(u)&&o&&F(o.y)&&u>=o.y),"".concat(t3,"-top"),F(u)&&o&&F(o.y)&&u<o.y)))}),R=L.cssClasses,z=L.cssProperties,U=et(et({transition:_&&O?"transform ".concat(S,"ms ").concat(A):void 0},z),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&k?"visible":"hidden",position:"absolute",top:0,left:0},B);return a().createElement("div",{tabIndex:-1,className:R,style:U,ref:function(t){x.wrapperNode=t}},P)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eo(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent),ec={isSsr:!0,get:function(t){return ec[t]},set:function(t,e){if("string"==typeof t)ec[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){ec[e]=t[e]})}}},eu=r(36315),el=r.n(eu);function es(t,e,r){return!0===e?el()(t,r):tr()(e)?el()(t,e):t}function ef(t){return(ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ep(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ep(Object(r),!0).forEach(function(e){em(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ep(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ed(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ed=function(){return!!t})()}function ey(t){return(ey=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ev(t,e){return(ev=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function em(t,e,r){return(e=eb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eb(t){var e=function(t,e){if("object"!=ef(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ef(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ef(e)?e:e+""}function eg(t){return t.dataKey}var ex=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ey(t),function(t,e){if(e&&("object"===ef(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ed()?Reflect.construct(t,e||[],ey(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ev(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,i=r.allowEscapeViewBox,o=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=es(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,eg));var w=x.length>0;return a().createElement(ea,{allowEscapeViewBox:i,animationDuration:o,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=eh(eh({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(t2,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eb(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);em(ex,"displayName","Tooltip"),em(ex,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ec.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ew=r(69433),eO=r.n(ew);let ej=Math.cos,eS=Math.sin,eA=Math.sqrt,eP=Math.PI,eE=2*eP,ek={draw(t,e){let r=eA(e/eP);t.moveTo(r,0),t.arc(0,0,r,0,eE)}},eM=eA(1/3),e_=2*eM,eT=eS(eP/10)/eS(7*eP/10),eN=eS(eE/10)*eT,eC=-ej(eE/10)*eT,eD=eA(3),eI=eA(3)/2,eB=1/eA(12),eL=(eB/2+1)*3;function eR(t){return function(){return t}}let ez=Math.PI,eU=2*ez,e$=eU-1e-6;function eF(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eW{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eF:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eF;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,n,i){if(t*=1,e*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,c=r-t,u=n-e,l=o-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&i){let p=r-o,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=i*Math.tan((ez-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${i},${i},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,i,o){if(t*=1,e*=1,r*=1,o=!!o,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^o,f=o?n-i:i-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eU+eU),f>e$?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ez)},${s},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eq(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eW(e)}function eX(t){return(eX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eW.prototype,eA(3),eA(3);var eG=["type","size","sizeType"];function eH(){return(eH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eV(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=eX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eX(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eK={symbolCircle:ek,symbolCross:{draw(t,e){let r=eA(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=eA(e/e_),n=r*eM;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=eA(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=eA(.8908130915292852*e),n=eN*r,i=eC*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=eE*e/5,a=ej(o),c=eS(o);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*i,c*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-eA(e/(3*eD));t.moveTo(0,2*r),t.lineTo(-eD*r,-r),t.lineTo(eD*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=eA(e/eL),n=r/2,i=r*eB,o=r*eB+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-eI*i,eI*n+-.5*i),t.lineTo(-.5*n-eI*o,eI*n+-.5*o),t.lineTo(-.5*a-eI*o,eI*a+-.5*o),t.lineTo(-.5*n+eI*i,-.5*i-eI*n),t.lineTo(-.5*n+eI*o,-.5*o-eI*n),t.lineTo(-.5*a+eI*o,-.5*o-eI*a),t.closePath()}}},eZ=Math.PI/180,eJ=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eZ;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eQ=function(t){var e,r=t.type,n=void 0===r?"circle":r,i=t.size,o=void 0===i?64:i,c=t.sizeType,u=void 0===c?"area":c,l=eY(eY({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,eG)),{},{type:n,size:o,sizeType:u}),s=l.className,f=l.cx,p=l.cy,h=tM(l,!0);return f===+f&&p===+p&&o===+o?a().createElement("path",eH({},h,{className:M("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eK["symbol".concat(eO()(n))]||ek,(function(t,e){let r=null,n=eq(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:eR(t||ek),e="function"==typeof e?e:eR(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:eR(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(eJ(o,u,n))())})):null};function e0(t){return(e0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function e1(){return(e1=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eQ.registerSymbol=function(t,e){eK["symbol".concat(eO()(t))]=e};function e5(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e5=function(){return!!t})()}function e4(t){return(e4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e3(t,e){return(e3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e6(t,e,r){return(e=e8(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e8(t){var e=function(t,e){if("object"!=e0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e0(e)?e:e+""}var e7=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=e4(t),function(t,e){if(e&&("object"===e0(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,e5()?Reflect.construct(t,e||[],e4(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&e3(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var o=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e2(Object(r),!0).forEach(function(e){e6(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete o.legendIcon,a().cloneElement(t.legendIcon,o)}return a().createElement(eQ,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,i=e.layout,o=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===i?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||o,f=M(e6(e6({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=tr()(e.value)?null:e.value;J(!tr()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return a().createElement("li",e1({className:f,style:l,key:"legend-item-".concat(r)},th(t.props,e,r)),a().createElement(tG,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:h}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e8(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);function e9(t){return(e9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e6(e7,"displayName","Legend"),e6(e7,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var rt=["ref"];function re(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?re(Object(r),!0).forEach(function(e){rc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):re(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ru(n.key),n)}}function ri(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ri=function(){return!!t})()}function ro(t){return(ro=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ra(t,e){return(ra=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rc(t,e,r){return(e=ru(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ru(t){var e=function(t,e){if("object"!=e9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e9(e)?e:e+""}function rl(t){return t.value}var rs=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=ro(e),rc(t=function(t,e){if(e&&("object"===e9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ri()?Reflect.construct(e,r||[],ro(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&ra(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?rr({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),rr(rr({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,i=e.height,o=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=rr(rr({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(o)),o);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,rt);return a().createElement(e7,r)}(r,rr(rr({},this.props),{},{payload:es(u,c,rl)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=rr(rr({},this.defaultProps),t.props).layout;return"vertical"===r&&F(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&rn(n.prototype,e),r&&rn(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function rf(){return(rf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}rc(rs,"displayName","Legend"),rc(rs,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var rp=function(t){var e=t.cx,r=t.cy,n=t.r,i=M("recharts-dot",t.className);return e===+e&&r===+r&&n===+n?a().createElement("circle",rf({},tM(t,!1),tp(t),{className:i,cx:e,cy:r,r:n})):null},rh=r(87955),rd=r.n(rh),ry=Object.getOwnPropertyNames,rv=Object.getOwnPropertySymbols,rm=Object.prototype.hasOwnProperty;function rb(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function rg(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function rx(t){return ry(t).concat(rv(t))}var rw=Object.hasOwn||function(t,e){return rm.call(t,e)};function rO(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rj=Object.getOwnPropertyDescriptor,rS=Object.keys;function rA(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rP(t,e){return rO(t.getTime(),e.getTime())}function rE(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rk(t,e){return t===e}function rM(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function r_(t,e,r){var n=rS(t),i=n.length;if(rS(e).length!==i)return!1;for(;i-- >0;)if(!rL(t,e,r,n[i]))return!1;return!0}function rT(t,e,r){var n,i,o,a=rx(t),c=a.length;if(rx(e).length!==c)return!1;for(;c-- >0;)if(!rL(t,e,r,n=a[c])||(i=rj(t,n),o=rj(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function rN(t,e){return rO(t.valueOf(),e.valueOf())}function rC(t,e){return t.source===e.source&&t.flags===e.flags}function rD(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rI(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rB(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rL(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rw(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rR=Array.isArray,rz="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rU=Object.assign,r$=Object.prototype.toString.call.bind(Object.prototype.toString),rF=rW();function rW(t){void 0===t&&(t={});var e,r,n,i,o,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?rT:rA,areDatesEqual:rP,areErrorsEqual:rE,areFunctionsEqual:rk,areMapsEqual:n?rb(rM,rT):rM,areNumbersEqual:rO,areObjectsEqual:n?rT:r_,arePrimitiveWrappersEqual:rN,areRegExpsEqual:rC,areSetsEqual:n?rb(rD,rT):rD,areTypedArraysEqual:n?rT:rI,areUrlsEqual:rB};if(r&&(i=rU({},i,r(i))),e){var o=rg(i.areArraysEqual),a=rg(i.areMapsEqual),c=rg(i.areObjectsEqual),u=rg(i.areSetsEqual);i=rU({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&o(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rR(t))return r(t,e,d);if(null!=rz&&rz(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=r$(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?i(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,i,o,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rq(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function rX(t){return(rX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rG(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rH(t){return(rH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rV(Object(r),!0).forEach(function(e){rK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rK(t,e,r){var n;return(n=function(t,e){if("object"!==rH(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rH(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rW({strict:!0}),rW({circular:!0}),rW({circular:!0,strict:!0}),rW({createInternalComparator:function(){return rO}}),rW({strict:!0,createInternalComparator:function(){return rO}}),rW({circular:!0,createInternalComparator:function(){return rO}}),rW({circular:!0,createInternalComparator:function(){return rO},strict:!0});var rZ=function(t){return t},rJ=function(t,e){return Object.keys(e).reduce(function(r,n){return rY(rY({},r),{},rK({},n,t(n,e[n])))},{})},rQ=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},r0=function(t,e,r,n,i,o,a,c){};function r1(t,e){if(t){if("string"==typeof t)return r2(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r2(t,e)}}function r2(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r5=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},r4=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},r3=function(t,e){return function(r){return r4(r5(t,e),r)}},r6=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,u=1;break;case"ease":o=.25,a=.1,c=.25,u=1;break;case"ease-in":o=.42,a=0,c=1,u=1;break;case"ease-out":o=.42,a=0,c=.58,u=1;break;case"ease-in-out":o=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(s,4)||r1(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],u=f[3]}else r0(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}r0([o,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r3(o,c),h=r3(a,u),d=(t=o,e=c,function(r){var n;return r4([].concat(function(t){if(Array.isArray(t))return r2(t)}(n=r5(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||r1(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=d(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return h(r)};return y.isStepper=!1,y},r8=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},r7=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r6(n);case"spring":return r8();default:if("cubic-bezier"===n.split("(")[0])return r6(n);r0(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(r0(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function r9(t){return(r9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nt(t){return function(t){if(Array.isArray(t))return no(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ni(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ne(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ne(Object(r),!0).forEach(function(e){nn(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ne(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nn(t,e,r){var n;return(n=function(t,e){if("object"!==r9(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r9(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ni(t,e){if(t){if("string"==typeof t)return no(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return no(t,e)}}function no(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var na=function(t,e,r){return t+(e-t)*r},nc=function(t){return t.from!==t.to},nu=function t(e,r,n){var i=rJ(function(t,r){if(nc(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(n,2)||ni(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return nr(nr({},r),{},{from:o,velocity:a})}return r},r);return n<1?rJ(function(t,e){return nc(e)?nr(nr({},e),{},{velocity:na(e.velocity,i[t].velocity,n),from:na(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let nl=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return nr(nr({},r),{},nn({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return nr(nr({},r),{},nn({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;l=nu(r,l,a),i(nr(nr(nr({},t),e),rJ(function(t,e){return e.from},l))),o=n,Object.values(l).filter(nc).length&&(s=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,l=rJ(function(t,e){return na.apply(void 0,nt(e).concat([r(c)]))},u);if(i(nr(nr(nr({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rJ(function(t,e){return na.apply(void 0,nt(e).concat([r(1)]))},u);i(nr(nr(nr({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function ns(t){return(ns="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var nf=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function np(t){return function(t){if(Array.isArray(t))return nh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nh(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nh(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ny(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nd(Object(r),!0).forEach(function(e){nv(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nd(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nv(t,e,r){return(e=nm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nm(t){var e=function(t,e){if("object"!==ns(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ns(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ns(e)?e:String(e)}function nb(t,e){return(nb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ng(t,e){if(e&&("object"===ns(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nx(t)}function nx(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nw(t){return(nw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var nO=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&nb(i,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nw(i);return t=e?Reflect.construct(r,arguments,nw(this).constructor):r.apply(this,arguments),ng(this,t)});function i(t,e){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),o=r.props,a=o.isActive,c=o.attributeName,u=o.from,l=o.to,s=o.steps,f=o.children,p=o.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nx(r)),r.changeStyle=r.changeStyle.bind(nx(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),ng(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},ng(r);r.state={style:c?nv({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:i?nv({},i,a):a};this.state&&u&&(i&&u[i]!==a||!i&&u!==a)&&this.setState(l);return}if(!rF(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||o?c:t.to;if(this.state&&u){var p={style:i?nv({},i,f):f};(i&&u[i]!==f||!i&&u!==f)&&this.setState(p)}this.runAnimation(ny(ny({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=nl(r,n,r7(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(np(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=i>0?r[i-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(np(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var h=rQ(p,o,c),d=ny(ny(ny({},f.style),u),{},{transition:h});return[].concat(np(t),[d,o,s]).filter(rZ)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rG(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rG(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void rq(t.bind(null,a),o):(t(o),void rq(t.bind(null,a)))}"object"===rX(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?nv({},c,u):u,v=rQ(Object.keys(y),a,l);d.start([s,o,ny(ny({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,nf)),c=o.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,o.cloneElement)(t,ny(ny({},i),{},{style:ny(ny({},void 0===r?{}:r),u),className:n}))};return 1===c?l(o.Children.only(e)):a().createElement("div",null,o.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nm(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(o.PureComponent);function nj(t){return(nj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nS(){return(nS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nP(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nj(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}nO.displayName="Animate",nO.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nO.propTypes={from:rd().oneOfType([rd().object,rd().string]),to:rd().oneOfType([rd().object,rd().string]),attributeName:rd().string,duration:rd().number,begin:rd().number,easing:rd().oneOfType([rd().string,rd().func]),steps:rd().arrayOf(rd().shape({duration:rd().number.isRequired,style:rd().object.isRequired,easing:rd().oneOfType([rd().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rd().func]),properties:rd().arrayOf("string"),onAnimationEnd:rd().func})),children:rd().oneOfType([rd().node,rd().func]),isActive:rd().bool,canBegin:rd().bool,onAnimationEnd:rd().func,shouldReAnimate:rd().bool,onAnimationStart:rd().func,onAnimationReStart:rd().func};var nk=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),o+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),o+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),o+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},nM=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},n_={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nT=function(t){var e,r=nE(nE({},n_),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nA(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nA(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var x=M("recharts-rectangle",d);return g?a().createElement(nO,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,i=t.height,o=t.x,u=t.y;return a().createElement(nO,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",nS({},tM(r,!0),{className:x,d:nk(o,u,e,i,h),ref:n})))}):a().createElement("path",nS({},tM(r,!0),{className:x,d:nk(l,s,f,p,h)}))};function nN(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nC(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nD extends Map{constructor(t,e=nB){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nI(this,t))}has(t){return super.has(nI(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nI({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nB(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nL=Symbol("implicit");function nR(){var t=new nD,e=[],r=[],n=nL;function i(i){let o=t.get(i);if(void 0===o){if(n!==nL)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nD,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return nR(e,r).unknown(n)},nN.apply(i,arguments),i}function nz(){var t,e,r=nR().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<o,p=f?a:o,h=f?o:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return p+t*e});return i(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nz(n(),[o,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nN.apply(f(),arguments)}function nU(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nz.apply(null,arguments).paddingInner(1))}function n$(t){return(n$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nF(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=n$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=n$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==n$(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nq(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nX={widthCache:{},cacheCount:0},nG={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nH="recharts_measurement_span",nV=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||ec.isSsr)return{width:0,height:0};var n=(Object.keys(e=nW({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:n});if(nX.widthCache[i])return nX.widthCache[i];try{var o=document.getElementById(nH);o||((o=document.createElement("span")).setAttribute("id",nH),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=nW(nW({},nG),n);Object.assign(o.style,a),o.textContent="".concat(t);var c=o.getBoundingClientRect(),u={width:c.width,height:c.height};return nX.widthCache[i]=u,++nX.cacheCount>2e3&&(nX.cacheCount=0,nX.widthCache={}),u}catch(t){return{width:0,height:0}}};function nY(t){return(nY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nK(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nZ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nZ(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nJ(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nY(e)?e:e+""}(n.key),n)}}var nQ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,n0=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,n1=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,n2=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n5={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},n4=Object.keys(n5),n3=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||n1.test(e)||(this.num=NaN,this.unit=""),n4.includes(e)&&(this.num=t*n5[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nK(null!=(e=n2.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&nJ(r.prototype,t),e&&nJ(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function n6(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nK(null!=(r=nQ.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],c=n3.parse(null!=i?i:""),u=n3.parse(null!=a?a:""),l="*"===o?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nQ,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nK(null!=(s=n0.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=n3.parse(null!=p?p:""),v=n3.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(n0,m.toString())}return e}var n8=/\(([^()]*)\)/;function n7(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nK(n8.exec(e),2)[1];e=e.replace(n8,n6(r))}return e}(e),e=n6(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var n9=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],it=["dx","dy","angle","className","breakAll"];function ie(){return(ie=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ir(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function ii(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return io(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return io(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function io(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ia=/[ \f\n\r\t\v\u2028\u2029]+/,ic=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];tt()(e)||(i=r?e.toString().split(""):e.toString().split(ia));var o=i.map(function(t){return{word:t,width:nV(t,n).width}}),a=r?0:nV("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:a}}catch(t){return null}},iu=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=F(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(ic({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=ii(h(m-1),2),g=b[0],x=b[1],w=ii(h(m),1)[0];if(g||w||(d=m+1),g&&w&&(y=m-1),!g&&w){o=x;break}v++}return o||p},il=function(t){return[{words:tt()(t)?[]:t.toString().split(ia)}]},is=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!ec.isSsr){var c=ic({breakAll:o,children:n,style:i});if(!c)return il(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return iu({breakAll:o,children:n,maxLines:a,style:i},u,l,e,r)}return il(n)},ip="#808080",ih=function(t){var e,r=t.x,n=void 0===r?0:r,i=t.y,c=void 0===i?0:i,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,d=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?ip:v,b=ir(t,n9),g=(0,o.useMemo)(function(){return is({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:h,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,h,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,A=ir(b,it);if(!W(n)||!W(c))return null;var P=n+(F(x)?x:0),E=c+(F(w)?w:0);switch(void 0===y?"end":y){case"start":e=n7("calc(".concat(f,")"));break;case"middle":e=n7("calc(".concat((g.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=n7("calc(".concat(g.length-1," * -").concat(l,")"))}var k=[];if(h){var _=g[0].width,T=b.width;k.push("scale(".concat((F(T)?T/_:1)/_,")"))}return O&&k.push("rotate(".concat(O,", ").concat(P,", ").concat(E,")")),k.length&&(A.transform=k.join(" ")),a().createElement("text",ie({},tM(A,!0),{x:P,y:E,className:M("recharts-text",j),textAnchor:void 0===d?"start":d,fill:m.includes("url")?ip:m}),g.map(function(t,r){var n=t.words.join(S?"":" ");return a().createElement("tspan",{x:P,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let id=Math.sqrt(50),iy=Math.sqrt(10),iv=Math.sqrt(2);function im(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=id?10:u>=iy?5:u>=iv?2:1;return(c<0?(n=Math.round(t*(o=Math.pow(10,-c)/l)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,c)*l)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?im(t,e,2*r):[n,i,o]}function ib(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?im(e,t,r):im(t,e,r);if(!(o>=i))return[];let c=o-i+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((o-t)/a);else for(let t=0;t<c;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((i+t)/a);else for(let t=0;t<c;++t)u[t]=(i+t)*a;return u}function ig(t,e,r){return im(t*=1,e*=1,r*=1)[2]}function ix(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?ig(e,t,r):ig(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function iw(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function iO(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ij(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=iw,r=(e,r)=>iw(t(e),r),n=(e,r)=>t(e)-r):(e=t===iw||t===iO?t:iS,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function iS(){return 0}function iA(t){return null===t?NaN:+t}let iP=ij(iw),iE=iP.right;function ik(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function iM(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function i_(){}iP.left,ij(iA).center;var iT="\\s*([+-]?\\d+)\\s*",iN="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iC="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iD=/^#([0-9a-f]{3,8})$/,iI=RegExp(`^rgb\\(${iT},${iT},${iT}\\)$`),iB=RegExp(`^rgb\\(${iC},${iC},${iC}\\)$`),iL=RegExp(`^rgba\\(${iT},${iT},${iT},${iN}\\)$`),iR=RegExp(`^rgba\\(${iC},${iC},${iC},${iN}\\)$`),iz=RegExp(`^hsl\\(${iN},${iC},${iC}\\)$`),iU=RegExp(`^hsla\\(${iN},${iC},${iC},${iN}\\)$`),i$={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iF(){return this.rgb().formatHex()}function iW(){return this.rgb().formatRgb()}function iq(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=iD.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?iX(e):3===r?new iV(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?iG(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?iG(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=iI.exec(t))?new iV(e[1],e[2],e[3],1):(e=iB.exec(t))?new iV(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=iL.exec(t))?iG(e[1],e[2],e[3],e[4]):(e=iR.exec(t))?iG(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=iz.exec(t))?i0(e[1],e[2]/100,e[3]/100,1):(e=iU.exec(t))?i0(e[1],e[2]/100,e[3]/100,e[4]):i$.hasOwnProperty(t)?iX(i$[t]):"transparent"===t?new iV(NaN,NaN,NaN,0):null}function iX(t){return new iV(t>>16&255,t>>8&255,255&t,1)}function iG(t,e,r,n){return n<=0&&(t=e=r=NaN),new iV(t,e,r,n)}function iH(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof i_||(i=iq(i)),i)?new iV((i=i.rgb()).r,i.g,i.b,i.opacity):new iV:new iV(t,e,r,null==n?1:n)}function iV(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function iY(){return`#${iQ(this.r)}${iQ(this.g)}${iQ(this.b)}`}function iK(){let t=iZ(this.opacity);return`${1===t?"rgb(":"rgba("}${iJ(this.r)}, ${iJ(this.g)}, ${iJ(this.b)}${1===t?")":`, ${t})`}`}function iZ(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function iJ(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function iQ(t){return((t=iJ(t))<16?"0":"")+t.toString(16)}function i0(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new i2(t,e,r,n)}function i1(t){if(t instanceof i2)return new i2(t.h,t.s,t.l,t.opacity);if(t instanceof i_||(t=iq(t)),!t)return new i2;if(t instanceof i2)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new i2(a,c,u,t.opacity)}function i2(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function i5(t){return(t=(t||0)%360)<0?t+360:t}function i4(t){return Math.max(0,Math.min(1,t||0))}function i3(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function i6(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}ik(i_,iq,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:iF,formatHex:iF,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return i1(this).formatHsl()},formatRgb:iW,toString:iW}),ik(iV,iH,iM(i_,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iV(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iV(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new iV(iJ(this.r),iJ(this.g),iJ(this.b),iZ(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iY,formatHex:iY,formatHex8:function(){return`#${iQ(this.r)}${iQ(this.g)}${iQ(this.b)}${iQ((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iK,toString:iK})),ik(i2,function(t,e,r,n){return 1==arguments.length?i1(t):new i2(t,e,r,null==n?1:n)},iM(i_,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new i2(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new i2(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new iV(i3(t>=240?t-240:t+120,i,n),i3(t,i,n),i3(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new i2(i5(this.h),i4(this.s),i4(this.l),iZ(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=iZ(this.opacity);return`${1===t?"hsl(":"hsla("}${i5(this.h)}, ${100*i4(this.s)}%, ${100*i4(this.l)}%${1===t?")":`, ${t})`}`}}));let i8=t=>()=>t;function i7(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):i8(isNaN(t)?e:t)}let i9=function t(e){var r,n=1==(r=+e)?i7:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):i8(isNaN(t)?e:t)};function i(t,e){var r=n((t=iH(t)).r,(e=iH(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=i7(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function ot(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=iH(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}ot(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return i6((r-n/e)*e,a,i,o,c)}}),ot(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return i6((r-n/e)*e,i,o,a,c)}});function oe(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var or=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,on=RegExp(or.source,"g");function oi(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?i8(e):("number"===i?oe:"string"===i?(n=iq(e))?(e=n,i9):function(t,e){var r,n,i,o,a,c=or.lastIndex=on.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(i=or.exec(t))&&(o=on.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(i=i[0])===(o=o[0])?l[u]?l[u]+=o:l[++u]=o:(l[++u]=null,s.push({i:u,x:oe(i,o)})),c=on.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof iq?i9:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=oi(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=oi(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:oe:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function oo(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function oa(t){return+t}var oc=[0,1];function ou(t){return t}function ol(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function os(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=ol(i,n),o=r(a,o)):(n=ol(n,i),o=r(o,a)),function(t){return o(n(t))}}function of(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=ol(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=iE(t,e,1,n)-1;return o[r](i[r](e))}}function op(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function oh(){var t,e,r,n,i,o,a=oc,c=oc,u=oi,l=ou;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==ou&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?of:os,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(c,a.map(t),oe)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,oa),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oo,s()},f.clamp=function(t){return arguments.length?(l=!!t||ou,s()):l!==ou},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function od(){return oh()(ou,ou)}var oy=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ov(t){var e;if(!(e=oy.exec(t)))throw Error("invalid format: "+t);return new om({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function om(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ob(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function og(t){return(t=ob(Math.abs(t)))?t[1]:NaN}function ox(t,e){var r=ob(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ov.prototype=om.prototype,om.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ow={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ox(100*t,e),r:ox,s:function(t,e){var r=ob(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(cK=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+ob(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function oO(t){return t}var oj=Array.prototype.map,oS=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function oA(t,e,r,n){var i,o,a,c=ix(t,e,r);switch((n=ov(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(og(u)/3)))-og(Math.abs(c))))||(n.precision=a),cQ(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,og(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-og(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-og(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cJ(n)}function oP(t){var e=t.domain;return t.ticks=function(t){var r=e();return ib(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return oA(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],l=o[c],s=10;for(l<u&&(i=u,u=l,l=i,i=a,a=c,c=i);s-- >0;){if((i=ig(u,l,r))===n)return o[a]=u,o[c]=l,e(o);if(i>0)u=Math.floor(u/i)*i,l=Math.ceil(l/i)*i;else if(i<0)u=Math.ceil(u*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function oE(){var t=od();return t.copy=function(){return op(t,oE())},nN.apply(t,arguments),oP(t)}function ok(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function oM(t){return Math.log(t)}function o_(t){return Math.exp(t)}function oT(t){return-Math.log(-t)}function oN(t){return-Math.exp(-t)}function oC(t){return isFinite(t)?+("1e"+t):t<0?0:t}function oD(t){return(e,r)=>-t(-e,r)}function oI(t){let e,r,n=t(oM,o_),i=n.domain,o=10;function a(){var a,c;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=o)?oC:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=oD(e),r=oD(r),t(oT,oN)):t(oM,o_),n}return n.base=function(t){return arguments.length?(o=+t,a()):o},n.domain=function(t){return arguments.length?(i(t),a()):i()},n.ticks=t=>{let n,a,c=i(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(o%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=ib(u,l,h))}else d=ib(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=ov(i)).precision||(i.trim=!0),i=cJ(i)),t===1/0)return i;let a=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=a?i(t):""}},n.nice=()=>i(ok(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function oB(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function oL(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function oR(t){var e=1,r=t(oB(1),oL(e));return r.constant=function(r){return arguments.length?t(oB(e=+r),oL(e)):e},oP(r)}function oz(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function oU(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function o$(t){return t<0?-t*t:t*t}function oF(t){var e=t(ou,ou),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(ou,ou):.5===r?t(oU,o$):t(oz(r),oz(1/r)):r},oP(e)}function oW(){var t=oF(oh());return t.copy=function(){return op(t,oW()).exponent(t.exponent())},nN.apply(t,arguments),t}function oq(){return oW.apply(null,arguments).exponent(.5)}function oX(t){return Math.sign(t)*t*t}function oG(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function oH(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}cJ=(cZ=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?oO:(e=oj.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?oO:(n=oj.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=ov(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):ow[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",w=ow[b],O=/[defgprs%]/.test(b);function j(t){var o,a,l,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?oS[8+cK/3]:"")+j+(S&&"("===n?")":""),O){for(o=-1,a=t.length;++o<a;)if(48>(l=t.charCodeAt(o))||l>57){j=(46===l?c+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}}y&&!h&&(t=i(t,1/0));var A=p.length+t.length+j.length,P=A<d?Array(d-A+1).join(e):"";switch(y&&h&&(t=i(P+t,P.length?d-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,A=P.length>>1)+p+t+j+P.slice(A);break;default:t=P+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=ov(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(og(e)/3))),i=Math.pow(10,-n),o=oS[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cQ=cZ.formatPrefix;function oV(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function oY(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let oK=new Date,oZ=new Date;function oJ(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>oJ(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(oK.setTime(+e),oZ.setTime(+n),t(oK),t(oZ),Math.floor(r(oK,oZ))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let oQ=oJ(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);oQ.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?oJ(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):oQ:null,oQ.range;let o0=oJ(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());o0.range;let o1=oJ(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());o1.range;let o2=oJ(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());o2.range;let o5=oJ(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());o5.range;let o4=oJ(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());o4.range;let o3=oJ(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);o3.range;let o6=oJ(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);o6.range;let o8=oJ(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function o7(t){return oJ(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}o8.range;let o9=o7(0),at=o7(1),ae=o7(2),ar=o7(3),an=o7(4),ai=o7(5),ao=o7(6);function aa(t){return oJ(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}o9.range,at.range,ae.range,ar.range,an.range,ai.range,ao.range;let ac=aa(0),au=aa(1),al=aa(2),as=aa(3),af=aa(4),ap=aa(5),ah=aa(6);ac.range,au.range,al.range,as.range,af.range,ap.range,ah.range;let ad=oJ(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ad.range;let ay=oJ(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());ay.range;let av=oJ(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());av.every=t=>isFinite(t=Math.floor(t))&&t>0?oJ(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,av.range;let am=oJ(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ab(t,e,r,n,i,o){let a=[[o0,1,1e3],[o0,5,5e3],[o0,15,15e3],[o0,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=ij(([,,t])=>t).right(a,i);if(o===a.length)return t.every(ix(e/31536e6,r/31536e6,n));if(0===o)return oQ.every(Math.max(ix(e,r,n),1));let[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}am.every=t=>isFinite(t=Math.floor(t))&&t>0?oJ(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,am.range;let[ag,ax]=ab(am,ay,ac,o8,o4,o2),[aw,aO]=ab(av,ad,o9,o3,o5,o1);function aj(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function aS(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function aA(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var aP={"-":"",_:" ",0:"0"},aE=/^\s*\d+/,ak=/^%/,aM=/[\\^$*+?|[\]().{}]/g;function a_(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function aT(t){return t.replace(aM,"\\$&")}function aN(t){return RegExp("^(?:"+t.map(aT).join("|")+")","i")}function aC(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aD(t,e,r){var n=aE.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=aE.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aL(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function az(t,e,r){var n=aE.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aU(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function a$(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aF(t,e,r){var n=aE.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aW(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aq(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aX(t,e,r){var n=aE.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aG(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aH(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aV(t,e,r){var n=aE.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aY(t,e,r){var n=aE.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aK(t,e,r){var n=aE.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aZ(t,e,r){var n=ak.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aJ(t,e,r){var n=aE.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aQ(t,e,r){var n=aE.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function a0(t,e){return a_(t.getDate(),e,2)}function a1(t,e){return a_(t.getHours(),e,2)}function a2(t,e){return a_(t.getHours()%12||12,e,2)}function a5(t,e){return a_(1+o3.count(av(t),t),e,3)}function a4(t,e){return a_(t.getMilliseconds(),e,3)}function a3(t,e){return a4(t,e)+"000"}function a6(t,e){return a_(t.getMonth()+1,e,2)}function a8(t,e){return a_(t.getMinutes(),e,2)}function a7(t,e){return a_(t.getSeconds(),e,2)}function a9(t){var e=t.getDay();return 0===e?7:e}function ct(t,e){return a_(o9.count(av(t)-1,t),e,2)}function ce(t){var e=t.getDay();return e>=4||0===e?an(t):an.ceil(t)}function cr(t,e){return t=ce(t),a_(an.count(av(t),t)+(4===av(t).getDay()),e,2)}function cn(t){return t.getDay()}function ci(t,e){return a_(at.count(av(t)-1,t),e,2)}function co(t,e){return a_(t.getFullYear()%100,e,2)}function ca(t,e){return a_((t=ce(t)).getFullYear()%100,e,2)}function cc(t,e){return a_(t.getFullYear()%1e4,e,4)}function cu(t,e){var r=t.getDay();return a_((t=r>=4||0===r?an(t):an.ceil(t)).getFullYear()%1e4,e,4)}function cl(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+a_(e/60|0,"0",2)+a_(e%60,"0",2)}function cs(t,e){return a_(t.getUTCDate(),e,2)}function cf(t,e){return a_(t.getUTCHours(),e,2)}function cp(t,e){return a_(t.getUTCHours()%12||12,e,2)}function ch(t,e){return a_(1+o6.count(am(t),t),e,3)}function cd(t,e){return a_(t.getUTCMilliseconds(),e,3)}function cy(t,e){return cd(t,e)+"000"}function cv(t,e){return a_(t.getUTCMonth()+1,e,2)}function cm(t,e){return a_(t.getUTCMinutes(),e,2)}function cb(t,e){return a_(t.getUTCSeconds(),e,2)}function cg(t){var e=t.getUTCDay();return 0===e?7:e}function cx(t,e){return a_(ac.count(am(t)-1,t),e,2)}function cw(t){var e=t.getUTCDay();return e>=4||0===e?af(t):af.ceil(t)}function cO(t,e){return t=cw(t),a_(af.count(am(t),t)+(4===am(t).getUTCDay()),e,2)}function cj(t){return t.getUTCDay()}function cS(t,e){return a_(au.count(am(t)-1,t),e,2)}function cA(t,e){return a_(t.getUTCFullYear()%100,e,2)}function cP(t,e){return a_((t=cw(t)).getUTCFullYear()%100,e,2)}function cE(t,e){return a_(t.getUTCFullYear()%1e4,e,4)}function ck(t,e){var r=t.getUTCDay();return a_((t=r>=4||0===r?af(t):af.ceil(t)).getUTCFullYear()%1e4,e,4)}function cM(){return"+0000"}function c_(){return"%"}function cT(t){return+t}function cN(t){return Math.floor(t/1e3)}function cC(t){return new Date(t)}function cD(t){return t instanceof Date?+t:+new Date(+t)}function cI(t,e,r,n,i,o,a,c,u,l){var s=od(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cD)):p().map(cC)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(ok(r,t)):s},s.copy=function(){return op(s,cI(t,e,r,n,i,o,a,c,u,l))},s}function cB(){return nN.apply(cI(aw,aO,av,ad,o9,o3,o5,o1,o0,c1).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cL(){return nN.apply(cI(ag,ax,am,ay,ac,o6,o4,o2,o0,c2).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cR(){var t,e,r,n,i,o=0,a=1,c=ou,u=!1;function l(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(oi),l.rangeRound=s(oo),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function cz(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cU(){var t=oF(cR());return t.copy=function(){return cz(t,cU()).exponent(t.exponent())},nC.apply(t,arguments)}function c$(){return cU.apply(null,arguments).exponent(.5)}function cF(){var t,e,r,n,i,o,a,c=0,u=.5,l=1,s=1,f=ou,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=oi);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=o(c*=1),e=o(u*=1),r=o(l*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(oi),h.rangeRound=d(oo),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cW(){var t=oF(cF());return t.copy=function(){return cz(t,cW()).exponent(t.exponent())},nC.apply(t,arguments)}function cq(){return cW.apply(null,arguments).exponent(.5)}function cX(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cG(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cH(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cV(t,e){return t[e]}function cY(t){let e=[];return e.key=t,e}c1=(c0=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=aN(i),s=aC(i),f=aN(o),p=aC(o),h=aN(a),d=aC(a),y=aN(c),v=aC(c),m=aN(u),b=aC(u),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:a0,e:a0,f:a3,g:ca,G:cu,H:a1,I:a2,j:a5,L:a4,m:a6,M:a8,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cT,s:cN,S:a7,u:a9,U:ct,V:cr,w:cn,W:ci,x:null,X:null,y:co,Y:cc,Z:cl,"%":c_},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:cs,e:cs,f:cy,g:cP,G:ck,H:cf,I:cp,j:ch,L:cd,m:cv,M:cm,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cT,s:cN,S:cb,u:cg,U:cx,V:cO,w:cj,W:cS,x:null,X:null,y:cA,Y:cE,Z:cM,"%":c_},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aq,e:aq,f:aK,g:aU,G:az,H:aG,I:aG,j:aX,L:aY,m:aW,M:aH,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aF,Q:aJ,s:aQ,S:aV,u:aI,U:aB,V:aL,w:aD,W:aR,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aU,Y:az,Z:a$,"%":aZ};function O(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=aP[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,i,o=aA(1900,void 0,1);if(S(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=aS(aA(o.y,0,1))).getUTCDay())>4||0===i?au.ceil(n):au(n),n=o6.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=aj(aA(o.y,0,1))).getDay())>4||0===i?at.ceil(n):at(n),n=o3.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?aS(aA(o.y,0,1)).getUTCDay():aj(aA(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,aS(o)):aj(o)}}function S(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=w[(i=e.charAt(a++))in aP?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c0.parse,c2=c0.utcFormat,c0.utcParse,Array.prototype.slice;var cK,cZ,cJ,cQ,c0,c1,c2,c5,c4,c3=r(90453),c6=r.n(c3),c8=r(15883),c7=r.n(c8),c9=r(21592),ut=r.n(c9),ue=r(71967),ur=r.n(ue),un=!0,ui="[DecimalError] ",uo=ui+"Invalid argument: ",ua=ui+"Exponent out of range: ",uc=Math.floor,uu=Math.pow,ul=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,us=uc(1286742750677284.5),uf={};function up(t,e){var r,n,i,o,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),un?uO(e,f):e;if(u=t.d,l=e.d,a=t.e,i=e.e,u=u.slice(),o=a-i){for(o<0?(n=u,o=-o,c=l.length):(n=l,i=a,c=u.length),o>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=u.length)-(o=l.length)<0&&(o=c,n=l,l=u,u=n),r=0;o;)r=(u[--o]=u[o]+l[o]+r)/1e7|0,u[o]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=i,un?uO(e,f):e}function uh(t,e,r){if(t!==~~t||t<e||t>r)throw Error(uo+t)}function ud(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=ug(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=ug(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}uf.absoluteValue=uf.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},uf.comparedTo=uf.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},uf.decimalPlaces=uf.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},uf.dividedBy=uf.div=function(t){return uy(this,new this.constructor(t))},uf.dividedToIntegerBy=uf.idiv=function(t){var e=this.constructor;return uO(uy(this,new e(t),0,1),e.precision)},uf.equals=uf.eq=function(t){return!this.cmp(t)},uf.exponent=function(){return um(this)},uf.greaterThan=uf.gt=function(t){return this.cmp(t)>0},uf.greaterThanOrEqualTo=uf.gte=function(t){return this.cmp(t)>=0},uf.isInteger=uf.isint=function(){return this.e>this.d.length-2},uf.isNegative=uf.isneg=function(){return this.s<0},uf.isPositive=uf.ispos=function(){return this.s>0},uf.isZero=function(){return 0===this.s},uf.lessThan=uf.lt=function(t){return 0>this.cmp(t)},uf.lessThanOrEqualTo=uf.lte=function(t){return 1>this.cmp(t)},uf.logarithm=uf.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(c4))throw Error(ui+"NaN");if(this.s<1)throw Error(ui+(this.s?"NaN":"-Infinity"));return this.eq(c4)?new r(0):(un=!1,e=uy(ux(this,i),ux(t,i),i),un=!0,uO(e,n))},uf.minus=uf.sub=function(t){return t=new this.constructor(t),this.s==t.s?uj(this,t):up(this,(t.s=-t.s,t))},uf.modulo=uf.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(ui+"NaN");return this.s?(un=!1,e=uy(this,t,0,1).times(t),un=!0,this.minus(e)):uO(new r(this),n)},uf.naturalExponential=uf.exp=function(){return uv(this)},uf.naturalLogarithm=uf.ln=function(){return ux(this)},uf.negated=uf.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},uf.plus=uf.add=function(t){return t=new this.constructor(t),this.s==t.s?up(this,t):uj(this,(t.s=-t.s,t))},uf.precision=uf.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(uo+t);if(e=um(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},uf.squareRoot=uf.sqrt=function(){var t,e,r,n,i,o,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(ui+"NaN")}for(t=um(this),un=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=ud(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=uc((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=a=(r=c.precision)+3;;)if(n=(o=n).plus(uy(this,o,a+2)).times(.5),ud(o.d).slice(0,a)===(e=ud(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(uO(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return un=!0,uO(n,r)},uf.times=uf.mul=function(t){var e,r,n,i,o,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(o=f,f=p,p=o,a=u,u=l,l=a),o=[],n=a=u+l;n--;)o.push(0);for(n=l;--n>=0;){for(e=0,i=u+n;i>n;)c=o[i]+p[n]*f[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,un?uO(t,s.precision):t},uf.toDecimalPlaces=uf.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(uh(t,0,1e9),void 0===e?e=n.rounding:uh(e,0,8),uO(r,t+um(r)+1,e))},uf.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=uS(n,!0):(uh(t,0,1e9),void 0===e?e=i.rounding:uh(e,0,8),r=uS(n=uO(new i(n),t+1,e),!0,t+1)),r},uf.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?uS(this):(uh(t,0,1e9),void 0===e?e=i.rounding:uh(e,0,8),r=uS((n=uO(new i(this),t+um(this)+1,e)).abs(),!1,t+um(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},uf.toInteger=uf.toint=function(){var t=this.constructor;return uO(new t(this),um(this)+1,t.rounding)},uf.toNumber=function(){return+this},uf.toPower=uf.pow=function(t){var e,r,n,i,o,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(c4);if(!(c=new u(c)).s){if(t.s<1)throw Error(ui+"Infinity");return c}if(c.eq(c4))return c;if(n=u.precision,t.eq(c4))return uO(c,n);if(a=(e=t.e)>=(r=t.d.length-1),o=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(i=new u(c4),e=Math.ceil(n/7+4),un=!1;r%2&&uA((i=i.times(c)).d,e),0!==(r=uc(r/2));)uA((c=c.times(c)).d,e);return un=!0,t.s<0?new u(c4).div(i):uO(i,n)}}else if(o<0)throw Error(ui+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,un=!1,i=t.times(ux(c,n+12)),un=!0,(i=uv(i)).s=o,i},uf.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=um(i),n=uS(i,r<=o.toExpNeg||r>=o.toExpPos)):(uh(t,1,1e9),void 0===e?e=o.rounding:uh(e,0,8),r=um(i=uO(new o(i),t,e)),n=uS(i,t<=r||r<=o.toExpNeg,t)),n},uf.toSignificantDigits=uf.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(uh(t,1,1e9),void 0===e?e=r.rounding:uh(e,0,8)),uO(new r(this),t,e)},uf.toString=uf.valueOf=uf.val=uf.toJSON=uf[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=um(this),e=this.constructor;return uS(this,t<=e.toExpNeg||t>=e.toExpPos)};var uy=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,w,O,j,S,A=n.constructor,P=n.s==i.s?1:-1,E=n.d,k=i.d;if(!n.s)return new A(n);if(!i.s)throw Error(ui+"Division by zero");for(l=0,u=n.e-i.e,j=k.length,w=E.length,d=(h=new A(P)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==o?o=A.precision:a?o+(um(n)-um(i))+1:o)<0)return new A(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<w||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),O=k[0],k[1]>=1e7/2&&++O;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/O|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?S:k,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,uO(h,a?o+um(h)+1:o)}}();function uv(t,e){var r,n,i,o,a,c=0,u=0,l=t.constructor,s=l.precision;if(um(t)>16)throw Error(ua+um(t));if(!t.s)return new l(c4);for(null==e?(un=!1,a=s):a=e,o=new l(.03125);t.abs().gte(.1);)t=t.times(o),u+=5;for(a+=Math.log(uu(2,u))/Math.LN10*2+5|0,r=n=i=new l(c4),l.precision=a;;){if(n=uO(n.times(t),a),r=r.times(++c),ud((o=i.plus(uy(n,r,a))).d).slice(0,a)===ud(i.d).slice(0,a)){for(;u--;)i=uO(i.times(i),a);return l.precision=s,null==e?(un=!0,uO(i,s)):i}i=o}}function um(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function ub(t,e,r){if(e>t.LN10.sd())throw un=!0,r&&(t.precision=r),Error(ui+"LN10 precision limit exceeded");return uO(new t(t.LN10),e)}function ug(t){for(var e="";t--;)e+="0";return e}function ux(t,e){var r,n,i,o,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(ui+(p.s?"NaN":"-Infinity"));if(p.eq(c4))return new d(0);if(null==e?(un=!1,l=y):l=e,p.eq(10))return null==e&&(un=!0),ub(d,l);if(d.precision=l+=10,n=(r=ud(h)).charAt(0),!(15e14>Math.abs(o=um(p))))return u=ub(d,l+2,y).times(o+""),p=ux(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(un=!0,uO(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=ud((p=p.times(t)).d)).charAt(0),f++;for(o=um(p),n>1?(p=new d("0."+r),o++):p=new d(n+"."+r.slice(1)),c=a=p=uy(p.minus(c4),p.plus(c4),l),s=uO(p.times(p),l),i=3;;){if(a=uO(a.times(s),l),ud((u=c.plus(uy(a,new d(i),l))).d).slice(0,l)===ud(c.d).slice(0,l))return c=c.times(2),0!==o&&(c=c.plus(ub(d,l+2,y).times(o+""))),c=uy(c,new d(f),l),d.precision=y,null==e?(un=!0,uO(c,y)):c;c=u,i+=2}}function uw(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=uc((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),un&&(t.e>us||t.e<-us))throw Error(ua+r)}else t.s=0,t.e=0,t.d=[0];return t}function uO(t,e,r){var n,i,o,a,c,u,l,s,f=t.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(o=f.length))return t;for(a=1,l=o=f[s];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=l/(o=uu(10,a-i-1))%10|0,u=e<0||void 0!==f[s+1]||l%o,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?l/uu(10,a-i):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(o=um(t),f.length=1,e=e-o-1,f[0]=uu(10,(7-e%7)%7),t.e=uc(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,o=1,s--):(f.length=s+1,o=uu(10,7-n),f[s]=i>0?(l/uu(10,a-i)%uu(10,i)|0)*o:0),u)for(;;)if(0==s){1e7==(f[0]+=o)&&(f[0]=1,++t.e);break}else{if(f[s]+=o,1e7!=f[s])break;f[s--]=0,o=1}for(n=f.length;0===f[--n];)f.pop();if(un&&(t.e>us||t.e<-us))throw Error(ua+um(t));return t}function uj(t,e){var r,n,i,o,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),un?uO(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(i=Math.max(Math.ceil(h/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((s=(i=u.length)<(c=f.length))&&(c=i),i=0;i<c;i++)if(u[i]!=f[i]){s=u[i]<f[i];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,i=f.length-c;i>0;--i)u[c++]=0;for(i=f.length;i>a;){if(u[--i]<f[i]){for(o=i;o&&0===u[--o];)u[o]=1e7-1;--u[o],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,un?uO(e,h):e):new p(0)}function uS(t,e,r){var n,i=um(t),o=ud(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+ug(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+ug(-i-1)+o,r&&(n=r-a)>0&&(o+=ug(n))):i>=a?(o+=ug(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+ug(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=ug(n))),t.s<0?"-"+o:o}function uA(t,e){if(t.length>e)return t.length=e,!0}function uP(t){if(!t||"object"!=typeof t)throw Error(ui+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(uc(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(uo+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(uo+r+": "+n);return this}var c5=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(uo+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return uw(this,t.toString())}if("string"!=typeof t)throw Error(uo+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,ul.test(t))uw(this,t);else throw Error(uo+t)}if(o.prototype=uf,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=uP,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c4=new c5(1);let uE=c5;function uk(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var uM=function(t){return t},u_={},uT=function(t){return t===u_},uN=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uT(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uC=function(t){return function t(e,r){return 1===e?r:uN(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==u_}).length;return a>=e?r.apply(void 0,i):t(e-a,uN(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return uT(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uk(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return uk(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uk(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uD=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uI=uC(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uB=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return uM;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},uL=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uR=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};uC(function(t,e,r){var n=+t;return n+r*(e-n)}),uC(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),uC(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uz={rangeStep:function(t,e,r){for(var n=new uE(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uE(t).abs().log(10).toNumber())+1}};function uU(t){return function(t){if(Array.isArray(t))return uW(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uF(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u$(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||uF(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uF(t,e){if(t){if("string"==typeof t)return uW(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uW(t,e)}}function uW(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uq(t){var e=u$(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function uX(t,e,r){if(t.lte(0))return new uE(0);var n=uz.getDigitCount(t.toNumber()),i=new uE(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new uE(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new uE(Math.ceil(c))}function uG(t,e,r){var n=1,i=new uE(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new uE(10).pow(uz.getDigitCount(t)-1),i=new uE(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new uE(Math.floor(t)))}else 0===t?i=new uE(Math.floor((e-1)/2)):r||(i=new uE(Math.floor(t)));var a=Math.floor((e-1)/2);return uB(uI(function(t){return i.add(new uE(t-a).mul(n)).toNumber()}),uD)(0,e)}var uH=uR(function(t){var e=u$(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=u$(uq([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uU(uD(0,i-1).map(function(){return 1/0}))):[].concat(uU(uD(0,i-1).map(function(){return-1/0})),[l]);return r>n?uL(s):s}if(u===l)return uG(u,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uE(0),tickMin:new uE(0),tickMax:new uE(0)};var c=uX(new uE(r).sub(e).div(n-1),i,a),u=Math.ceil((o=e<=0&&r>=0?new uE(0):(o=new uE(e).add(r).div(2)).sub(new uE(o).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uE(r).sub(o).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,i,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:o.sub(new uE(u).mul(c)),tickMax:o.add(new uE(l).mul(c))})}(u,l,a,o),p=f.step,h=f.tickMin,d=f.tickMax,y=uz.rangeStep(h,d.add(new uE(.1).mul(p)),p);return r>n?uL(y):y});uR(function(t){var e=u$(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=u$(uq([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uG(u,i,o);var s=uX(new uE(l).sub(u).div(a-1),o,0),f=uB(uI(function(t){return new uE(u).add(new uE(t).mul(s)).toNumber()}),uD)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uL(f):f});var uV=uR(function(t,e){var r=u$(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=u$(uq([n,i]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var l=Math.max(e,2),s=uX(new uE(u).sub(c).div(l-1),o,0),f=[].concat(uU(uz.rangeStep(new uE(c),new uE(u).sub(new uE(.99).mul(s)),s)),[u]);return n>i?uL(f):f}),uY=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uK(t){return(uK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uZ(){return(uZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uQ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uQ=function(){return!!t})()}function u0(t){return(u0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u1(t,e){return(u1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u2(t,e,r){return(e=u5(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u5(t){var e=function(t,e){if("object"!=uK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uK(e)?e:e+""}var u4=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=u0(t),function(t,e){if(e&&("object"===uK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uQ()?Reflect.construct(t,e||[],u0(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&u1(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,i=t.dataKey,o=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tM(function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,uY),!1);"x"===this.props.direction&&"number"!==u.type&&tW(!1);var f=o.map(function(t){var o,f,p=c(t,i),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uJ(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uJ(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=b[0],f=b[1]}else o=f=v;if("vertical"===r){var g=u.scale,x=d+e,w=x+n,O=x-n,j=g(y-o),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var A=l.scale,P=h+e,E=P-n,k=P+n,M=A(y-o),_=A(y+f);m.push({x1:E,y1:_,x2:k,y2:_}),m.push({x1:P,y1:M,x2:P,y2:_}),m.push({x1:E,y1:M,x2:k,y2:M})}return a().createElement(tY,uZ({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uZ({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tY,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u5(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function u3(t){return(u3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u6(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u3(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}u2(u4,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),u2(u4,"displayName","ErrorBar");var u7=function(t){var e,r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,o=t.legendContent,a=tS(r,rs);if(!a)return null;var c=rs.defaultProps,u=void 0!==c?u8(u8({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===o?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u8(u8({},r),e.props):{},i=n.dataKey,o=n.name,a=n.legendType;return{inactive:n.hide,dataKey:i,type:u.iconType||a||"square",color:lu(e),value:o||i,payload:n}}),u8(u8(u8({},u),rs.getWithHeight(a,i)),{},{payload:e,item:a})};function u9(t){return(u9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lt(t){return function(t){if(Array.isArray(t))return le(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return le(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return le(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ln(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lr(Object(r),!0).forEach(function(e){li(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function li(t,e,r){var n;return(n=function(t,e){if("object"!=u9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==u9(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lo(t,e,r){return tt()(t)||tt()(e)?r:W(e)?L()(t,e,r):tr()(e)?e(t):r}function la(t,e,r,n){var i=ut()(t,function(t){return lo(t,e)});if("number"===r){var o=i.filter(function(t){return F(t)||parseFloat(t)});return o.length?[c7()(o),c6()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!tt()(t)}):i).map(function(t){return W(t)||t instanceof Date?t:""})}var lc=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(U(s-l)!==U(f-s)){var h=[];if(U(f-s)===U(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},lu=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?ln(ln({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},ll=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var l=i[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tg(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?ln(ln({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var w=tt()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:tt()(w)?void 0:G(w,r,0)})}}return o},ls=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=G(r,i,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=i&&(h-=(u-1)*l,l=0),h>=i&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((i-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(lt(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=G(n,i,0,!0);i-2*y-(u-1)*l<=0&&(l=0);var v=(i-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(lt(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},lf=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=u7({children:i,legendWidth:o-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&F(t[f]))return ln(ln({},t),{},li({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&F(t[p]))return ln(ln({},t),{},li({},p,t[p]+(s||0)))}return t},lp=function(t,e,r,n,i){var o=tj(e.props.children,u4).filter(function(t){var e;return e=t.props.direction,!!tt()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(o&&o.length){var a=o.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=lo(e,r);if(tt()(n))return t;var i=Array.isArray(n)?[c7()(n),c6()(n)]:[n,n],o=a.reduce(function(t,r){var n=lo(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},lh=function(t,e,r,n,i){var o=e.map(function(e){return lp(t,e,r,i,n)}).filter(function(t){return!tt()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},ld=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&lp(t,e,o,n)||la(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},ly=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},lv=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},lm=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*U(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!I()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}})},lb=new WeakMap,lg=function(t,e){if("function"!=typeof e)return t;lb.has(t)||lb.set(t,new WeakMap);var r=lb.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},lx=function(t,e,r){var i=t.scale,o=t.type,a=t.layout,c=t.axisType;if("auto"===i)return"radial"===a&&"radiusAxis"===c?{scale:nz(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:oE(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nU(),realScaleType:"point"}:"category"===o?{scale:nz(),realScaleType:"band"}:{scale:oE(),realScaleType:"linear"};if(C()(i)){var u="scale".concat(eO()(i));return{scale:(n[u]||nU)(),realScaleType:n[u]?u:"point"}}return tr()(i)?{scale:i}:{scale:nU(),realScaleType:"point"}},lw=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},lO=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lj=function(t,e){if(!e||2!==e.length||!F(e[0])||!F(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!F(t[0])||t[0]<r)&&(i[0]=r),(!F(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},lS={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=I()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}cX(t,e)}},none:cX,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}cX(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<i;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=l/u)}r[a-1][1]+=r[a-1][0]=o,cX(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=I()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},lA=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=lS[r];return(function(){var t=eR([]),e=cH,r=cX,n=cV;function i(i){var o,a,c=Array.from(t.apply(this,arguments),cY),u=c.length,l=-1;for(let t of i)for(o=0,++l;o<u;++o)(c[o][l]=[0,+n(t,c[o].key,l,i)]).data=t;for(o=0,a=cG(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:eR(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:eR(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?cH:"function"==typeof t?t:eR(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?cX:t,i):r},i})().keys(n).value(function(t,e){return+lo(t,e,0)}).order(cH).offset(i)(t)},lP=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?ln(ln({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(W(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[X("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return ln(ln({},t),{},li({},c,u))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return ln(ln({},e),{},li({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lA(t,a.items,i)}))},{})),ln(ln({},e),{},li({},o,c))},{})},lE=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var l=uH(u,i,a);return t.domain([c7()(l),c6()(l)]),{niceTicks:l}}return i&&"number"===n?{niceTicks:uV(t.domain(),i,a)}:null};function lk(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!tt()(i[e.dataKey])){var c=K(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=lo(i,tt()(a)?e.dataKey:a);return tt()(u)?null:e.scale(u)}var lM=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=lo(o,e.dataKey,e.domain[a]);return tt()(c)?null:e.scale(c)-i/2+n},l_=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},lT=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?ln(ln({},t.type.defaultProps),t.props):t.props).stackId;if(W(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},lN=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[c7()(e.concat([t[0]]).filter(F)),c6()(e.concat([t[1]]).filter(F))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lC=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lD=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lI=function(t,e,r){if(tr()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(F(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lC.test(t[0])){var i=+lC.exec(t[0])[1];n[0]=e[0]-i}else tr()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(F(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lD.test(t[1])){var o=+lD.exec(t[1])[1];n[1]=e[1]+o}else tr()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lB=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=tF()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],l=i[a-1];o=Math.min((u.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},lL=function(t,e,r){return!t||!t.length||ur()(t,L()(r,"type.defaultProps.domain"))?e:t},lR=function(t,e){var r=t.type.defaultProps?ln(ln({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return ln(ln({},tM(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:lu(t),value:lo(e,n),type:c,payload:e,chartType:u,hide:l})};function lz(t){return(lz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lU(Object(r),!0).forEach(function(e){lF(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lF(t,e,r){var n;return(n=function(t,e){if("object"!=lz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lz(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lW=["Webkit","Moz","O","ms"],lq=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lW.reduce(function(t,n){return l$(l$({},t),{},lF({},n+r,e))},{});return n[t]=e,n};function lX(t){return(lX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lG(){return(lG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lH(Object(r),!0).forEach(function(e){lQ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lY(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,l0(n.key),n)}}function lK(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lK=function(){return!!t})()}function lZ(t){return(lZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lJ(t,e){return(lJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lQ(t,e,r){return(e=l0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l0(t){var e=function(t,e){if("object"!=lX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lX(e)?e:e+""}var l1=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nU().domain(tU()(0,c)).range([i,i+o-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},l2=function(t){return t.changedTouches&&!!t.changedTouches.length},l5=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=lZ(r),lQ(e=function(t,e){if(e&&("object"===lX(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lK()?Reflect.construct(r,i||[],lZ(this).constructor):r.apply(this,i)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lQ(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lQ(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),lQ(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lQ(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lQ(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lQ(e,"handleSlideDragStart",function(t){var r=l2(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lJ(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,i=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(i,u),f=n.getIndexInRange(i,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=lo(r[t],i,t);return tr()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=l2(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(lQ(lQ({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(lQ({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:o,x:e,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,c=t.data,u=t.children,l=t.padding,s=o.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:i,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,o=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lV(lV({},tM(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(i=h[y])?void 0:i.name);return a().createElement(tY,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),o.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,i=r.height,o=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:o,fillOpacity:.2,x:u,y:n,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,i=t.height,o=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tY,{className:"recharts-brush-texts"},a().createElement(ih,lG({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+i/2},f),this.getTextOfTick(e)),a().createElement(ih,lG({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+o+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,i=t.x,o=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!F(i)||!F(o)||!F(c)||!F(u)||c<=0||u<=0)return null;var m=M("recharts-brush",r),b=1===a().Children.count(n),g=lq("userSelect","none");return a().createElement(tY,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,i=t.height,o=t.stroke,c=Math.floor(r+i/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:i,fill:o,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tr()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lV({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?l1({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&lY(n.prototype,e),r&&lY(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function l4(t){return(l4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l3(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=l4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==l4(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}lQ(l5,"displayName","Brush"),lQ(l5,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var l8=Math.PI/180,l7=function(t,e,r,n){return{x:t+Math.cos(-l8*n)*r,y:e+Math.sin(-l8*n)*r}},l9=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},st=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=l9({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},se=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},sr=function(t,e){var r,n=st({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,c=e.outerRadius;if(i<a||i>c)return!1;if(0===i)return!0;var u=se(e),l=u.startAngle,s=u.endAngle,f=o;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?l6(l6({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function sn(t){return(sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var si=["offset"];function so(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sa(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sn(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sa(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function su(){return(su=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var sl=function(t){var e=t.value,r=t.formatter,n=tt()(t.children)?e:t.children;return tr()(r)?r(n):n},ss=function(t,e,r){var n,i,o=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+h)/2,b=U(y-d)*Math.min(Math.abs(y-d),360),g=b>=0?1:-1;"insideStart"===o?(n=d+g*u,i=v):"insideEnd"===o?(n=y-g*u,i=!v):"end"===o&&(n=y+g*u,i=v),i=b<=0?i:!i;var x=l7(s,f,m,n),w=l7(s,f,m,n+(i?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!i,",\n    ").concat(w.x,",").concat(w.y),j=tt()(t.id)?X("recharts-radial-line-"):t.id;return a().createElement("text",su({},r,{dominantBaseline:"central",className:M("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:j,d:O})),a().createElement("textPath",{xlinkHref:"#".concat(j)},e))},sf=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=l7(i,o,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=l7(i,o,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},sp=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return sc(sc({},{x:o+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return sc(sc({},{x:o+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===i){var m={x:o-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return sc(sc({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===i){var b={x:o+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return sc(sc({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===i?sc({x:o+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===i?sc({x:o+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===i?sc({x:o+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===i?sc({x:o+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===i?sc({x:o+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===i?sc({x:o+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===i?sc({x:o+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===i?sc({x:o+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):ti()(i)&&(F(i.x)||$(i.x))&&(F(i.y)||$(i.y))?sc({x:o+G(i.x,c),y:a+G(i.y,u),textAnchor:"end",verticalAnchor:"end"},g):sc({x:o+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function sh(t){var e,r=t.offset,n=sc({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,si)),i=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!i||tt()(u)&&tt()(l)&&!(0,o.isValidElement)(s)&&!tr()(s))return null;if((0,o.isValidElement)(s))return(0,o.cloneElement)(s,n);if(tr()(s)){if(e=(0,o.createElement)(s,n),(0,o.isValidElement)(e))return e}else e=sl(n);var h="cx"in i&&F(i.cx),d=tM(n,!0);if(h&&("insideStart"===c||"insideEnd"===c||"end"===c))return ss(n,e,d);var y=h?sf(n):sp(n);return a().createElement(ih,su({className:M("recharts-label",void 0===f?"":f)},d,y,{breakAll:p}),e)}sh.displayName="Label";var sd=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(F(d)&&F(y)){if(F(s)&&F(f))return{x:s,y:f,width:d,height:y};if(F(p)&&F(h))return{x:p,y:h,width:d,height:y}}return F(s)&&F(f)?{x:s,y:f,width:0,height:0}:F(e)&&F(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};sh.parseViewBox=sd,sh.renderCallByParent=function(t,e){var r,n,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var c=t.children,u=sd(t),l=tj(c,sh).map(function(t,r){return(0,o.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!i)return l;return[(r=t.label,n=e||u,!r?null:!0===r?a().createElement(sh,{key:"label-implicit",viewBox:n}):W(r)?a().createElement(sh,{key:"label-implicit",viewBox:n,value:r}):(0,o.isValidElement)(r)?r.type===sh?(0,o.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(sh,{key:"label-implicit",content:r,viewBox:n}):tr()(r)?a().createElement(sh,{key:"label-implicit",content:r,viewBox:n}):ti()(r)?a().createElement(sh,su({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return so(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return so(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return so(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var sy=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sv=r(69691),sm=r.n(sv),sb=r(47212),sg=r.n(sb),sx=function(t){return null};sx.displayName="Cell";var sw=r(5359),sO=r.n(sw);function sj(t){return(sj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sS=["valueAccessor"],sA=["data","dataKey","clockWise","id","textBreakAll"];function sP(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sE(){return(sE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sk(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sj(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s_(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var sT=function(t){return Array.isArray(t.value)?sO()(t.value):t.value};function sN(t){var e=t.valueAccessor,r=void 0===e?sT:e,n=s_(t,sS),i=n.data,o=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=s_(n,sA);return i&&i.length?a().createElement(tY,{className:"recharts-label-list"},i.map(function(t,e){var n=tt()(o)?r(t,e):lo(t&&t.payload,o),i=tt()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(sh,sE({},tM(t,!0),s,i,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:sh.parseViewBox(tt()(c)?t:sM(sM({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sN.displayName="LabelList",sN.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=tj(t.children,sN).map(function(t,r){return(0,o.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(sN,{key:"labelList-implicit",data:e}):a().isValidElement(r)||tr()(r)?a().createElement(sN,{key:"labelList-implicit",data:e,content:r}):ti()(r)?a().createElement(sN,sE({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sP(t)}(i)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return sP(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sP(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):i};var sC=r(38404),sD=r.n(sC),sI=r(98451),sB=r.n(sI);function sL(t){return(sL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sR(){return(sR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sz(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sU(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sL(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sF=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},sW={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sq=function(t){var e,r=s$(s$({},sW),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sz(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sz(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=M("recharts-trapezoid",d);return b?a().createElement(nO,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,i=t.lowerWidth,o=t.height,u=t.x,l=t.y;return a().createElement(nO,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",sR({},tM(r,!0),{className:g,d:sF(u,l,e,i,o),ref:n})))}):a().createElement("g",null,a().createElement("path",sR({},tM(r,!0),{className:g,d:sF(l,s,f,p,h)})))};function sX(t){return(sX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sG(){return(sG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sH(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sX(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sY=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/l8,f=u?i:i+o*s;return{center:l7(e,r,l,f),circleTangency:l7(e,r,n,f),lineTangency:l7(e,r,l*Math.cos(s*l8),u?i-o*s:i),theta:s}},sK=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=t.endAngle,c=U(a-o)*Math.min(Math.abs(a-o),359.999),u=o+c,l=l7(e,r,i,o),s=l7(e,r,i,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(o>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=l7(e,r,n,o),h=l7(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(o<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sZ=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=U(l-u),f=sY({cx:e,cy:r,radius:i,angle:u,sign:s,cornerRadius:o,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sY({cx:e,cy:r,radius:i,angle:l,sign:-s,cornerRadius:o,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):sK({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sY({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,A=sY({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),P=A.circleTangency,E=A.lineTangency,k=A.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-k;if(M<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sJ={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sQ=function(t){var e,r=sV(sV({},sJ),t),n=r.cx,i=r.cy,o=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,h=r.className;if(c<o||f===p)return null;var d=M("recharts-sector",h),y=c-o,v=G(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?sZ({cx:n,cy:i,innerRadius:o,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):sK({cx:n,cy:i,innerRadius:o,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sG({},tM(r,!0),{className:d,d:e,role:"img"}))},s0=["option","shapeType","propTransformer","activeClassName","isActive"];function s1(t){return(s1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s2(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s1(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s1(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s4(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nT,r);case"trapezoid":return a().createElement(sq,r);case"sector":return a().createElement(sQ,r);case"symbols":if("symbols"===e)return a().createElement(eQ,r);break;default:return null}}function s3(t){var e,r=t.option,n=t.shapeType,i=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,s0);if((0,o.isValidElement)(r))e=(0,o.cloneElement)(r,s5(s5({},l),(0,o.isValidElement)(r)?r.props:r));else if(tr()(r))e=r(l);else if(sD()(r)&&!sB()(r)){var s=(void 0===i?function(t,e){return s5(s5({},e),t)}:i)(r,l);e=a().createElement(s4,{shapeType:n,elementProps:s})}else e=a().createElement(s4,{shapeType:n,elementProps:l});return u?a().createElement(tY,{className:void 0===c?"recharts-active-shape":c},e):e}function s6(t,e){return null!=e&&"trapezoids"in t.props}function s8(t,e){return null!=e&&"sectors"in t.props}function s7(t,e){return null!=e&&"points"in t.props}function s9(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function ft(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function fe(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}var fr=["x","y"];function fn(t){return(fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fi(){return(fi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fo(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=fn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fn(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fo(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fc(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,fr),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),u=parseInt("".concat(e.width||i.width),10);return fa(fa(fa(fa(fa({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function fu(t){return a().createElement(s3,fi({shapeType:"rectangle",propTransformer:fc,activeClassName:"recharts-active-bar"},t))}var fl=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||tW(!1),e)}},fs=["value","background"];function ff(t){return(ff="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fp(){return(fp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fh(Object(r),!0).forEach(function(e){fg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fx(n.key),n)}}function fv(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fv=function(){return!!t})()}function fm(t){return(fm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fb(t,e){return(fb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fg(t,e,r){return(e=fx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fx(t){var e=function(t,e){if("object"!=ff(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ff(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ff(e)?e:e+""}var fw=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=fm(e),fg(t=function(t,e){if(e&&("object"===ff(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fv()?Reflect.construct(e,r||[],fm(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fg(t,"id",X("recharts-bar-")),fg(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fg(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fb(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,o=r.activeIndex,c=r.activeBar,u=tM(this.props,!1);return t&&t.map(function(t,r){var l=r===o,s=fd(fd(fd({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tY,fp({className:"recharts-bar-rectangle"},th(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(fu,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,o=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(nO,{begin:o,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=s&&s[e];if(r){var o=Y(r.x,t.x),a=Y(r.y,t.y),c=Y(r.width,t.width),u=Y(r.height,t.height);return fd(fd({},t),{},{x:o(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===n){var l=Y(0,t.height)(i);return fd(fd({},t),{},{y:t.y+t.height-l,height:l})}var f=Y(0,t.width)(i);return fd(fd({},t),{},{width:f})});return a().createElement(tY,null,t.renderRectanglesStatically(o))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!ur()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,o=tM(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,fs);if(!c)return null;var l=fd(fd(fd(fd(fd({},u),{},{fill:"#eee"},c),o),th(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(fu,fp({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,o=r.yAxis,c=r.layout,u=tj(r.children,u4);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:lo(t,e)}};return a().createElement(tY,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.xAxis,o=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,y=M("recharts-bar",n),v=i&&i.allowDataOverflow,m=o&&o.allowDataOverflow,b=v||m,g=tt()(h)?this.id:h;return a().createElement(tY,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,a().createElement(tY,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||d)&&sN.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fy(n.prototype,e),r&&fy(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function fO(t){return(fO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fj(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fE(n.key),n)}}function fS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fS(Object(r),!0).forEach(function(e){fP(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fP(t,e,r){return(e=fE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fE(t){var e=function(t,e){if("object"!=fO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fO(e)?e:e+""}fg(fw,"displayName","Bar"),fg(fw,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ec.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fg(fw,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=lO(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fd(fd({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:o,w=l?x.scale.domain():null,O=l_({numericAxis:x}),j=tj(b,sx),S=f.map(function(t,e){l?f=lj(l[s+e],w):Array.isArray(f=lo(t,m))||(f=[O,f]);var n=fl(g,fw.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,A=[a.scale(f[0]),a.scale(f[1])],P=A[0],E=A[1];p=lM({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:t,index:e}),y=null!=(S=null!=E?E:P)?S:void 0,v=h.size;var k=P-E;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=U(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var _=[o.scale(f[0]),o.scale(f[1])],T=_[0],N=_[1];if(p=T,y=lM({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:t,index:e}),v=N-T,b=h.size,x={x:o.x,y:y,width:o.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var C=U(v||n)*(Math.abs(n)-Math.abs(v));v+=C}}return fd(fd(fd({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lR(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fd({data:S,layout:d},p)});var fk=function(t,e,r,n,i){var o=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tS(u,fw);return l.reduce(function(o,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,A=y.categoricalDomain.sort(Z);if(A.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(A[e-1]||0),S))}),Number.isFinite(S)){var P=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=P*E/2),"no-gap"===y.padding){var k=G(t.barCategoryGap,P*E),M=P*E/2;u=M-k-(M-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,w&&(l=[l[1],l[0]]);var _=lx(y,i,f),T=_.scale,N=_.realScaleType;T.domain(m).range(l),lw(T);var C=lE(T,fA(fA({},y),{},{realScaleType:N}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[O]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[O]-d*y.width,h=r.top);var D=fA(fA(fA({},y),C),{},{realScaleType:N,x:p,y:h,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=lB(D,C),y.hide||"xAxis"!==n?y.hide||(s[O]+=(d?-1:1)*D.width):s[O]+=(d?-1:1)*D.height,fA(fA({},o),{},fP({},a,D))},{})},fM=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},f_=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fj(r.prototype,t),e&&fj(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fP(f_,"EPS",1e-4);var fT=function(t){var e=Object.keys(t).reduce(function(e,r){return fA(fA({},e),{},fP({},r,f_.create(t[r])))},{});return fA(fA({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return sm()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return sg()(t,function(t,r){return e[r].isInRange(t)})}})},fN=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))};function fC(){return(fC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fD(t){return(fD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fI(Object(r),!0).forEach(function(e){fU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fL(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fL=function(){return!!t})()}function fR(t){return(fR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fz(t,e){return(fz=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fU(t,e,r){return(e=f$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f$(t){var e=function(t,e){if("object"!=fD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fD(e)?e:e+""}var fF=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=fT({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return sy(t,"discard")&&!o.isInRange(a)?null:a},fW=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fR(t),function(t,e){if(e&&("object"===fD(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fL()?Reflect.construct(t,e||[],fR(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fz(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.r,o=t.alwaysShow,c=t.clipPathId,u=W(e),l=W(n);if(J(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fF(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=fB(fB({clipPath:sy(this.props,"hidden")?"url(#".concat(c,")"):void 0},tM(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tY,{className:M("recharts-reference-dot",y)},r.renderDot(d,v),sh.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f$(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fU(fW,"displayName","ReferenceDot"),fU(fW,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fU(fW,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tr()(t)?t(e):a().createElement(rp,fC({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fq=r(67367),fX=r.n(fq),fG=r(22964),fH=r.n(fG),fV=r(86451),fY=r.n(fV)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fK=(0,o.createContext)(void 0),fZ=(0,o.createContext)(void 0),fJ=(0,o.createContext)(void 0),fQ=(0,o.createContext)({}),f0=(0,o.createContext)(void 0),f1=(0,o.createContext)(0),f2=(0,o.createContext)(0),f5=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,i=e.offset,o=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fY(i);return a().createElement(fK.Provider,{value:r},a().createElement(fZ.Provider,{value:n},a().createElement(fQ.Provider,{value:i},a().createElement(fJ.Provider,{value:s},a().createElement(f0.Provider,{value:o},a().createElement(f1.Provider,{value:l},a().createElement(f2.Provider,{value:u},c)))))))},f4=function(t){var e=(0,o.useContext)(fK);null==e&&tW(!1);var r=e[t];return null==r&&tW(!1),r},f3=function(){var t=(0,o.useContext)(fZ);return fH()(t,function(t){return sg()(t.domain,Number.isFinite)})||H(t)},f6=function(t){var e=(0,o.useContext)(fZ);null==e&&tW(!1);var r=e[t];return null==r&&tW(!1),r},f8=function(){return(0,o.useContext)(f2)},f7=function(){return(0,o.useContext)(f1)};function f9(t){return(f9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pt=function(){return!!t})()}function pe(t){return(pe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pr(t,e){return(pr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pn(Object(r),!0).forEach(function(e){po(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function po(t,e,r){return(e=pa(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pa(t){var e=function(t,e){if("object"!=f9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f9(e)?e:e+""}function pc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function pu(){return(pu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var pl=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tr()(t)?t(e):a().createElement("line",pu({},e,{className:"recharts-reference-line-line"}))},ps=function(t,e,r,n,i,o,a,c,u){var l=i.x,s=i.y,f=i.width,p=i.height;if(r){var h=u.y,d=t.y.apply(h,{position:o});if(sy(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:o});if(sy(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:o})});return sy(u,"discard")&&fX()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pf(t){var e,r=t.x,n=t.y,i=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,o.useContext)(f0),h=f4(c),d=f6(u),y=(0,o.useContext)(fJ);if(!p||!y)return null;J(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=ps(fT({x:h.scale,y:d.scale}),W(r),W(n),i&&2===i.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return pc(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pc(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],O=w.x,j=w.y,S=pi(pi({clipPath:sy(t,"hidden")?"url(#".concat(p,")"):void 0},tM(t,!0)),{},{x1:g,y1:x,x2:O,y2:j});return a().createElement(tY,{className:M("recharts-reference-line",s)},pl(l,S),sh.renderCallByParent(t,fM({x:(e={x1:g,y1:x,x2:O,y2:j}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pp=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=pe(t),function(t,e){if(e&&("object"===f9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pt()?Reflect.construct(t,e||[],pe(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pr(r,t),e=[{key:"render",value:function(){return a().createElement(pf,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pa(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function ph(){return(ph=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pd(t){return(pd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function py(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?py(Object(r),!0).forEach(function(e){px(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):py(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}po(pp,"displayName","ReferenceLine"),po(pp,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pm=function(){return!!t})()}function pb(t){return(pb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pg(t,e){return(pg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function px(t,e,r){return(e=pw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pw(t){var e=function(t,e){if("object"!=pd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pd(e)?e:e+""}var pO=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,l=i.xAxis,s=i.yAxis;if(!l||!s)return null;var f=fT({x:l.scale,y:s.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sy(i,"discard")||f.isInRange(p)&&f.isInRange(h)?fM(p,h):null},pj=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=pb(t),function(t,e){if(e&&("object"===pd(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pm()?Reflect.construct(t,e||[],pb(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pg(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,i=t.y1,o=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;J(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=W(e),f=W(n),p=W(i),h=W(o),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=pO(s,f,p,h,this.props);if(!y&&!d)return null;var v=sy(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tY,{className:M("recharts-reference-area",c)},r.renderRect(d,pv(pv({clipPath:v},tM(this.props,!0)),y)),sh.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pw(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pS(t){return function(t){if(Array.isArray(t))return pA(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pA(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pA(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}px(pj,"displayName","ReferenceArea"),px(pj,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),px(pj,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tr()(t)?t(e):a().createElement(nT,ph({},e,{className:"recharts-reference-area-rect"}))});var pP=function(t,e,r,n,i){var o=tj(t,pp),a=tj(t,fW),c=[].concat(pS(o),pS(a)),u=tj(t,pj),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sy(e.props,"extendDomain")&&F(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sy(e.props,"extendDomain")&&F(e.props[p])&&F(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return F(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pE=r(11117),pk=new(r.n(pE)()),pM="recharts.syncMouseEvents";function p_(t){return(p_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pT(t,e,r){return(e=pN(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pN(t){var e=function(t,e){if("object"!=p_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p_(e)?e:e+""}var pC=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pT(this,"activeIndex",0),pT(this,"coordinateList",[]),pT(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pN(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pD(){}function pI(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pB(t){this._context=t}function pL(t){this._context=t}function pR(t){this._context=t}pB.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pI(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pI(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pL.prototype={areaStart:pD,areaEnd:pD,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pI(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pR.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pI(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pz{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pU(t){this._context=t}function p$(t){this._context=t}function pF(t){return new p$(t)}pU.prototype={areaStart:pD,areaEnd:pD,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pW(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function pq(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pX(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function pG(t){this._context=t}function pH(t){this._context=new pV(t)}function pV(t){this._context=t}function pY(t){this._context=t}function pK(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function pZ(t,e){this._context=t,this._t=e}function pJ(t){return t[0]}function pQ(t){return t[1]}function p0(t,e){var r=eR(!0),n=null,i=pF,o=null,a=eq(c);function c(c){var u,l,s,f=(c=cG(c)).length,p=!1;for(null==n&&(o=i(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(l,u,c),+e(l,u,c));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?pJ:eR(t),e="function"==typeof e?e:void 0===e?pQ:eR(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eR(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eR(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function p1(t,e,r){var n=null,i=eR(!0),o=null,a=pF,c=null,u=eq(l);function l(l){var s,f,p,h,d,y=(l=cG(l)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&i(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return p0().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?pJ:eR(+t),e="function"==typeof e?e:void 0===e?eR(0):eR(+e),r="function"==typeof r?r:void 0===r?pQ:eR(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eR(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eR(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eR(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eR(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:eR(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),l):o},l}function p2(t){return(p2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p5(){return(p5=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p3(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p4(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=p2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p2(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}p$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pG.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pX(this,this._t0,pq(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pX(this,pq(this,r=pW(this,t,e)),r);break;default:pX(this,this._t0,r=pW(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pH.prototype=Object.create(pG.prototype)).point=function(t,e){pG.prototype.point.call(this,e,t)},pV.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},pY.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pK(t),i=pK(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pZ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var p6={curveBasisClosed:function(t){return new pL(t)},curveBasisOpen:function(t){return new pR(t)},curveBasis:function(t){return new pB(t)},curveBumpX:function(t){return new pz(t,!0)},curveBumpY:function(t){return new pz(t,!1)},curveLinearClosed:function(t){return new pU(t)},curveLinear:pF,curveMonotoneX:function(t){return new pG(t)},curveMonotoneY:function(t){return new pH(t)},curveNatural:function(t){return new pY(t)},curveStep:function(t){return new pZ(t,.5)},curveStepAfter:function(t){return new pZ(t,1)},curveStepBefore:function(t){return new pZ(t,0)}},p8=function(t){return t.x===+t.x&&t.y===+t.y},p7=function(t){return t.x},p9=function(t){return t.y},ht=function(t,e){if(tr()(t))return t;var r="curve".concat(eO()(t));return("curveMonotone"===r||"curveBump"===r)&&e?p6["".concat(r).concat("vertical"===e?"Y":"X")]:p6[r]||pF},he=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=ht(void 0===r?"linear":r,a),s=u?i.filter(function(t){return p8(t)}):i;if(Array.isArray(o)){var f=u?o.filter(function(t){return p8(t)}):o,p=s.map(function(t,e){return p3(p3({},t),{},{base:f[e]})});return(e="vertical"===a?p1().y(p9).x1(p7).x0(function(t){return t.base.x}):p1().x(p7).y1(p9).y0(function(t){return t.base.y})).defined(p8).curve(l),e(p)}return(e="vertical"===a&&F(o)?p1().y(p9).x1(p7).x0(o):F(o)?p1().x(p7).y1(p9).y0(o):p0().x(p7).y(p9)).defined(p8).curve(l),e(s)},hr=function(t){var e=t.className,r=t.points,n=t.path,i=t.pathRef;if((!r||!r.length)&&!n)return null;var o=r&&r.length?he(t):n;return a().createElement("path",p5({},tM(t,!1),tp(t),{className:M("recharts-curve",e),d:o,ref:i}))};function hn(t){return(hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var hi=["x","y","top","left","width","height","className"];function ho(){return(ho=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ha(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var hc=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,i=void 0===n?0:n,o=t.top,c=void 0===o?0:o,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ha(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hn(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ha(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,hi));return F(r)&&F(i)&&F(f)&&F(h)&&F(c)&&F(l)?a().createElement("path",ho({},tM(y,!0),{className:M("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(i,"h").concat(f)})):null};function hu(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[l7(e,r,n,i),l7(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}function hl(t){return(hl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hs(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hl(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hs(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hp(t){var e,r,n,i,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var m=hr;if("ScatterChart"===y)i=l,m=hc;else if("BarChart"===y)e=h/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=nT;else if("radial"===d){var b=hu(l),g=b.cx,x=b.cy,w=b.radius;i={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=sQ}else i={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return hu(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=l7(c,u,l,f),h=l7(c,u,s,f);n=p.x,i=p.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(d,l,f)},m=hr;var O=hf(hf(hf(hf({stroke:"#ccc",pointerEvents:"none"},f),i),tM(v,!1)),{},{payload:s,payloadIndex:p,className:M("recharts-tooltip-cursor",v.className)});return(0,o.isValidElement)(v)?(0,o.cloneElement)(v,O):(0,o.createElement)(m,O)}var hh=["item"],hd=["children","className","width","height","style","compact","title","desc"];function hy(t){return(hy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hv(){return(hv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hm(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||hj(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hb(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function hg(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hg=function(){return!!t})()}function hx(t){return(hx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hw(t,e){return(hw=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hO(t){return function(t){if(Array.isArray(t))return hS(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hj(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hj(t,e){if(t){if("string"==typeof t)return hS(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hS(t,e)}}function hS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hA(Object(r),!0).forEach(function(e){hE(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hE(t,e,r){return(e=hk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hk(t){var e=function(t,e){if("object"!=hy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hy(e)?e:e+""}var hM={xAxis:["bottom","top"],yAxis:["left","right"]},h_={width:"100%",height:"100%"},hT={x:0,y:0};function hN(t){return t}var hC=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return hP(hP(hP({},n),l7(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return hP(hP(hP({},n),l7(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hT},hD=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hO(t),hO(r)):t},[]);return o.length>0?o:t&&t.length&&F(n)&&F(i)?t.slice(n,i+1):[]};function hI(t){return"number"===t?[0,"auto"]:void 0}var hB=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=hD(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=o.dataKey&&!o.allowDuplicatedCategory?K(void 0===s?a:s,o.dataKey,n):s&&s[r]||a[r])?[].concat(hO(i),[lR(c,l)]):i},[])},hL=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=lc(o,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hB(t,e,l,s),p=hC(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hR=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=ly(l,i);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hP(hP({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,w=h[o];if(e[w])return e;var O=hD(t.data,{graphicalItems:n.filter(function(t){var e;return(o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o])===w}),dataStartIndex:c,dataEndIndex:u}),j=O.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&F(n)&&F(i))return!0}return!1})(h.domain,v,d)&&(P=lI(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(k=la(O,y,"category")));var S=hI(d);if(!P||0===P.length){var A,P,E,k,M,_=null!=(M=h.domain)?M:S;if(y){if(P=la(O,y,d),"category"===d&&p){var T=V(P);m&&T?(E=P,P=tU()(0,j)):m||(P=lL(_,P,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hO(t),[e])},[]))}else if("category"===d)P=m?P.filter(function(t){return""!==t&&!tt()(t)}):lL(_,P,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||tt()(e)?t:[].concat(hO(t),[e])},[]);else if("number"===d){var N=lh(O,n.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===w&&(x||!i)}),y,i,l);N&&(P=N)}p&&("number"===d||"auto"!==b)&&(k=la(O,y,"category"))}else P=p?tU()(0,j):a&&a[w]&&a[w].hasStack&&"number"===d?"expand"===f?[0,1]:lN(a[w].stackGroups,c,u):ld(O,n.filter(function(t){var e=o in t.props?t.props[o]:t.type.defaultProps[o],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),d,l,!0);"number"===d?(P=pP(s,P,w,i,g),_&&(P=lI(_,P,v))):"category"===d&&_&&P.every(function(t){return _.indexOf(t)>=0})&&(P=_)}return hP(hP({},e),{},hE({},w,hP(hP({},h),{},{axisType:i,domain:P,categoricalDomain:k,duplicateDomain:E,originalDomain:null!=(A=h.domain)?A:S,isCategorical:p,layout:l})))},{})},hz=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hD(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=ly(l,i),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hP(hP({},e.type.defaultProps),e.props):e.props)[o],m=hI("number");return t[v]?t:(d++,y=h?tU()(0,p):a&&a[v]&&a[v].hasStack?pP(s,y=lN(a[v].stackGroups,c,u),v,i):pP(s,y=lI(m,ld(f,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!i}),"number",l),n.defaultProps.allowDataOverflow),v,i),hP(hP({},t),{},hE({},v,hP(hP({axisType:i},n.defaultProps),{},{hide:!0,orientation:L()(hM,"".concat(i,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hU=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tj(l,i),p={};return f&&f.length?p=hR(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=hz(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},h$=function(t){var e=H(t),r=lm(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tF()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lB(e,r)}},hF=function(t){var e=t.children,r=t.defaultShowTooltip,n=tS(e,l5),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},hW=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hq=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tS(s,l5),h=tS(s,rs),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hP(hP({},t),{},hE({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:hP(hP({},t),{},hE({},n,L()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hP(hP({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||l5.defaultProps.height),h&&e&&(v=lf(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hP(hP({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},hX=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,i=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hW(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tg(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hD(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hP(hP({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],A=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=x["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||tW(!1);var o=n[i];return hP(hP({},t),{},hE(hE({},r.axisType,o),"".concat(r.axisType,"Ticks"),lm(o)))},{}),P=A[v],E=A["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&lT(r,n[j].stackGroups),M=tg(r.type).indexOf("Bar")>=0,_=lB(P,E),T=[],N=m&&ll({barSize:u,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(M){var C,D,I=tt()(O)?h:O,B=null!=(C=null!=(D=lB(P,E,!0))?D:I)?C:0;T=ls({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:N[S],maxBarSize:I}),B!==_&&(T=T.map(function(t){return hP(hP({},t),{},{position:hP(hP({},t.position),{},{offset:t.position.offset-B/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:hP(hP({},L(hP(hP({},A),{},{displayedData:g,props:t,dataKey:w,item:r,bandSize:_,barPosition:T,offset:i,stackedData:k,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hE(hE(hE({key:r.key||"item-".concat(d)},y,A[y]),v,A[v]),"animationId",o)),childIndex:tO(t.children).indexOf(r),item:r})}),b},d=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tA({props:i}))return null;var u=i.children,s=i.layout,p=i.stackOffset,d=i.data,y=i.reverseStackOrder,v=hW(s),m=v.numericAxisName,b=v.cateAxisName,g=tj(u,r),x=lP(d,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hP(hP({},t),{},hE({},r,hU(i,hP(hP({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:o,dataEndIndex:a}))))},{}),O=hq(hP(hP({},w),{},{props:i,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=f(i,w[t],O,t.replace("Map",""),e)});var j=h$(w["".concat(b,"Map")]),S=h(i,hP(hP({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return hP(hP({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(t){var r;function n(t){var r,i,c,u,l;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return u=n,l=[t],u=hx(u),hE(c=function(t,e){if(e&&("object"===hy(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hg()?Reflect.construct(u,l||[],hx(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hE(c,"accessibilityManager",new pC),hE(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;c.setState(hP({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:i},hP(hP({},c.state),{},{legendBBox:t}))))}}),hE(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hE(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return hP({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hE(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=hP(hP({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;tr()(n)&&n(r,t)}}),hE(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?hP(hP({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;tr()(n)&&n(r,t)}),hE(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hE(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hE(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hE(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;tr()(r)&&r(e,t)}),hE(c,"handleOuterEvent",function(t){var e,r,n=tC(t),i=L()(c.props,"".concat(n));n&&tr()(i)&&i(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),hE(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=hP(hP({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;tr()(n)&&n(r,t)}}),hE(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;tr()(e)&&e(c.getMouseInfo(t),t)}),hE(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;tr()(e)&&e(c.getMouseInfo(t),t)}),hE(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hE(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hE(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hE(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;tr()(e)&&e(c.getMouseInfo(t),t)}),hE(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;tr()(e)&&e(c.getMouseInfo(t),t)}),hE(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pk.emit(pM,c.props.syncId,t,c.eventEmitterSymbol)}),hE(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,i=c.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(hP({dataStartIndex:o,dataEndIndex:a},d({props:c.props,dataStartIndex:o,dataEndIndex:a,updateId:i},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=hP(hP({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=hB(c.state,c.props.data,s),w=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hT;c.setState(hP(hP({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hE(c,"renderCursor",function(t){var r,n=c.state,i=n.isTooltipActive,o=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!=(r=t.props.active)?r:i,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(hp,{key:y,activeCoordinate:o,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hE(c,"renderPolarAxis",function(t,e,r){var n=L()(t,"type.axisType"),i=L()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?hP(hP({},a),t.props):t.props,l=i&&i[u["".concat(n,"Id")]];return(0,o.cloneElement)(t,hP(hP({},l),{},{className:M(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:lm(l,!0)}))}),hE(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,i=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=H(u),f=H(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,o.cloneElement)(t,{polarAngles:Array.isArray(n)?n:lm(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:lm(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hE(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,i=e.height,a=c.props.margin||{},u=u7({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=hb(u,hh);return(0,o.cloneElement)(l,hP(hP({},f),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hE(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,i=tS(r,ex);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=i.props.active)?t:u;return(0,o.cloneElement)(i,{viewBox:hP(hP({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hE(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,o.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lg(c.handleBrushChange,t.props.onChange),data:n,x:F(t.props.x)?t.props.x:a.left,y:F(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:F(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hE(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,o.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hE(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,i=t.basePoint,o=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hP(hP({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hP(hP({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:lu(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tM(s,!1)),tp(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(o))),i?c.push(n.renderActiveDot(s,hP(hP({},f),{},{cx:i.x,cy:i.y}),"".concat(u,"-basePoint-").concat(o))):a&&c.push(null),c}),hE(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=tS(c.props.children,ex),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hP(hP({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O=!!(!g&&u&&p&&(b||x||w)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:lg(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(j={onMouseLeave:lg(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lg(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,o.cloneElement)(t,hP(hP({},n.props),j));if(O)if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var A="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());E=K(d,A,f),k=y&&v&&K(v,A,f)}else E=null==d?void 0:d[s],k=y&&v&&v[s];if(w||x){var P=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,o.cloneElement)(t,hP(hP(hP({},n.props),j),{},{activeIndex:P})),null,null]}if(!tt()(E))return[S].concat(hO(c.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:s,isRange:y})))}else{var E,k,M,_=(null!=(M=c.getItemByXY(c.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,T=_.item,N=void 0===T?t:T,C=_.childIndex,D=hP(hP(hP({},n.props),j),{},{activeIndex:C});return[(0,o.cloneElement)(N,D),null,null]}return y?[S,null,null]:[S,null]}),hE(c,"renderCustomized",function(t,e,r){return(0,o.cloneElement)(t,hP(hP({key:"recharts-customized-".concat(r)},c.props),c.state))}),hE(c,"renderMap",{CartesianGrid:{handler:hN,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hN},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hN},YAxis:{handler:hN},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:X("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=T()(c.triggeredAfterMouseMove,null!=(i=t.throttleDelay)?i:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hw(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=tS(e,ex);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hB(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hP(hP({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){t_([tS(t.children,ex)],[tS(this.props.children,ex)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tS(this.props.children,ex);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:i}return i}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hL(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=H(u).scale,h=H(l).scale,d=p&&p.invert?p.invert(i.chartX):null,y=h&&h.invert?h.invert(i.chartY):null;return hP(hP({},i),{},{xValue:d,yValue:y},f)}return f?hP(hP({},i),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?sr({x:i,y:o},H(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tS(t,ex),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hP(hP({},tp(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pk.on(pM,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pk.removeListener(pM,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===tg(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,i=e.height,o=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:i,width:o})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hm(e,2),n=r[0],i=r[1];return hP(hP({},t),{},hE({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hm(e,2),n=r[0],i=r[1];return hP(hP({},t),{},hE({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hP(hP({},u.type.defaultProps),u.props):u.props,s=tg(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nM(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return sr(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(s6(a,n)||s8(a,n)||s7(a,n)){var h=function(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(s6(o,i)?e="trapezoids":s8(o,i)?e="sectors":s7(o,i)&&(e="points"),e),u=s6(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:s8(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:s7(o,i)?i.payload:{},l=a.filter(function(t,e){var r=ur()(u,t),n=o.props[c].filter(function(t){var e;return(s6(o,i)?e=s9:s8(o,i)?e=ft:s7(o,i)&&(e=fe),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hP(hP({},a),{},{childIndex:d}),payload:s7(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tA(this))return null;var n=this.props,i=n.children,o=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=tM(hb(n,hd),!1);if(s)return a().createElement(f5,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tG,hv({},h,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tN(i,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!=(t=this.props.tabIndex)?t:0,h.role=null!=(e=this.props.role)?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return a().createElement(f5,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",hv({className:M("recharts-wrapper",o),style:hP({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(t){r.container=t}}),a().createElement(tG,hv({},h,{width:c,height:u,title:f,desc:p,style:h_}),this.renderClipPath(),tN(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hk(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);hE(y,"displayName",e),hE(y,"defaultProps",hP({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hE(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,o=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hF(t);return hP(hP(hP({},p),{},{updateId:0},d(hP(hP({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||o!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!ta(l,e.prevMargin)){var h=hF(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hP(hP({},hL(e,n,c)),{},{updateId:e.updateId+1}),m=hP(hP(hP({},h),y),v);return hP(hP(hP({},m),d(hP({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:i})}if(!t_(i,e.prevChildren)){var b,g,x,w,O=tS(i,l5),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:s,S=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:f,A=tt()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hP(hP({updateId:A},d(hP(hP({props:t},e),{},{updateId:A,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:S})}return null}),hE(y,"renderActiveDot",function(t,e,r){var n;return n=(0,o.isValidElement)(t)?(0,o.cloneElement)(t,e):tr()(t)?t(e):a().createElement(rp,e),a().createElement(tY,{className:"recharts-active-dot",key:r},n)});var v=(0,o.forwardRef)(function(t,e){return a().createElement(y,hv({},t,{ref:e}))});return v.displayName=y.displayName,v},hG=["type","layout","connectNulls","ref"],hH=["key"];function hV(t){return(hV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hY(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function hK(){return(hK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hZ(Object(r),!0).forEach(function(e){h3(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hQ(t){return function(t){if(Array.isArray(t))return h0(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return h0(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h0(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h0(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h1(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h6(n.key),n)}}function h2(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h2=function(){return!!t})()}function h5(t){return(h5=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h4(t,e){return(h4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h3(t,e,r){return(e=h6(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h6(t){var e=function(t,e){if("object"!=hV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hV(e)?e:e+""}var h8=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=h5(e),h3(t=function(t,e){if(e&&("object"===hV(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h2()?Reflect.construct(e,r||[],h5(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),h3(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),h3(t,"getStrokeDasharray",function(e,r,i){var o=i.reduce(function(t,e){return t+e});if(!o)return t.generateSimpleStrokeDasharray(r,e);for(var a=Math.floor(e/o),c=e%o,u=r-e,l=[],s=0,f=0;s<i.length;f+=i[s],++s)if(f+i[s]>c){l=[].concat(hQ(i.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(hQ(n.repeat(i,a)),hQ(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),h3(t,"id",X("recharts-line-")),h3(t,"pathRef",function(e){t.mainCurve=e}),h3(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),h3(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&h4(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,i=r.xAxis,o=r.yAxis,c=r.layout,u=tj(r.children,u4);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:lo(t.payload,e)}};return a().createElement(tY,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.dot,c=i.points,u=i.dataKey,l=tM(this.props,!1),s=tM(o,!0),f=c.map(function(t,e){var r=hJ(hJ(hJ({key:"dot-".concat(e),r:3},l),s),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return n.renderDotItem(o,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tY,hK({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var i=this.props,o=i.type,c=i.layout,u=i.connectNulls,l=hJ(hJ(hJ({},tM((i.ref,hY(i,hG)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:o,layout:c,connectNulls:u});return a().createElement(hr,hK({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,o=n.strokeDasharray,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=n.animateNewValues,h=n.width,d=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return a().createElement(nO,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/i.length,l=i.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],i=Y(n.x,t.x),o=Y(n.y,t.y);return hJ(hJ({},t),{},{x:i(c),y:o(c)})}if(p){var a=Y(2*h,t.x),l=Y(d/2,t.y);return hJ(hJ({},t),{},{x:a(c),y:l(c)})}return hJ(hJ({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=Y(0,m)(c);if(o){var f="".concat(o).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(s,m,f)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(i,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,i=r.isAnimationActive,o=this.state,a=o.prevPoints,c=o.totalLength;return i&&n&&n.length&&(!a&&c>0||!ur()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,i=e.points,o=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,d=e.id;if(r||!i||!i.length)return null;var y=this.state.isAnimationFinished,v=1===i.length,m=M("recharts-line",o),b=c&&c.allowDataOverflow,g=u&&u.allowDataOverflow,x=b||g,w=tt()(d)?this.id:d,O=null!=(t=tM(n,!1))?t:{r:3,strokeWidth:2},j=O.r,S=O.strokeWidth,A=(tE(n)?n:{}).clipDot,P=void 0===A||A,E=2*(void 0===j?3:j)+(void 0===S?2:S);return a().createElement(tY,{className:m},b||g?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:b?s:s-f/2,y:g?l:l-p/2,width:b?f:2*f,height:g?p:2*p})),!P&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:s-E/2,y:l-E/2,width:f+E,height:p+E}))):null,!v&&this.renderCurve(x,w),this.renderErrorBar(x,w),(v||n)&&this.renderDots(x,P,w),(!h||y)&&sN.renderCallByParent(this.props,i))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(hQ(t),[0]):t,n=[],i=0;i<e;++i)n=[].concat(hQ(n),hQ(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(tr()(t))r=t(e);else{var n=e.key,i=hY(e,hH),o=M("recharts-line-dot","boolean"!=typeof t?t.className:"");r=a().createElement(rp,hK({key:n},i,{className:o}))}return r}}],e&&h1(n.prototype,e),r&&h1(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function h7(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function h9(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function dt(t){return(dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function de(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?de(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=dt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dt(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):de(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dn(t,e,r){var n,i,o,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(F(h)||ec.isSsr)return h7(l,("number"==typeof h&&F(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nV(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var i,o=tr()(d)?d(t.value,n):t.value;return"width"===b?(i=nV(o,{fontSize:e,letterSpacing:r}),fN({width:i.width+g.width,height:i.height+g.height},v)):nV(o,{fontSize:e,letterSpacing:r})[b]},w=l.length>=2?U(l[1].coordinate-l[0].coordinate):1,O=(n="width"===b,i=s.x,o=s.y,a=s.width,c=s.height,1===w?{start:n?i:o,end:n?i+a:o+c}:{start:n?i+a:o+c,end:n?i:o});return"equidistantPreserveStart"===h?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(o=function(){var e,o=null==n?void 0:n[l];if(void 0===o)return{v:h7(n,s)};var a=l,p=function(){return void 0===e&&(e=r(o,a)),e},h=o.coordinate,d=0===l||h9(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+i),l+=s)}())return o.v;return[]}(w,O,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(o){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=dr(dr({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),h9(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+i),a[c-1]=dr(dr({},s),{},{isShow:!0}))}for(var h=o?c-1:c,d=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var s=t*(o.coordinate-t*c()/2-u);a[e]=o=dr(dr({},o),{},{tickCoord:s<0?o.coordinate-s*t:o.coordinate})}else a[e]=o=dr(dr({},o),{},{tickCoord:o.coordinate});h9(t,o.tickCoord,c,u,l)&&(u=o.tickCoord+t*(c()/2+i),a[e]=dr(dr({},o),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(w,O,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,l=function(e){var n,l=o[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);o[e]=l=dr(dr({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else o[e]=l=dr(dr({},l),{},{tickCoord:l.coordinate});h9(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+i),o[e]=dr(dr({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return o}(w,O,x,l,f)).filter(function(t){return t.isShow})}h3(h8,"displayName","Line"),h3(h8,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!ec.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),h3(h8,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,i=t.xAxisTicks,o=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return hJ({points:u.map(function(t,e){var u=lo(t,a);return"horizontal"===s?{x:lk({axis:r,ticks:i,bandSize:c,entry:t,index:e}),y:tt()(u)?null:n.scale(u),value:u,payload:t}:{x:tt()(u)?null:r.scale(u),y:lk({axis:n,ticks:o,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var di=["viewBox"],da=["viewBox"],dc=["ticks"];function du(t){return(du="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dl(){return(dl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ds(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function df(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ds(Object(r),!0).forEach(function(e){dm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ds(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dp(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function dh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,db(n.key),n)}}function dd(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dd=function(){return!!t})()}function dy(t){return(dy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dv(t,e){return(dv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dm(t,e,r){return(e=db(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function db(t){var e=function(t,e){if("object"!=du(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=du(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==du(e)?e:e+""}var dg=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=dy(r),(e=function(t,e){if(e&&("object"===du(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dd()?Reflect.construct(r,i||[],dy(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&dv(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=dp(t,di),i=this.props,o=i.viewBox,a=dp(i,da);return!ta(r,o)||!ta(n,a)||!ta(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=F(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=l+!d*f)-v*m)-v*y,o=b;break;case"left":n=i=t.coordinate,o=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=i=t.coordinate,o=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(i=l+d*f)+v*m)+v*y,o=b}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.orientation,c=t.mirror,u=t.axisLine,l=df(df(df({},tM(this.props,!1)),tM(u,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var s=+("top"===o&&!c||"bottom"===o&&c);l=df(df({},l),{},{x1:e,y1:r+s*i,x2:e+n,y2:r+s*i})}else{var f=+("left"===o&&!c||"right"===o&&c);l=df(df({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+i})}return a().createElement("line",dl({},l,{className:M("recharts-cartesian-axis-line",L()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var i=this,o=this.props,c=o.tickLine,u=o.stroke,l=o.tick,s=o.tickFormatter,f=o.unit,p=dn(df(df({},this.props),{},{ticks:t}),e,r),h=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=tM(this.props,!1),v=tM(l,!1),m=df(df({},y),{},{fill:"none"},tM(c,!1)),b=p.map(function(t,e){var r=i.getTickLineCoord(t),o=r.line,b=r.tick,g=df(df(df(df({textAnchor:h,verticalAnchor:d},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tY,dl({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},th(i.props,t,e)),c&&a().createElement("line",dl({},m,o,{className:M("recharts-cartesian-axis-tick-line",L()(c,"className"))})),l&&n.renderTickItem(l,g,"".concat(tr()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,i=e.height,o=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=dp(u,dc),f=l;return(tr()(o)&&(f=o(l&&l.length>0?this.props:s)),n<=0||i<=0||!f||!f.length)?null:a().createElement(tY,{className:M("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),sh.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):tr()(t)?t(e):a().createElement(ih,dl({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&dh(n.prototype,e),r&&dh(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);function dx(t){return(dx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}dm(dg,"displayName","CartesianAxis"),dm(dg,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function dw(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dw=function(){return!!t})()}function dO(t){return(dO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dj(t,e){return(dj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dS(t,e,r){return(e=dA(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dA(t){var e=function(t,e){if("object"!=dx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dx(e)?e:e+""}function dP(){return(dP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dE(t){var e=t.xAxisId,r=f8(),n=f7(),i=f4(e);return null==i?null:a().createElement(dg,dP({},i,{className:M("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lm(t,!0)}}))}var dk=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dO(t),function(t,e){if(e&&("object"===dx(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dw()?Reflect.construct(t,e||[],dO(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dj(r,t),e=[{key:"render",value:function(){return a().createElement(dE,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dA(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dM(t){return(dM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}dS(dk,"displayName","XAxis"),dS(dk,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function d_(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d_=function(){return!!t})()}function dT(t){return(dT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dN(t,e){return(dN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dC(t,e,r){return(e=dD(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dD(t){var e=function(t,e){if("object"!=dM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dM(e)?e:e+""}function dI(){return(dI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var dB=function(t){var e=t.yAxisId,r=f8(),n=f7(),i=f6(e);return null==i?null:a().createElement(dg,dI({},i,{className:M("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lm(t,!0)}}))},dL=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dT(t),function(t,e){if(e&&("object"===dM(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d_()?Reflect.construct(t,e||[],dT(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dN(r,t),e=[{key:"render",value:function(){return a().createElement(dB,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dD(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);dC(dL,"displayName","YAxis"),dC(dL,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dR=hX({chartName:"LineChart",GraphicalChild:h8,axisComponents:[{axisType:"xAxis",AxisComp:dk},{axisType:"yAxis",AxisComp:dL}],formatAxisMap:fk}),dz=["x1","y1","x2","y2","key"],dU=["offset"];function d$(t){return(d$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dF(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=d$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d$(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dq(){return(dq=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dX(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var dG=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:i,ry:u,width:o,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dH(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(tr()(t))r=t(e);else{var n=e.x1,i=e.y1,o=e.x2,c=e.y2,u=e.key,l=tM(dX(e,dz),!1),s=(l.offset,dX(l,dU));r=a().createElement("line",dq({},s,{x1:n,y1:i,x2:o,y2:c,fill:"none",key:u}))}return r}function dV(t){var e=t.x,r=t.width,n=t.horizontal,i=void 0===n||n,o=t.horizontalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return dH(i,dW(dW({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dY(t){var e=t.y,r=t.height,n=t.vertical,i=void 0===n||n,o=t.verticalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return dH(i,dW(dW({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function dK(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:i+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dZ(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,i=t.x,o=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:i+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:o,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dJ=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return lv(dn(dW(dW(dW({},dg.defaultProps),r),{},{ticks:lm(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},dQ=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return lv(dn(dW(dW(dW({},dg.defaultProps),r),{},{ticks:lm(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},d0={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function d1(t){var e,r,n,i,c,u,l=f8(),s=f7(),f=(0,o.useContext)(fQ),p=dW(dW({},t),{},{stroke:null!=(e=t.stroke)?e:d0.stroke,fill:null!=(r=t.fill)?r:d0.fill,horizontal:null!=(n=t.horizontal)?n:d0.horizontal,horizontalFill:null!=(i=t.horizontalFill)?i:d0.horizontalFill,vertical:null!=(c=t.vertical)?c:d0.vertical,verticalFill:null!=(u=t.verticalFill)?u:d0.verticalFill,x:F(t.x)?t.x:f.left,y:F(t.y)?t.y:f.top,width:F(t.width)?t.width:f.width,height:F(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=H((0,o.useContext)(fK)),w=f3();if(!F(y)||y<=0||!F(v)||v<=0||!F(h)||h!==+h||!F(d)||d!==+d)return null;var O=p.verticalCoordinatesGenerator||dJ,j=p.horizontalCoordinatesGenerator||dQ,S=p.horizontalPoints,A=p.verticalPoints;if((!S||!S.length)&&tr()(j)){var P=b&&b.length,E=j({yAxis:w?dW(dW({},w),{},{ticks:P?b:w.ticks}):void 0,width:l,height:s,offset:f},!!P||m);J(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(d$(E),"]")),Array.isArray(E)&&(S=E)}if((!A||!A.length)&&tr()(O)){var k=g&&g.length,M=O({xAxis:x?dW(dW({},x),{},{ticks:k?g:x.ticks}):void 0,width:l,height:s,offset:f},!!k||m);J(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(d$(M),"]")),Array.isArray(M)&&(A=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dG,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dV,dq({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),a().createElement(dY,dq({},p,{offset:f,verticalPoints:A,xAxis:x,yAxis:w})),a().createElement(dK,dq({},p,{horizontalPoints:S})),a().createElement(dZ,dq({},p,{verticalPoints:A})))}d1.displayName="CartesianGrid";var d2=["layout","type","stroke","connectNulls","isRange","ref"],d5=["key"];function d4(t){return(d4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d3(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function d6(){return(d6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d8(Object(r),!0).forEach(function(e){yn(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d9(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yi(n.key),n)}}function yt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(yt=function(){return!!t})()}function ye(t){return(ye=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yr(t,e){return(yr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yn(t,e,r){return(e=yi(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yi(t){var e=function(t,e){if("object"!=d4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d4(e)?e:e+""}var yo=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=ye(e),yn(t=function(t,e){if(e&&("object"===d4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yt()?Reflect.construct(e,r||[],ye(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0}),yn(t,"id",X("recharts-area-")),yn(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),tr()(e)&&e()}),yn(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),tr()(e)&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&yr(n,t),e=[{key:"renderDots",value:function(t,e,r){var i=this.props.isAnimationActive,o=this.state.isAnimationFinished;if(i&&!o)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=tM(this.props,!1),p=tM(u,!0),h=l.map(function(t,e){var r=d7(d7(d7({key:"dot-".concat(e),r:3},f),p),{},{index:e,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return n.renderDotItem(u,r)}),d={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tY,d6({className:"recharts-area-dots"},d),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,o=n[0].x,c=n[n.length-1].x,u=t*Math.abs(o-c),l=c6()(n.map(function(t){return t.y||0}));return(F(r)&&"number"==typeof r?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(c6()(r.map(function(t){return t.y||0})),l)),F(l))?a().createElement("rect",{x:o<c?o:o-u,y:0,width:u,height:Math.floor(l+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,o=n[0].y,c=n[n.length-1].y,u=t*Math.abs(o-c),l=c6()(n.map(function(t){return t.x||0}));return(F(r)&&"number"==typeof r?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(c6()(r.map(function(t){return t.x||0})),l)),F(l))?a().createElement("rect",{x:0,y:o<c?o:o-u,width:l+(i?parseInt("".concat(i),10):1),height:Math.floor(u)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var i=this.props,o=i.layout,c=i.type,u=i.stroke,l=i.connectNulls,s=i.isRange,f=(i.ref,d3(i,d2));return a().createElement(tY,{clipPath:r?"url(#clipPath-".concat(n,")"):null},a().createElement(hr,d6({},tM(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:o,stroke:"none",className:"recharts-area-area"})),"none"!==u&&a().createElement(hr,d6({},tM(this.props,!1),{className:"recharts-area-curve",layout:o,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&a().createElement(hr,d6({},tM(this.props,!1),{className:"recharts-area-curve",layout:o,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,o=n.baseLine,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=this.state,h=p.prevPoints,d=p.prevBaseLine;return a().createElement(nO,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"area-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var c=n.t;if(h){var u,l=h.length/i.length,s=i.map(function(t,e){var r=Math.floor(e*l);if(h[r]){var n=h[r],i=Y(n.x,t.x),o=Y(n.y,t.y);return d7(d7({},t),{},{x:i(c),y:o(c)})}return t});return u=F(o)&&"number"==typeof o?Y(d,o)(c):tt()(o)||I()(o)?Y(d,0)(c):o.map(function(t,e){var r=Math.floor(e*l);if(d[r]){var n=d[r],i=Y(n.x,t.x),o=Y(n.y,t.y);return d7(d7({},t),{},{x:i(c),y:o(c)})}return t}),r.renderAreaStatically(s,u,t,e)}return a().createElement(tY,null,a().createElement("defs",null,a().createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(c))),a().createElement(tY,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(i,o,t,e)))})}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,i=r.baseLine,o=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return o&&n&&n.length&&(!c&&l>0||!ur()(c,n)||!ur()(u,i))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,i,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,i=e.points,o=e.className,c=e.top,u=e.left,l=e.xAxis,s=e.yAxis,f=e.width,p=e.height,h=e.isAnimationActive,d=e.id;if(r||!i||!i.length)return null;var y=this.state.isAnimationFinished,v=1===i.length,m=M("recharts-area",o),b=l&&l.allowDataOverflow,g=s&&s.allowDataOverflow,x=b||g,w=tt()(d)?this.id:d,O=null!=(t=tM(n,!1))?t:{r:3,strokeWidth:2},j=O.r,S=O.strokeWidth,A=(tE(n)?n:{}).clipDot,P=void 0===A||A,E=2*(void 0===j?3:j)+(void 0===S?2:S);return a().createElement(tY,{className:m},b||g?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:b?u:u-f/2,y:g?c:c-p/2,width:b?f:2*f,height:g?p:2*p})),!P&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:u-E/2,y:c-E/2,width:f+E,height:p+E}))):null,v?null:this.renderArea(x,w),(n||v)&&this.renderDots(x,P,w),(!h||y)&&sN.renderCallByParent(this.props,i))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],e&&d9(n.prototype,e),r&&d9(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);yn(yo,"displayName","Area"),yn(yo,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!ec.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),yn(yo,"getBaseValue",function(t,e,r,n){var i=t.layout,o=t.baseValue,a=e.props.baseValue,c=null!=a?a:o;if(F(c)&&"number"==typeof c)return c;var u="horizontal"===i?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]}),yn(yo,"getComposedData",function(t){var e,r=t.props,n=t.item,i=t.xAxis,o=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,h=t.offset,d=r.layout,y=s&&s.length,v=yo.getBaseValue(r,n,i,o),m="horizontal"===d,b=!1,g=p.map(function(t,e){y?r=s[f+e]:Array.isArray(r=lo(t,l))?b=!0:r=[v,r];var r,n=null==r[1]||y&&null==lo(t,l);return m?{x:lk({axis:i,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:o.scale(r[1]),value:r,payload:t}:{x:n?null:i.scale(r[1]),y:lk({axis:o,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}});return e=y||b?g.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?o.scale(e):null}:{x:null!=e?i.scale(e):null,y:t.y}}):m?o.scale(v):i.scale(v),d7({points:g,baseLine:e,layout:d,isRange:b},h)}),yn(yo,"renderDotItem",function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(tr()(t))r=t(e);else{var n=M("recharts-area-dot","boolean"!=typeof t?t.className:""),i=e.key,o=d3(e,d5);r=a().createElement(rp,d6({},o,{key:i,className:n}))}return r});var ya=hX({chartName:"AreaChart",GraphicalChild:yo,axisComponents:[{axisType:"xAxis",AxisComp:dk},{axisType:"yAxis",AxisComp:dL}],formatAxisMap:fk});let yc=[{name:"Jan",value:400,previous:350},{name:"Feb",value:300,previous:280},{name:"Mar",value:600,previous:450},{name:"Apr",value:800,previous:600},{name:"May",value:700,previous:650},{name:"Jun",value:900,previous:750},{name:"Jul",value:1100,previous:900}];function yu({title:t,data:e,type:r,icon:n,color:o="#3b82f6",showTrend:a=!0,className:u=""}){let l=e[e.length-1]?.value||0,s=e[e.length-2]?.value||0,f=s>0?(l-s)/s*100:0,p=f>=0,h=({active:t,payload:e,label:r})=>t&&e&&e.length?(0,i.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl p-3 shadow-2xl",children:[(0,i.jsx)("p",{className:"text-white/60 text-sm",children:r}),(0,i.jsx)("p",{className:"text-white font-semibold",children:`${e[0].value.toLocaleString()}`})]}):null;return"stat"===r?(0,i.jsx)(j.A,{variant:"glass",className:`p-6 ${u}`,children:(0,i.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[n&&(0,i.jsx)("div",{className:"p-2 rounded-xl bg-white/10",children:n}),(0,i.jsx)("h3",{className:"text-white/80 font-medium",children:t})]}),a&&(0,i.jsxs)("div",{className:`flex items-center gap-1 ${p?"text-green-400":"text-red-400"}`,children:[p?(0,i.jsx)(m.A,{className:"w-4 h-4"}):(0,i.jsx)(P,{className:"w-4 h-4"}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[Math.abs(f).toFixed(1),"%"]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-white",children:l.toLocaleString()}),(0,i.jsxs)("div",{className:"text-white/60 text-sm",children:["vs ",s.toLocaleString()," last period"]})]})]})}):(0,i.jsx)(j.A,{variant:"glass",className:`p-6 ${u}`,children:(0,i.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,i.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[n&&(0,i.jsx)("div",{className:"p-2 rounded-xl bg-white/10",children:n}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-white font-semibold",children:t}),(0,i.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,i.jsx)("span",{className:"text-2xl font-bold text-white",children:l.toLocaleString()}),a&&(0,i.jsxs)("div",{className:`flex items-center gap-1 ${p?"text-green-400":"text-red-400"}`,children:[p?(0,i.jsx)(m.A,{className:"w-4 h-4"}):(0,i.jsx)(P,{className:"w-4 h-4"}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[Math.abs(f).toFixed(1),"%"]})]})]})]})]})}),(0,i.jsx)("div",{className:"mt-4",children:(()=>{switch(r){case"line":return(0,i.jsx)(tR,{width:"100%",height:120,children:(0,i.jsxs)(dR,{data:yc,children:[(0,i.jsx)(d1,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),(0,i.jsx)(dk,{dataKey:"name",axisLine:!1,tickLine:!1,tick:{fill:"rgba(255,255,255,0.6)",fontSize:12}}),(0,i.jsx)(dL,{hide:!0}),(0,i.jsx)(ex,{content:(0,i.jsx)(h,{})}),(0,i.jsx)(h8,{type:"monotone",dataKey:"value",stroke:o,strokeWidth:3,dot:{fill:o,strokeWidth:2,r:4},activeDot:{r:6,stroke:o,strokeWidth:2}})]})});case"area":return(0,i.jsx)(tR,{width:"100%",height:120,children:(0,i.jsxs)(ya,{data:yc,children:[(0,i.jsx)(d1,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),(0,i.jsx)(dk,{dataKey:"name",axisLine:!1,tickLine:!1,tick:{fill:"rgba(255,255,255,0.6)",fontSize:12}}),(0,i.jsx)(dL,{hide:!0}),(0,i.jsx)(ex,{content:(0,i.jsx)(h,{})}),(0,i.jsx)("defs",{children:(0,i.jsxs)("linearGradient",{id:`gradient-${t}`,x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"5%",stopColor:o,stopOpacity:.3}),(0,i.jsx)("stop",{offset:"95%",stopColor:o,stopOpacity:0})]})}),(0,i.jsx)(yo,{type:"monotone",dataKey:"value",stroke:o,strokeWidth:2,fillOpacity:1,fill:`url(#gradient-${t})`})]})});default:return null}})()})]})})}let yl=()=>(0,i.jsx)(yu,{title:"Content Views",data:yc,type:"area",icon:(0,i.jsx)(E.A,{className:"w-5 h-5 text-blue-400"}),color:"#3b82f6"}),ys=()=>(0,i.jsx)(yu,{title:"Engagement Rate",data:yc,type:"line",icon:(0,i.jsx)(k.A,{className:"w-5 h-5 text-purple-400"}),color:"#8b5cf6"}),yf=()=>(0,i.jsx)(yu,{title:"Content Created",data:[{name:"Total",value:47},{name:"Previous",value:32}],type:"stat",icon:(0,i.jsx)(w.A,{className:"w-5 h-5 text-green-400"})}),yp=()=>(0,i.jsx)(yu,{title:"Avg. Read Time",data:[{name:"Current",value:3.2},{name:"Previous",value:2.8}],type:"stat",icon:(0,i.jsx)(v.A,{className:"w-5 h-5 text-orange-400"})}),yh=()=>(0,i.jsx)(yu,{title:"Conversion Rate",data:[{name:"Current",value:12.5},{name:"Previous",value:9.8}],type:"stat",icon:(0,i.jsx)(x.A,{className:"w-5 h-5 text-pink-400"})}),yd=()=>(0,i.jsx)(yu,{title:"AI Generations",data:yc,type:"area",icon:(0,i.jsx)(s.A,{className:"w-5 h-5 text-yellow-400"}),color:"#f59e0b"}),yy=[{id:"aayush",title:"Aayush Agent",description:"Advanced hierarchical AI system for comprehensive content research and generation",icon:s.A,href:"/hierarchical-agents",gradient:"from-orange-500 to-red-500",usage:"15 articles this month",popular:!0,featured:!0},{id:"blog",title:"Blog Generator",description:"Create SEO-optimized blog posts with AI research",icon:f.A,href:"/blog",gradient:"from-blue-500 to-purple-600",usage:"24 posts this month",popular:!0},{id:"email",title:"Email Generator",description:"Craft high-converting email campaigns",icon:p.A,href:"/email",gradient:"from-pink-500 to-rose-500",usage:"12 emails this month",popular:!1},{id:"tweet",title:"Tweet Generator",description:"Generate viral-worthy social media content",icon:h.A,href:"/tweet",gradient:"from-cyan-500 to-blue-500",usage:"48 tweets this month",popular:!1},{id:"youtube",title:"YouTube Scripts",description:"Create engaging video scripts with hooks",icon:d.A,href:"/youtube",gradient:"from-orange-500 to-red-500",usage:"8 scripts this month",popular:!1}];y.A,f.A,v.A,m.A;let yv=[{id:1,title:"The Future of AI in Healthcare",type:"Aayush Article",createdAt:"2 hours ago",status:"Published",engagement:"1.2K views",agent:"Aayush"},{id:2,title:"Sustainable Technology Trends 2024",type:"Aayush Article",createdAt:"4 hours ago",status:"Published",engagement:"856 views",agent:"Aayush"},{id:3,title:"Welcome to Our Newsletter",type:"Email",createdAt:"5 hours ago",status:"Sent",engagement:"89% open rate"},{id:4,title:"Top 10 AI Tools for 2024",type:"Tweet Thread",createdAt:"1 day ago",status:"Published",engagement:"245 likes"}];function ym(){let[t,e]=(0,o.useState)(!1);return t?(0,i.jsx)(A.A,{children:(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome back, John! \uD83D\uDC4B"}),(0,i.jsx)("p",{className:"text-white/70",children:"Ready to create amazing content? Let's see what you can build today."})]}),(0,i.jsx)(S.A,{variant:"primary",size:"lg",icon:(0,i.jsx)(b.A,{className:"w-5 h-5"}),children:"Create Content"})]})}),(0,i.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,i.jsx)(yf,{}),(0,i.jsx)(yp,{}),(0,i.jsx)(yh,{})]}),(0,i.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.15},className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsx)(yl,{}),(0,i.jsx)(ys,{})]}),(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.15},children:(0,i.jsxs)(j.A,{variant:"glass",className:"p-8 bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/30",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)("div",{className:"p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg",children:(0,i.jsx)(s.A,{className:"w-8 h-8 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Aayush Agent"}),(0,i.jsx)("p",{className:"text-orange-200",children:"Advanced Hierarchical AI Content System"})]})]}),(0,i.jsx)(l(),{href:"/hierarchical-agents",children:(0,i.jsx)(S.A,{variant:"primary",className:"bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700",icon:(0,i.jsx)(g.A,{className:"w-4 h-4"}),children:"Launch Aayush"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,i.jsxs)("div",{className:"text-center p-4 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-400 mb-1",children:"15"}),(0,i.jsx)("div",{className:"text-sm text-white/70",children:"Articles Generated"})]}),(0,i.jsxs)("div",{className:"text-center p-4 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-400 mb-1",children:"127"}),(0,i.jsx)("div",{className:"text-sm text-white/70",children:"Sources Analyzed"})]}),(0,i.jsxs)("div",{className:"text-center p-4 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-400 mb-1",children:"4.2m"}),(0,i.jsx)("div",{className:"text-sm text-white/70",children:"Words Processed"})]}),(0,i.jsxs)("div",{className:"text-center p-4 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-400 mb-1",children:"98%"}),(0,i.jsx)("div",{className:"text-sm text-white/70",children:"Success Rate"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(x.A,{className:"w-4 h-4 text-red-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-white",children:"Topic Analyst"}),(0,i.jsx)("div",{className:"text-xs text-white/60",children:"Keyword extraction"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(w.A,{className:"w-4 h-4 text-orange-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-white",children:"Primary Research"}),(0,i.jsx)("div",{className:"text-xs text-white/60",children:"Multi-query search"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-amber-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(m.A,{className:"w-4 h-4 text-amber-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-white",children:"Gap Analyst"}),(0,i.jsx)("div",{className:"text-xs text-white/60",children:"Content gaps"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(s.A,{className:"w-4 h-4 text-yellow-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-white",children:"Deep Research"}),(0,i.jsx)("div",{className:"text-xs text-white/60",children:"Targeted filling"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(f.A,{className:"w-4 h-4 text-red-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-white",children:"Content Writing"}),(0,i.jsx)("div",{className:"text-xs text-white/60",children:"RAG generation"})]})]})]})]})}),(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:(0,i.jsx)(yd,{})}),(0,i.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Content Tools"}),(0,i.jsx)(l(),{href:"/tools",className:"text-blue-400 hover:text-blue-300 text-sm font-medium",children:"View all tools →"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:yy.map((t,e)=>(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3+.1*e},className:t.featured?"md:col-span-2":"",children:(0,i.jsxs)(j.A,{variant:"elevated",className:`p-6 h-full group cursor-pointer relative overflow-hidden ${t.featured?"border-2 border-orange-500/50 bg-gradient-to-br from-orange-500/10 to-red-500/10":""}`,children:[t.featured&&(0,i.jsx)("div",{className:"absolute top-0 right-0 bg-gradient-to-l from-orange-500 to-red-500 text-white px-3 py-1 text-xs font-bold rounded-bl-lg",children:"FEATURED"}),(0,i.jsx)(l(),{href:t.href,className:"block h-full",children:(0,i.jsxs)("div",{className:"flex flex-col h-full",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("div",{className:`p-3 rounded-xl bg-gradient-to-r ${t.gradient} ${t.featured?"shadow-lg shadow-orange-500/25":""}`,children:(0,i.jsx)(t.icon,{className:"w-6 h-6 text-white"})}),t.popular&&!t.featured&&(0,i.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium",children:[(0,i.jsx)(O.A,{className:"w-3 h-3"}),"Popular"]})]}),(0,i.jsx)("h3",{className:`font-semibold mb-2 ${t.featured?"text-xl text-white":"text-lg text-white"}`,children:t.title}),(0,i.jsx)("p",{className:`text-white/70 mb-4 flex-1 ${t.featured?"text-base leading-relaxed":"text-sm"}`,children:t.description}),t.featured&&(0,i.jsxs)("div",{className:"mb-4 p-3 bg-white/5 rounded-lg border border-orange-500/20",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 text-orange-400 text-sm font-medium mb-1",children:[(0,i.jsx)(s.A,{className:"w-4 h-4"}),"Advanced Features"]}),(0,i.jsx)("div",{className:"text-white/70 text-xs",children:"5-agent workflow • Real-time research • WYSIWYG editor • SEO analysis"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-white/50 text-xs",children:t.usage}),(0,i.jsx)(g.A,{className:"w-4 h-4 text-white/50 group-hover:text-white group-hover:translate-x-1 transition-all"})]})]})})]})},t.id))})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},className:"lg:col-span-2",children:(0,i.jsxs)(j.A,{variant:"glass",className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Recent Content"}),(0,i.jsx)(l(),{href:"/library",className:"text-blue-400 hover:text-blue-300 text-sm font-medium",children:"View all →"})]}),(0,i.jsx)("div",{className:"space-y-4",children:yv.map(t=>(0,i.jsxs)("div",{className:`flex items-center justify-between p-4 rounded-xl transition-colors cursor-pointer ${"Aayush"===t.agent?"bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 hover:from-orange-500/15 hover:to-red-500/15":"bg-white/5 hover:bg-white/10"}`,children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1",children:["Aayush"===t.agent&&(0,i.jsx)("div",{className:"p-1.5 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg",children:(0,i.jsx)(s.A,{className:"w-3 h-3 text-white"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h4",{className:"text-white font-medium",children:t.title}),(0,i.jsxs)("div",{className:"flex items-center gap-4 mt-1",children:[(0,i.jsx)("span",{className:`text-sm font-medium ${"Aayush"===t.agent?"text-orange-400":"text-white/60"}`,children:t.type}),(0,i.jsx)("span",{className:"text-white/50 text-sm",children:t.createdAt})]})]})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("div",{className:"text-green-400 text-sm font-medium",children:t.status}),(0,i.jsx)("div",{className:"text-white/60 text-sm",children:t.engagement})]})]},t.id))})]})}),(0,i.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.5},children:(0,i.jsxs)(j.A,{variant:"glass",className:"p-6",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-white mb-6",children:"Quick Actions"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(l(),{href:"/hierarchical-agents",children:(0,i.jsx)(S.A,{variant:"primary",className:"w-full justify-start bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700",icon:(0,i.jsx)(s.A,{className:"w-4 h-4"}),children:"Launch Aayush Agent"})}),(0,i.jsx)(S.A,{variant:"secondary",className:"w-full justify-start",icon:(0,i.jsx)(f.A,{className:"w-4 h-4"}),children:"Write Blog Post"}),(0,i.jsx)(S.A,{variant:"secondary",className:"w-full justify-start",icon:(0,i.jsx)(p.A,{className:"w-4 h-4"}),children:"Create Email"}),(0,i.jsx)(S.A,{variant:"secondary",className:"w-full justify-start",icon:(0,i.jsx)(h.A,{className:"w-4 h-4"}),children:"Generate Tweet"}),(0,i.jsx)(S.A,{variant:"secondary",className:"w-full justify-start",icon:(0,i.jsx)(w.A,{className:"w-4 h-4"}),children:"View Analytics"})]}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-xl border border-blue-500/30",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(x.A,{className:"w-4 h-4 text-blue-400"}),(0,i.jsx)("span",{className:"text-blue-400 font-medium text-sm",children:"Monthly Goal"})]}),(0,i.jsx)("div",{className:"text-white text-sm mb-2",children:"Content pieces: 92/100"}),(0,i.jsx)("div",{className:"w-full bg-white/10 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full",style:{width:"92%"}})})]})]})})]})]})}):null}},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),i=r(55048);t.exports=function(t){if(!i(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),i=r(34117),o=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},7383:(t,e,r)=>{var n=r(67009),i=r(32269),o=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(i(r)&&o(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),i=r(52931),o=r(32269);t.exports=function(t){return o(t)?n(t):i(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),i=r(89624),o=r(47282),a=o&&o.isTypedArray;t.exports=a?i(a):n},10653:(t,e,r)=>{var n=r(21456),i=r(63979),o=r(7651);t.exports=function(t){return n(t,o,i)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(69984);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),i=r(55048),o=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return a;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},13419:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},13861:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return i(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),i=r(27467);t.exports=function t(e,r,o,a,c){return e===r||(null!=e&&null!=r&&(i(e)||i(r))?n(e,r,o,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),i=r(46063),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},15909:(t,e,r)=>{var n=r(87506),i=r(66930),o=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(o||i),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),i=r(1707),o=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return i(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(o)),c(a(t,function(t,r,i){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),i=r(22),o=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:o(r);return u<0&&(u=a(c+u,0)),n(t,i(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this}},20540:(t,e,r)=>{var n=r(55048),i=r(70151),o=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,o=i();if(g(o))return w(o);p=setTimeout(x,(t=o-h,r=o-d,n=e-t,v?c(n,s-r):n))}function w(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function O(){var t,r=i(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(o(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(i())},O}},20623:(t,e,r)=>{var n=r(15871),i=r(40491),o=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=i(r,t);return void 0===a&&a===e?o(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}},21456:(t,e,r)=>{var n=r(41693),i=r(40542);t.exports=function(t,e,r){var o=e(t);return i(t)?o:n(o,r(t))}},21592:(t,e,r)=>{var n=r(42205),i=r(61837);t.exports=function(t,e){return n(i(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,o,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:i.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(o)var g=u?o(b,m,p,e,t,c):o(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,o,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),i=r(32269),o=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},25541:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27006:(t,e,r)=>{var n=r(46328),i=r(99525),o=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!i(e,function(t,e){if(!o(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},28837:(t,e,r)=>{var n=r(57797),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():i.call(e,r,1),--this.size,!0)}},28947:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},28977:(t,e,r)=>{var n=r(11539),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-i?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),i=r(70222),o=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?i(t):o(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),i=r(658),o=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!i||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),i=r(69619);t.exports=function(t){return null!=t&&i(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),i=r(67619),o=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:i(t,e)?[t]:o(a(t))}},35163:(t,e,r)=>{var n=r(15451),i=r(27467),o=Object.prototype,a=o.hasOwnProperty,c=o.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return i(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),i=r(4999),o=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new i(t),new i(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},36315:(t,e,r)=>{var n=r(22),i=r(92662);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),i=r(27006),o=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e);x=x==f?h:x,w=w==f?h:w;var O=x==h,j=w==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(t)?i(t,e,r,y,v,m):o(t,e,x,r,y,v,m);if(!(1&r)){var A=O&&d.call(t,"__wrapped__"),P=j&&d.call(e,"__wrapped__");if(A||P){var E=A?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),i=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(i,""):t}},38404:(t,e,r)=>{var n=r(29395),i=r(65932),o=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var i=null==t?void 0:n(t,e);return void 0===i?r:i}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41312:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41353:t=>{t.exports=function(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(r=r>i?i:r)<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(i);++n<i;)o[n]=t[n+e];return o}},41547:(t,e,r)=>{var n=r(61548),i=r(90851);t.exports=function(t,e){var r=i(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),i=r(85450);t.exports=function t(e,r,o,a,c){var u=-1,l=e.length;for(o||(o=i),c||(c=[]);++u<l;){var s=e[u];r>0&&o(s)?r>1?t(s,r-1,o,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},45058:(t,e,r)=>{var n=r(42082),i=r(8852),o=r(67619),a=r(46436);t.exports=function(t){return o(t)?n(a(t)):i(t)}},45603:(t,e,r)=>{var n=r(20540),i=r(55048);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),i=r(66354),o=r(11424);t.exports=function(t,e){return o(i(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),i=r(89185),o=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=i,a.prototype.has=o,t.exports=a},46436:(t,e,r)=>{var n=r(49227),i=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-i?"-0":e}},47212:(t,e,r)=>{var n=r(87270),i=r(30316),o=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.process,c=function(){try{var t=o&&o.require&&o.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[i,o,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},48730:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49227:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"symbol"==typeof t||i(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(i,function(t,r,n,i){e.push(n?i.replace(o,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var a=t[r];e(a,r,t)&&(o[i++]=a)}return o}},52823:(t,e,r)=>{var n=r(85406),i=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!i&&i in t}},52931:(t,e,r)=>{var n=r(77834),i=r(89605),o=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),i=r(32269);t.exports=function(t,e){var r=-1,o=i(t)?Array(t.length):[];return n(t,function(t,n,i){o[++r]=e(t,n,i)}),o}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,i){if(null==r)return r;if(!n(r))return t(r,i);for(var o=r.length,a=e?o:-1,c=Object(r);(e?a--:++a<o)&&!1!==i(c[a],a,c););return r}}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,i=n(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),i=r(35163),o=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(o(t)||i(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),i=r(52823),o=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!o(t)||i(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),i=r(22),o=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),i=r(40542),o=r(27467);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),i=r(83949),o=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return o.call(t,e)})}:i},64398:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var i=-1,o=t.criteria,a=e.criteria,c=o.length,u=r.length;++i<c;){var l=n(o[i],a[i]);if(l){if(i>=u)return l;return l*("desc"==r[i]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++i];if(!1===r(o[u],u,o))break}return e}}},66354:(t,e,r)=>{var n=r(85244),i=Math.max;t.exports=function(t,e,r){return e=i(void 0===e?t.length-1:e,0),function(){for(var o=arguments,a=-1,c=i(o.length-e,0),u=Array(c);++a<c;)u[a]=o[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=o[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var i=e(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),i=r(34117),o=r(48385);t.exports=function(t){return i(t)?o(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),i=r(28837),o=r(94388),a=r(13419),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case i:return e}}}(t)===o}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),i=r(37575),o=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=i,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),i=r(22),o=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),i=r(49227),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||i(t))||a.test(t)||!o.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),i=r(99114),o=r(22);t.exports=function(t,e){var r={};return e=o(e,3),i(t,function(t,i,o){n(r,i,e(t,i,o))}),r}},69984:(t,e,r)=>{var n=r(14675),i=r(91928),o=r(48169);t.exports=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:o},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=o.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var i=a.call(t);return n&&(e?t[c]=r:delete t[c]),i}},70334:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71960:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),i=r(93311),o=r(41132);t.exports=function(t){var e=i(t);return 1==e.length&&e[0][2]?o(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),i=r(15871);t.exports=function(t,e,r,o){var a=r.length,c=a,u=!o;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(o)var d=o(f,p,s,t,e,h);if(!(void 0===d?i(p,f,3,o,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},80195:(t,e,r)=>{var n=r(79474),i=r(21367),o=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return i(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),i=r(1944),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,c=a&&a.exports===o?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||i},80336:(t,e,r)=>{Promise.resolve().then(r.bind(r,80559))},80458:(t,e,r)=>{var n=r(29395),i=r(69619),o=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return o(t)&&i(t.length)&&!!a[n(t)]}},80559:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx","default")},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,i=null===t,o=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||i&&c&&l||!r&&l||!o)return 1;if(!i&&!a&&!s&&t<e||s&&r&&o&&!i&&!a||u&&r&&o||!c&&o||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),i=r(35163),o=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=o(t),s=!r&&i(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},82710:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),i=r(48088),o=r(88170),a=r.n(o),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,s=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},83949:t=>{t.exports=function(){return[]}},84031:(t,e,r)=>{"use strict";var n=r(34452);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),i=r(35163),o=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||i(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),i="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||i||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),i=r(17518),o=r(46229),a=r(7383);t.exports=o(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),i(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function i(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=t.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n),r}i.Cache=n,t.exports=i},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),i=r(7383),o=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),i=r(84261),o=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90064:(t,e,r)=>{Promise.resolve().then(r.bind(r,4315))},90200:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),i=r(99180),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92662:(t,e,r)=>{var n=r(46328),i=r(80704),o=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=i,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=o;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),i=r(7651);t.exports=function(t){for(var e=i(t),r=e.length;r--;){var o=e[r],a=t[o];e[r]=[o,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},95308:(t,e,r)=>{var n=r(34772),i=r(36959),o=r(2408);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},95746:(t,e,r)=>{var n=r(15909),i=r(29205),o=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),i=r(39774),o=r(74610);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},98451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),u=Array(c);c--;)u[o?c:++a]=t,t+=i;return u}},99114:(t,e,r)=>{var n=r(12344),i=r(7651);t.exports=function(t,e){return t&&n(t,e,i)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,135,951,15,141,556],()=>r(82710));module.exports=n})();