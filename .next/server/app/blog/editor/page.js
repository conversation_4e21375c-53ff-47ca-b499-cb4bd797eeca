(()=>{var e={};e.id=681,e.ids=[681],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},8954:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a3});var r,i,o,s=n(60687),l=n(43210),a=n(16189),c=n(97905),d=n(28559),h=n(74606),p=n(70615),u=n(31158),f=n(8819),m=n(10022),g=n(16337),y=n(51215);function b(e){this.content=e}b.prototype={constructor:b,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var r=n&&n!=e?this.remove(n):this,i=r.find(e),o=r.content.slice();return -1==i?o.push(n||e,t):(o[i+1]=t,n&&(o[i]=n)),new b(o)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new b(n)},addToStart:function(e,t){return new b([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new b(n)},addBefore:function(e,t,n){var r=this.remove(t),i=r.content.slice(),o=r.find(e);return i.splice(-1==o?i.length:o,0,t,n),new b(i)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=b.from(e)).size?new b(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=b.from(e)).size?new b(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=b.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},b.from=function(e){if(e instanceof b)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new b(t)};class v{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,r=0,i){for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;if(a>e&&!1!==n(l,r+s,i||null,o)&&l.content.size){let i=s+1;l.nodesBetween(Math.max(0,e-i),Math.min(l.content.size,t-i),n,r+i)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let i="",o=!0;return this.nodesBetween(e,t,(s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(o?o=!1:i+=n),i+=a},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),i=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),i=1);i<e.content.length;i++)r.push(e.content[i]);return new v(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let i=0,o=0;o<t;i++){let s=this.content[i],l=o+s.nodeSize;l>e&&((o<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-o),Math.min(s.text.length,t-o)):s.cut(Math.max(0,e-o-1),Math.min(s.content.size,t-o-1))),n.push(s),r+=s.nodeSize),o=l}return new v(n,r)}cutByIndex(e,t){return e==t?v.empty:0==e&&t==this.content.length?this:new v(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),i=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new v(r,i)}addToStart(e){return new v([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new v(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return function e(t,n,r){for(let i=0;;i++){if(i==t.childCount||i==n.childCount)return t.childCount==n.childCount?null:r;let o=t.child(i),s=n.child(i);if(o==s){r+=o.nodeSize;continue}if(!o.sameMarkup(s))return r;if(o.isText&&o.text!=s.text){for(let e=0;o.text[e]==s.text[e];e++)r++;return r}if(o.content.size||s.content.size){let t=e(o.content,s.content,r+1);if(null!=t)return t}r+=o.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,r,i){for(let o=t.childCount,s=n.childCount;;){if(0==o||0==s)return o==s?null:{a:r,b:i};let l=t.child(--o),a=n.child(--s),c=l.nodeSize;if(l==a){r-=c,i-=c;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let e=0,t=Math.min(l.text.length,a.text.length);for(;e<t&&l.text[l.text.length-e-1]==a.text[a.text.length-e-1];)e++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let t=e(l.content,a.content,r-1,i-1);if(t)return t}r-=c,i-=c}}(this,e,t,n)}findIndex(e,t=-1){if(0==e)return k(0,e);if(e==this.size)return k(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=e){if(i==e||t>0)return k(n+1,i);return k(n,r)}r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return v.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new v(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return v.empty;let t,n=0;for(let r=0;r<e.length;r++){let i=e[r];n+=i.nodeSize,r&&i.isText&&e[r-1].sameMarkup(i)?(t||(t=e.slice(0,r)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new v(t||e,n)}static from(e){if(!e)return v.empty;if(e instanceof v)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new v([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}v.empty=new v([],0);let w={index:0,offset:0};function k(e,t){return w.index=e,w.offset=t,w}function x(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!x(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!x(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class S{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&x(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return S.none;if(e instanceof S)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}S.none=[];class M extends Error{}class C{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,r,i){let{index:o,offset:s}=t.findIndex(n),l=t.maybeChild(o);if(s==n||l.isText)return i&&!i.canReplace(o,o,r)?null:t.cut(0,n).append(r).append(t.cut(n));let a=e(l.content,n-s-1,r);return a&&t.replaceChild(o,l.copy(a))}(this.content,e+this.openStart,t);return n&&new C(n,this.openStart,this.openEnd)}removeBetween(e,t){return new C(function e(t,n,r){let{index:i,offset:o}=t.findIndex(n),s=t.maybeChild(i),{index:l,offset:a}=t.findIndex(r);if(o==n||s.isText){if(a!=r&&!t.child(l).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return t.replaceChild(i,s.copy(e(s.content,n-o-1,r-o-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return C.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new C(v.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)r++;return new C(e,n,r)}}function O(e,t){if(!t.type.compatibleContent(e.type))throw new M("Cannot join "+t.type.name+" onto "+e.type.name)}function N(e,t,n){let r=e.node(n);return O(r,t.node(n)),r}function A(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function T(e,t,n,r){let i=(t||e).node(n),o=0,s=t?t.index(n):i.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(A(e.nodeAfter,r),o++));for(let e=o;e<s;e++)A(i.child(e),r);t&&t.depth==n&&t.textOffset&&A(t.nodeBefore,r)}function E(e,t){return e.type.checkContent(t),e.copy(t)}function D(e,t,n){let r=[];return T(null,e,n,r),e.depth>n&&A(E(N(e,t,n+1),D(e,t,n+1)),r),T(t,null,n,r),new v(r)}C.empty=new C(v.empty,0,0);class R{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)r+=n.child(t).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return S.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let i=n.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||r&&i[o].isInSet(r.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new L(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],r=0,i=t;for(let t=e;;){let{index:e,offset:o}=t.content.findIndex(i),s=i-o;if(n.push(t,e,r+o),!s||(t=t.child(e)).isText)break;i=s-1,r+=o+1}return new R(t,n,i)}static resolveCached(e,t){let n=j.get(e);if(n)for(let e=0;e<n.elts.length;e++){let r=n.elts[e];if(r.pos==t)return r}else j.set(e,n=new I);let r=n.elts[n.i]=R.resolve(e,t);return n.i=(n.i+1)%P,r}}class I{constructor(){this.elts=[],this.i=0}}let P=12,j=new WeakMap;class L{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let z=Object.create(null);class B{constructor(e,t,n,r=S.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||v.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&x(this.attrs,t||e.defaultAttrs||z)&&S.sameSet(this.marks,n||S.none)}copy(e=null){return e==this.content?this:new B(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new B(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return C.empty;let r=this.resolve(e),i=this.resolve(t),o=n?0:r.sharedDepth(t),s=r.start(o);return new C(r.node(o).content.cut(r.pos-s,i.pos-s),r.depth-o,i.depth-o)}replace(e,t,n){var r=this.resolve(e),i=this.resolve(t);if(n.openStart>r.depth)throw new M("Inserted content deeper than insertion position");if(r.depth-n.openStart!=i.depth-n.openEnd)throw new M("Inconsistent open depths");return function e(t,n,r,i){let o=t.index(i),s=t.node(i);if(o==n.index(i)&&i<t.depth-r.openStart){let l=e(t,n,r,i+1);return s.copy(s.content.replaceChild(o,l))}if(!r.content.size)return E(s,D(t,n,i));if(r.openStart||r.openEnd||t.depth!=i||n.depth!=i){let{start:e,end:o}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)r=t.node(e).copy(v.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(r,t);return E(s,function e(t,n,r,i,o){let s=t.depth>o&&N(t,n,o+1),l=i.depth>o&&N(r,i,o+1),a=[];return T(null,t,o,a),s&&l&&n.index(o)==r.index(o)?(O(s,l),A(E(s,e(t,n,r,i,o+1)),a)):(s&&A(E(s,D(t,n,o+1)),a),T(n,r,o,a),l&&A(E(l,D(r,i,o+1)),a)),T(i,null,o,a),new v(a)}(t,e,o,n,i))}{let e=t.parent,i=e.content;return E(e,i.cut(0,t.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(r,i,n,0)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return R.resolveCached(this,e)}resolveNoCache(e){return R.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),V(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=v.empty,r=0,i=n.childCount){let o=this.contentMatchAt(e).matchFragment(n,r,i),s=o&&o.matchFragment(this.content,t);if(!s||!s.validEnd)return!1;for(let e=r;e<i;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(e).matchType(n),o=i&&i.matchFragment(this.content,t);return!!o&&o.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=S.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!S.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=v.fromJSON(e,t.content),i=e.nodeType(t.type).create(t.attrs,r,n);return i.type.checkAttrs(i.attrs),i}}B.prototype.text=void 0;class $ extends B{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):V(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new $(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new $(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function V(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class F{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let r,i=new H(e,t);if(null==i.next)return F.empty;let o=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let i=[];for(let e in n){let r=n[e];r.isInGroup(t)&&i.push(r)}return 0==i.length&&e.err("No node type or group '"+t+"' found"),i})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=_(e),r=n;return e.eat(",")&&(r="}"!=e.next?_(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let s=(n=function(e){let t=[[]];return i(function e(t,o){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,o)),[]);if("seq"==t.type)for(let r=0;;r++){let s=e(t.exprs[r],o);if(r==t.exprs.length-1)return s;i(s,o=n())}else if("star"==t.type){let s=n();return r(o,s),i(e(t.expr,s),s),[r(s)]}else if("plus"==t.type){let s=n();return i(e(t.expr,o),s),i(e(t.expr,s),s),[r(s)]}else if("opt"==t.type)return[r(o)].concat(e(t.expr,o));else if("range"==t.type){let s=o;for(let r=0;r<t.min;r++){let r=n();i(e(t.expr,s),r),s=r}if(-1==t.max)i(e(t.expr,s),s);else for(let o=t.min;o<t.max;o++){let o=n();r(s,o),i(e(t.expr,s),o),s=o}return[r(s)]}else if("name"==t.type)return[r(o,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let i={term:r,to:n};return t[e].push(i),i}function i(e,t){e.forEach(e=>e.to=t)}}(o),r=Object.create(null),function e(t){let i=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let r;if(e){for(let t=0;t<i.length;t++)i[t][0]==e&&(r=i[t][1]);W(n,t).forEach(t=>{r||i.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)})}})});let o=r[t.join(",")]=new F(t.indexOf(n.length-1)>-1);for(let t=0;t<i.length;t++){let n=i[t][1].sort(q);o.next.push({type:i[t][0],next:r[n.join(",")]||e(n)})}return o}(W(n,0)));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],i=!e.validEnd,o=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];o.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(s)&&r.push(s)}i&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function i(o,s){let l=o.matchFragment(e,n);if(l&&(!t||l.validEnd))return v.from(s.map(e=>e.createAndFill()));for(let e=0;e<o.next.length;e++){let{type:t,next:n}=o.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let e=i(n,s.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<i.next.length;e++){let{type:o,next:s}=i.next[e];o.isLeaf||o.hasRequiredAttrs()||o.name in t||r.type&&!s.validEnd||(n.push({match:o.contentMatch,type:o,via:r}),t[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return!function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)r+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return r}).join("\n")}}F.empty=new F(!0);class H{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function _(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function q(e,t){return t-e}function W(e,t){let n=[];return function t(r){let i=e[r];if(1==i.length&&!i[0].term)return t(i[0].to);n.push(r);for(let e=0;e<i.length;e++){let{term:r,to:o}=i[e];r||-1!=n.indexOf(o)||t(o)}}(t),n.sort(q)}function K(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function J(e,t){let n=Object.create(null);for(let r in e){let i=t&&t[r];if(void 0===i){let t=e[r];if(t.hasDefault)i=t.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function U(e,t,n,r){for(let r in t)if(!(r in e))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in e){let r=e[n];r.validate&&r.validate(t[n])}}function G(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new Q(e,r,t[r]);return n}class Y{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=G(e,n.attrs),this.defaultAttrs=K(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==F.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:J(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new B(this,this.computeAttrs(e),v.from(t),S.setFrom(n))}createChecked(e=null,t,n){return t=v.from(t),this.checkContent(t),new B(this,this.computeAttrs(e),t,S.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=v.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),i=r&&r.fillBefore(v.empty,!0);return i?new B(this,e,t.append(i),S.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){U(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:S.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,r)=>n[e]=new Y(e,t,r));let r=t.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class Q{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${i}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class X{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=G(e,r.attrs),this.excluded=null;let i=K(this.attrs);this.instance=i?new S(this,i):null}create(e=null){return!e&&this.instance?this.instance:new S(this,J(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((e,i)=>n[e]=new X(e,r++,t,i)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){U(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class Z{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=b.from(e.nodes),t.marks=b.from(e.marks||{}),this.nodes=Y.compile(this.spec.nodes,this),this.marks=X.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],r=t.spec.content||"",i=t.spec.marks;if(t.contentMatch=n[r]||(n[r]=F.parse(r,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==i?null:i?ee(this,i.split(" ")):""!=i&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:ee(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof Y){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new $(n,n.defaultAttrs,e,S.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return B.fromJSON(this,e)}markFromJSON(e){return S.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function ee(e,t){let n=[];for(let r=0;r<t.length;r++){let i=t[r],o=e.marks[i],s=o;if(o)n.push(o);else for(let t in e.marks){let r=e.marks[t];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=r)}if(!s)throw SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class et{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new el(this,t,!1);return n.addAll(e,S.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new el(this,t,!0);return n.addAll(e,S.none,t.from,t.to),C.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){var r,i;let n=this.tags[o];if(r=e,i=n.tag,(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,i)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],o=r.style;if(0==o.indexOf(e)&&(!r.context||n.matchesContext(r.context))&&(!(o.length>e.length)||61==o.charCodeAt(e.length)&&o.slice(e.length+1)==t)){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let t in e.marks){let r=e.marks[t].spec.parseDOM;r&&r.forEach(e=>{n(e=ea(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let r=e.nodes[t].spec.parseDOM;r&&r.forEach(e=>{n(e=ea(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new et(e,et.schemaRules(e)))}}let en={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},er={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},ei={ol:!0,ul:!0};function eo(e,t,n){return null!=t?!!t|2*("full"===t):e&&"pre"==e.whitespace?3:-5&n}class es{constructor(e,t,n,r,i,o){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=o,this.content=[],this.activeMarks=S.none,this.match=i||(4&o?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(v.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=v.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(v.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!en.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class el{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=t.topNode,i,o=eo(null,t.preserveWhitespace,0)|4*!!n;i=r?new es(r.type,r.attrs,S.none,!0,t.topMatch||r.type.contentMatch,o):n?new es(null,null,S.none,!0,null,o):new es(e.schema.topNodeType,null,S.none,!0,null,o),this.nodes=[i],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],i=e.previousSibling;(!t||i&&"BR"==i.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,i=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),s;ei.hasOwnProperty(o)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&ei.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(s=this.parser.matchTag(e,this,n));e:if(l?l.ignore:er.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,r=this.needsBlock;if(en.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let s=l&&l.skip?t:this.readStyles(e,t);s&&this.addAll(e,s),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?s:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let r=this.parser.matchedStyles[e],i=n.getPropertyValue(r);if(i)for(let e;;){let n=this.parser.matchStyle(r,i,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,r){let i,o;if(t.node)if((o=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(o.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(o,t.attrs||null,n,t.preserveWhitespace);e&&(i=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n,!1));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}i&&this.sync(s)&&this.open--}addAll(e,t,n,r){let i=n||0;for(let o=n?e.childNodes[n]:e.firstChild,s=null==r?null:e.childNodes[r];o!=s;o=o.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(o,t);this.findAtPoint(e,i)}findPlace(e,t,n){let r,i;for(let t=this.open,o=0;t>=0;t--){let s=this.nodes[t],l=s.findWrapping(e);if(l&&(!r||r.length>l.length+o)&&(r=l,i=s,!l.length))break;if(s.solid){if(n)break;o+=2}}if(!r)return null;this.sync(i);for(let e=0;e<r.length;e++)t=this.enterInner(r[e],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=S.none;for(let i of r.concat(e.marks))(t.type?t.type.allowsMarkType(i.type):ec(i.type,e.type))&&(n=i.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,r){let i=this.findPlace(e.create(t),n,!1);return i&&(i=this.enterInner(e,t,n,!0,r)),i}enterInner(e,t,n,r=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let s=eo(e,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let l=S.none;return n=n.filter(t=>(o.type?!o.type.allowsMarkType(t.type):!ec(t.type,e))||(l=t.addToSet(l),!1)),this.nodes.push(new es(e,t,l,r,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;else this.localPreserveWS&&(this.nodes[t].options|=1);return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+ +!r,o=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=i;s--)if(o(e-1,s))return!0;return!1}{let e=s>0||0==s&&r?this.nodes[s].type:n&&s>=i?n.node(s-i).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function ea(e){let t={};for(let n in e)t[n]=e[n];return t}function ec(e,t){let n=t.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(e))continue;let o=[],s=e=>{o.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:i}=e.edge(n);if(r==t||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class ed{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=ep(t).createDocumentFragment());let r=n,i=[];return e.forEach(e=>{if(i.length||e.marks.length){let n=0,o=0;for(;n<i.length&&o<e.marks.length;){let t=e.marks[o];if(!this.marks[t.type.name]){o++;continue}if(!t.eq(i[n][0])||!1===t.type.spec.spanning)break;n++,o++}for(;n<i.length;)r=i.pop()[1];for(;o<e.marks.length;){let n=e.marks[o++],s=this.serializeMark(n,e.isInline,t);s&&(i.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=ef(ep(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&ef(ep(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return ef(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new ed(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=eh(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return eh(e.marks)}}function eh(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function ep(e){return e.document||window.document}let eu=new WeakMap;function ef(e,t,n,r){let i,o,s;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let l=t[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(o=eu.get(r))&&eu.set(r,(s=null,!function e(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])s||(s=[]),s.push(t);else for(let n=0;n<t.length;n++)e(t[n]);else for(let n in t)e(t[n])}(r),o=s)),a=o)&&a.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let c=l.indexOf(" ");c>0&&(n=l.slice(0,c),l=l.slice(c+1));let d=n?e.createElementNS(n,l):e.createElement(l),h=t[1],p=1;if(h&&"object"==typeof h&&null==h.nodeType&&!Array.isArray(h)){for(let e in p=2,h)if(null!=h[e]){let t=e.indexOf(" ");t>0?d.setAttributeNS(e.slice(0,t),e.slice(t+1),h[e]):d.setAttribute(e,h[e])}}for(let o=p;o<t.length;o++){let s=t[o];if(0===s){if(o<t.length-1||o>p)throw RangeError("Content hole must be the only child of its parent node");return{dom:d,contentDOM:d}}{let{dom:t,contentDOM:o}=ef(e,s,n,r);if(d.appendChild(t),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:d,contentDOM:i}}class em{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class eg{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&eg.empty)return eg.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,i=this.inverted?2:1,o=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let l=this.ranges[s]-(this.inverted?r:0);if(l>e)break;let a=this.ranges[s+i],c=this.ranges[s+o],d=l+a;if(e<=d){let i=a?e==l?-1:e==d?1:t:t,o=l+r+(i<0?0:c);if(n)return o;let h=e==(t<0?l:d)?null:s/3+(e-l)*65536,p=e==l?2:e==d?1:4;return(t<0?e!=l:e!=d)&&(p|=8),new em(o,p,h)}r+=c-a}return n?e+r:new em(e+r,0,null)}touches(e,t){let n=0,r=65535&t,i=this.inverted?2:1,o=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let s=this.ranges[t]-(this.inverted?n:0);if(s>e)break;let l=this.ranges[t+i];if(e<=s+l&&t==3*r)return!0;n+=this.ranges[t+o]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let o=this.ranges[r],s=o-(this.inverted?i:0),l=o+(this.inverted?0:i),a=this.ranges[r+t],c=this.ranges[r+n];e(s,s+a,l,l+c),i+=c-a}}invert(){return new eg(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?eg.empty:new eg(e<0?[0,-e,0]:[0,0,e])}}eg.empty=new eg([]);class ey{constructor(e,t,n=0,r=e?e.length:0){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new ey(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new ey;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this._maps[n].mapResult(e,t);if(null!=i.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this._maps[t].recover(i.recover);continue}}r|=i.delInfo,e=i.pos}return n?e:new em(e,r,null)}}let eb=Object.create(null);class ev{getMap(){return eg.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=eb[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in eb)throw RangeError("Duplicate use of step JSON ID "+e);return eb[e]=t,t.prototype.jsonID=e,t}}class ew{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new ew(e,null)}static fail(e){return new ew(null,e)}static fromReplace(e,t,n,r){try{return ew.ok(e.replace(t,n,r))}catch(e){if(e instanceof M)return ew.fail(e.message);throw e}}}function ek(e,t,n){let r=[];for(let i=0;i<e.childCount;i++){let o=e.child(i);o.content.size&&(o=o.copy(ek(o.content,t,o))),o.isInline&&(o=t(o,n,i)),r.push(o)}return v.fromArray(r)}class ex extends ev{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),r=n.node(n.sharedDepth(this.to)),i=new C(ek(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,r),t.openStart,t.openEnd);return ew.fromReplace(e,this.from,this.to,i)}invert(){return new eS(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new ex(t.pos,n.pos,this.mark)}merge(e){return e instanceof ex&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new ex(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new ex(t.from,t.to,e.markFromJSON(t.mark))}}ev.jsonID("addMark",ex);class eS extends ev{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new C(ek(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return ew.fromReplace(e,this.from,this.to,n)}invert(){return new ex(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new eS(t.pos,n.pos,this.mark)}merge(e){return e instanceof eS&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new eS(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new eS(t.from,t.to,e.markFromJSON(t.mark))}}ev.jsonID("removeMark",eS);class eM extends ev{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return ew.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return ew.fromReplace(e,this.pos,this.pos+1,new C(v.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new eM(this.pos,t.marks[n]);return new eM(this.pos,this.mark)}}return new eC(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new eM(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new eM(t.pos,e.markFromJSON(t.mark))}}ev.jsonID("addNodeMark",eM);class eC extends ev{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return ew.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return ew.fromReplace(e,this.pos,this.pos+1,new C(v.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new eM(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new eC(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new eC(t.pos,e.markFromJSON(t.mark))}}ev.jsonID("removeNodeMark",eC);class eO extends ev{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&eA(e,this.from,this.to)?ew.fail("Structure replace would overwrite content"):ew.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new eg([this.from,this.to-this.from,this.slice.size])}invert(e){return new eO(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new eO(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof eO)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart)if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;else{let t=this.slice.size+e.slice.size==0?C.empty:new C(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new eO(e.from,this.to,t,this.structure)}{let t=this.slice.size+e.slice.size==0?C.empty:new C(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new eO(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new eO(t.from,t.to,C.fromJSON(e,t.slice),!!t.structure)}}ev.jsonID("replace",eO);class eN extends ev{constructor(e,t,n,r,i,o,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=o,this.structure=s}apply(e){if(this.structure&&(eA(e,this.from,this.gapFrom)||eA(e,this.gapTo,this.to)))return ew.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return ew.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?ew.fromReplace(e,this.from,this.to,n):ew.fail("Content does not fit in gap")}getMap(){return new eg([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new eN(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||i>n.pos?null:new eN(t.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new eN(t.from,t.to,t.gapFrom,t.gapTo,C.fromJSON(e,t.slice),t.insert,!!t.structure)}}function eA(e,t,n){let r=e.resolve(t),i=n-t,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let e=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,i--}}return!1}function eT(e,t,n,r=n.contentMatch,i=!0){let o=e.doc.nodeAt(t),s=[],l=t+1;for(let t=0;t<o.childCount;t++){let a=o.child(t),c=l+a.nodeSize,d=r.matchType(a.type);if(d){r=d;for(let t=0;t<a.marks.length;t++)n.allowsMarkType(a.marks[t].type)||e.step(new eS(l,c,a.marks[t]));if(i&&a.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,r;for(;e=t.exec(a.text);)r||(r=new C(v.from(n.schema.text(" ",n.allowedMarks(a.marks))),0,0)),s.push(new eO(l+e.index,l+e.index+e[0].length,r))}}else s.push(new eO(l,c,C.empty));l=c}if(!r.validEnd){let t=r.fillBefore(v.empty,!0);e.replace(l,l,new C(t,0,0))}for(let t=s.length-1;t>=0;t--)e.step(s[t])}function eE(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),i=e.$from.index(n),o=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(i,o,t))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(o==r.childCount||r.canReplace(0,o))))break}return null}function eD(e,t,n=null,r=e){let i=function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.contentMatchAt(r).findWrapping(t);if(!o)return null;let s=o.length?o[0]:t;return n.canReplaceWith(r,i,s)?o:null}(e,t),o=i&&function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.child(r),s=t.contentMatch.findWrapping(o.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let e=r;l&&e<i;e++)l=l.matchType(n.child(e).type);return l&&l.validEnd?s:null}(r,t);return o?i.map(eR).concat({type:t,attrs:n}).concat(o.map(eR)):null}function eR(e){return{type:e,attrs:null}}function eI(e,t,n,r){t.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let i=e.mapping.slice(r).map(n+1+o+s.index);e.replaceWith(i,i+1,t.type.schema.linebreakReplacement.create())}}})}function eP(e,t,n,r){t.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=e.mapping.slice(r).map(n+1+o);e.replaceWith(i,i+1,t.type.schema.text("\n"))}})}function ej(e,t,n=1,r){let i=e.resolve(t),o=i.depth-n,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let e=i.depth-1,t=n-2;e>o;e--,t--){let n=i.node(e),o=i.index(e);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(o,n.childCount),l=r&&r[t+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[t]||n;if(!n.canReplace(o+1,n.childCount)||!a.type.validContent(s))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function eL(e,t){let n=e.resolve(t),r=n.index();return ez(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function ez(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let i=0;i<t.childCount;i++){let o=t.child(i),s=o.type==r?e.type.schema.nodes.text:o.type;if(!(n=n.matchType(s))||!e.type.allowsMarks(o.marks))return!1}return n.validEnd}(e,t))}function eB(e,t,n=-1){let r=e.resolve(t);for(let e=r.depth;;e--){let i,o,s=r.index(e);if(e==r.depth?(i=r.nodeBefore,o=r.nodeAfter):n>0?(i=r.node(e+1),s++,o=r.node(e).maybeChild(s)):(i=r.node(e).maybeChild(s-1),o=r.node(e+1)),i&&!i.isTextblock&&ez(i,o)&&r.node(e).canReplace(s,s+1))return t;if(0==e)break;t=n<0?r.before(e):r.after(e)}}function e$(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let i=n.content;for(let e=0;e<n.openStart;e++)i=i.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=r.depth;t>=0;t--){let n=t==r.depth?0:r.pos<=(r.start(t+1)+r.end(t+1))/2?-1:1,o=r.index(t)+ +(n>0),s=r.node(t),l=!1;if(1==e)l=s.canReplace(o,o,i);else{let e=s.contentMatchAt(o).findWrapping(i.firstChild.type);l=e&&s.canReplaceWith(o,o,e[0])}if(l)return 0==n?r.pos:n<0?r.before(t+1):r.after(t+1)}return null}function eV(e,t,n=t,r=C.empty){if(t==n&&!r.size)return null;let i=e.resolve(t),o=e.resolve(n);return eF(i,o,r)?new eO(t,n,r):new eH(i,o,r).fit()}function eF(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}ev.jsonID("replaceAround",eN);class eH{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=v.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=v.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(e<0?this.$to:n.doc.resolve(e));if(!r)return null;let i=this.placed,o=n.depth,s=r.depth;for(;o&&s&&1==i.childCount;)i=i.firstChild.content,o--,s--;let l=new C(i,o,s);return e>-1?new eN(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new eO(n.pos,r.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,r=null,i=(n?(r=eW(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let e=this.depth;e>=0;e--){let{type:o,match:s}=this.frontier[e],l,a=null;if(1==t&&(i?s.matchType(i.type)||(a=s.fillBefore(v.from(i),!1)):r&&o.compatibleContent(r.type)))return{sliceDepth:n,frontierDepth:e,parent:r,inject:a};if(2==t&&i&&(l=s.findWrapping(i.type)))return{sliceDepth:n,frontierDepth:e,parent:r,wrap:l};if(r&&s.matchType(r.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=eW(e,t);return!!r.childCount&&!r.firstChild.isLeaf&&(this.unplaced=new C(e,t+1,Math.max(n,r.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=eW(e,t);if(r.childCount<=1&&t>0){let i=e.size-t<=t+r.size;this.unplaced=new C(e_(e,t-1,1),t-1,i?t-1:n)}else this.unplaced=new C(e_(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:r,wrap:i}){for(;this.depth>t;)this.closeFrontierNode();if(i)for(let e=0;e<i.length;e++)this.openFrontierNode(i[e]);let o=this.unplaced,s=n?n.content:o.content,l=o.openStart-e,a=0,c=[],{match:d,type:h}=this.frontier[t];if(r){for(let e=0;e<r.childCount;e++)c.push(r.child(e));d=d.matchFragment(r)}let p=s.size+e-(o.content.size-o.openEnd);for(;a<s.childCount;){let e=s.child(a),t=d.matchType(e.type);if(!t)break;(++a>1||0==l||e.content.size)&&(d=t,c.push(function e(t,n,r){if(n<=0)return t;let i=t.content;return n>1&&(i=i.replaceChild(0,e(i.firstChild,n-1,1==i.childCount?r-1:0))),n>0&&(i=t.type.contentMatch.fillBefore(i).append(i),r<=0&&(i=i.append(t.type.contentMatch.matchFragment(i).fillBefore(v.empty,!0)))),t.copy(i)}(e.mark(h.allowedMarks(e.marks)),1==a?l:0,a==s.childCount?p:-1)))}let u=a==s.childCount;u||(p=-1),this.placed=eq(this.placed,t,v.from(c)),this.frontier[t].match=d,u&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=s;e<p;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=u?0==e?C.empty:new C(e_(o.content,e-1,1),e-1,p<0?o.openEnd:e-1):new C(e_(o.content,e,a),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!eK(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){t:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=eK(e,t,r,n,i);if(o){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],i=eK(e,n,r,t,!0);if(!i||i.childCount)continue t}return{depth:t,fit:o,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=eq(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(e),this.placed=eq(this.placed,this.depth,v.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(v.empty,!0);e.childCount&&(this.placed=eq(this.placed,this.frontier.length,e))}}function e_(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(e_(e.firstChild.content,t-1,n)))}function eq(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(eq(e.lastChild.content,t-1,n)))}function eW(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function eK(e,t,n,r,i){let o=e.node(t),s=i?e.indexAfter(t):e.index(t);if(s==o.childCount&&!n.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,o.content,s)?l:null}function eJ(e,t){let n=[],r=Math.min(e.depth,t.depth);for(let i=r;i>=0;i--){let r=e.start(i);if(r<e.pos-(e.depth-i)||t.end(i)>t.pos+(t.depth-i)||e.node(i).type.spec.isolating||t.node(i).type.spec.isolating)break;(r==t.start(i)||i==e.depth&&i==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&i&&t.start(i-1)==r-1)&&n.push(i)}return n}class eU extends ev{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return ew.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let r=t.type.create(n,null,t.marks);return ew.fromReplace(e,this.pos,this.pos+1,new C(v.from(r),0,+!t.isLeaf))}getMap(){return eg.empty}invert(e){return new eU(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new eU(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new eU(t.pos,t.attr,t.value)}}ev.jsonID("attr",eU);class eG extends ev{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return ew.ok(n)}getMap(){return eg.empty}invert(e){return new eG(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new eG(t.attr,t.value)}}ev.jsonID("docAttr",eG);let eY=class extends Error{};(eY=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),eY.prototype.constructor=eY,eY.prototype.name="TransformError";class eQ{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new ey}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new eY(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=C.empty){let r=eV(this.doc,e,t,n);return r&&this.step(r),this}replaceWith(e,t,n){return this.replace(e,t,new C(v.from(n),0,0))}delete(e,t){return this.replace(e,t,C.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return!function(e,t,n,r){if(!r.size)return e.deleteRange(t,n);let i=e.doc.resolve(t),o=e.doc.resolve(n);if(eF(i,o,r))return e.step(new eO(t,n,r));let s=eJ(i,e.doc.resolve(n));0==s[s.length-1]&&s.pop();let l=-(i.depth+1);s.unshift(l);for(let e=i.depth,t=i.pos-1;e>0;e--,t--){let n=i.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;s.indexOf(e)>-1?l=e:i.before(e)==t&&s.splice(1,0,-e)}let a=s.indexOf(l),c=[],d=r.openStart;for(let e=r.content,t=0;;t++){let n=e.firstChild;if(c.push(n),t==r.openStart)break;e=n.content}for(let e=d-1;e>=0;e--){var h;let t=c[e],n=(h=t.type).spec.defining||h.spec.definingForContent;if(n&&!t.sameMarkup(i.node(Math.abs(l)-1)))d=e;else if(n||!t.type.isTextblock)break}for(let t=r.openStart;t>=0;t--){let l=(t+d+1)%(r.openStart+1),h=c[l];if(h)for(let t=0;t<s.length;t++){let c=s[(t+a)%s.length],d=!0;c<0&&(d=!1,c=-c);let p=i.node(c-1),u=i.index(c-1);if(p.canReplaceWith(u,u,h.type,h.marks))return e.replace(i.before(c),d?o.after(c):n,new C(function e(t,n,r,i,o){if(n<r){let o=t.firstChild;t=t.replaceChild(0,o.copy(e(o.content,n+1,r,i,o)))}if(n>i){let e=o.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(v.empty,!0))}return t}(r.content,0,r.openStart,l),l,r.openEnd))}}let p=e.steps.length;for(let l=s.length-1;l>=0&&(e.replace(t,n,r),!(e.steps.length>p));l--){let e=s[l];e<0||(t=i.before(e),n=o.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){var r=e,i=t;if(!n.isInline&&r==i&&this.doc.resolve(r).parent.content.size){let e=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let e=r.depth-1;e>=0;e--){let t=r.index(e);if(r.node(e).canReplaceWith(t,t,n))return r.before(e+1);if(t>0)return null}if(r.parentOffset==r.parent.content.size)for(let e=r.depth-1;e>=0;e--){let t=r.indexAfter(e);if(r.node(e).canReplaceWith(t,t,n))return r.after(e+1);if(t<r.node(e).childCount)break}return null}(this.doc,r,n.type);null!=e&&(r=i=e)}return this.replaceRange(r,i,new C(v.from(n),0,0)),this}deleteRange(e,t){return!function(e,t,n){let r=e.doc.resolve(t),i=e.doc.resolve(n),o=eJ(r,i);for(let t=0;t<o.length;t++){let n=o[t],s=t==o.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return e.delete(r.start(n),i.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return e.delete(r.before(n),i.after(n))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(t-r.start(o)==r.depth-o&&n>r.end(o)&&i.end(o)-n!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return e.delete(r.before(o),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return!function(e,t,n){let{$from:r,$to:i,depth:o}=t,s=r.before(o+1),l=i.after(o+1),a=s,c=l,d=v.empty,h=0;for(let e=o,t=!1;e>n;e--)t||r.index(e)>0?(t=!0,d=v.from(r.node(e).copy(d)),h++):a--;let p=v.empty,u=0;for(let e=o,t=!1;e>n;e--)t||i.after(e+1)<i.end(e)?(t=!0,p=v.from(i.node(e).copy(p)),u++):c++;e.step(new eN(a,c,s,l,new C(d.append(p),h,u),d.size-h,!0))}(this,e,t),this}join(e,t=1){return!function(e,t,n){let r=null,{linebreakReplacement:i}=e.doc.type.schema,o=e.doc.resolve(t-n),s=o.node().type;if(i&&s.inlineContent){let e="pre"==s.whitespace,t=!!s.contentMatch.matchType(i);e&&!t?r=!1:!e&&t&&(r=!0)}let l=e.steps.length;if(!1===r){let r=e.doc.resolve(t+n);eP(e,r.node(),r.before(),l)}s.inlineContent&&eT(e,t+n-1,s,o.node().contentMatchAt(o.index()),null==r);let a=e.mapping.slice(l),c=a.map(t-n);if(e.step(new eO(c,a.map(t+n,-1),C.empty,!0)),!0===r){let t=e.doc.resolve(c);eI(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return!function(e,t,n){let r=v.empty;for(let e=n.length-1;e>=0;e--){if(r.size){let t=n[e].type.contentMatch.matchFragment(r);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=v.from(n[e].type.create(n[e].attrs,r))}let i=t.start,o=t.end;e.step(new eN(i,o,i,o,new C(r,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,r=null){var i=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let o=i.steps.length;return i.doc.nodesBetween(e,t,(e,t)=>{var s,l,a;let c,d,h="function"==typeof r?r(e):r;if(e.isTextblock&&!e.hasMarkup(n,h)&&(s=i.doc,l=i.mapping.slice(o).map(t),a=n,d=(c=s.resolve(l)).index(),c.parent.canReplaceWith(d,d+1,a))){let r=null;if(n.schema.linebreakReplacement){let e="pre"==n.whitespace,t=!!n.contentMatch.matchType(n.schema.linebreakReplacement);e&&!t?r=!1:!e&&t&&(r=!0)}!1===r&&eP(i,e,t,o),eT(i,i.mapping.slice(o).map(t,1),n,void 0,null===r);let s=i.mapping.slice(o),l=s.map(t,1),a=s.map(t+e.nodeSize,1);return i.step(new eN(l,a,l+1,a-1,new C(v.from(n.create(h,null,e.marks)),0,0),1,!0)),!0===r&&eI(i,e,t,o),!1}}),this}setNodeMarkup(e,t,n=null,r){return!function(e,t,n,r,i){let o=e.doc.nodeAt(t);if(!o)throw RangeError("No node at given position");n||(n=o.type);let s=n.create(r,null,i||o.marks);if(o.isLeaf)return e.replaceWith(t,t+o.nodeSize,s);if(!n.validContent(o.content))throw RangeError("Invalid content for node type "+n.name);e.step(new eN(t,t+o.nodeSize,t+1,t+o.nodeSize-1,new C(v.from(s),0,0),1,!0))}(this,e,t,n,r),this}setNodeAttribute(e,t,n){return this.step(new eU(e,t,n)),this}setDocAttribute(e,t){return this.step(new eG(e,t)),this}addNodeMark(e,t){return this.step(new eM(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(t instanceof S)t.isInSet(n.marks)&&this.step(new eC(e,t));else{let r=n.marks,i,o=[];for(;i=t.isInSet(r);)o.push(new eC(e,i)),r=i.removeFromSet(r);for(let e=o.length-1;e>=0;e--)this.step(o[e])}return this}split(e,t=1,n){return!function(e,t,n=1,r){let i=e.doc.resolve(t),o=v.empty,s=v.empty;for(let e=i.depth,t=i.depth-n,l=n-1;e>t;e--,l--){o=v.from(i.node(e).copy(o));let t=r&&r[l];s=v.from(t?t.type.create(t.attrs,s):i.node(e).copy(s))}e.step(new eO(t,t,new C(o.append(s),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var r;let i,o,s,l;return r=this,s=[],l=[],r.doc.nodesBetween(e,t,(r,a,c)=>{if(!r.isInline)return;let d=r.marks;if(!n.isInSet(d)&&c.type.allowsMarkType(n.type)){let c=Math.max(a,e),h=Math.min(a+r.nodeSize,t),p=n.addToSet(d);for(let e=0;e<d.length;e++)d[e].isInSet(p)||(i&&i.to==c&&i.mark.eq(d[e])?i.to=h:s.push(i=new eS(c,h,d[e])));o&&o.to==c?o.to=h:l.push(o=new ex(c,h,n))}}),s.forEach(e=>r.step(e)),l.forEach(e=>r.step(e)),this}removeMark(e,t,n){var r;let i,o;return r=this,i=[],o=0,r.doc.nodesBetween(e,t,(r,s)=>{if(!r.isInline)return;o++;let l=null;if(n instanceof X){let e=r.marks,t;for(;t=n.isInSet(e);)(l||(l=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(r.marks)&&(l=[n]):l=r.marks;if(l&&l.length){let n=Math.min(s+r.nodeSize,t);for(let t=0;t<l.length;t++){let r=l[t],a;for(let e=0;e<i.length;e++){let t=i[e];t.step==o-1&&r.eq(i[e].style)&&(a=t)}a?(a.to=n,a.step=o):i.push({style:r,from:Math.max(s,e),to:n,step:o})}}}),i.forEach(e=>r.step(new eS(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return eT(this,e,t,n),this}}let eX=Object.create(null);class eZ{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new e0(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=C.empty){let n=t.content.lastChild,r=null;for(let e=0;e<t.openEnd;e++)r=n,n=n.lastChild;let i=e.steps.length,o=this.ranges;for(let s=0;s<o.length;s++){let{$from:l,$to:a}=o[s],c=e.mapping.slice(i);e.replaceRange(c.map(l.pos),c.map(a.pos),s?C.empty:t),0==s&&te(e,i,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=e.mapping.slice(n),a=l.map(o.pos),c=l.map(s.pos);i?e.deleteRange(a,c):(e.replaceRangeWith(a,c,t),te(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new e3(e):e9(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let r=e.depth-1;r>=0;r--){let i=t<0?e9(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):e9(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(i)return i}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new e8(e.node(0))}static atStart(e){return e9(e,e,0,0,1)||new e8(e)}static atEnd(e){return e9(e,e,e.content.size,e.childCount,-1)||new e8(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=eX[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in eX)throw RangeError("Duplicate use of selection JSON ID "+e);return eX[e]=t,t.prototype.jsonID=e,t}getBookmark(){return e3.between(this.$anchor,this.$head).getBookmark()}}eZ.prototype.visible=!0;class e0{constructor(e,t){this.$from=e,this.$to=t}}let e1=!1;function e2(e){e1||e.parent.inlineContent||(e1=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class e3 extends eZ{constructor(e,t=e){e2(e),e2(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return eZ.near(n);let r=e.resolve(t.map(this.anchor));return new e3(r.parent.inlineContent?r:n,n)}replace(e,t=C.empty){if(super.replace(e,t),t==C.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof e3&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new e4(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new e3(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let e=eZ.findFrom(t,n,!0)||eZ.findFrom(t,-n,!0);if(!e)return eZ.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r?e=t:(e=(eZ.findFrom(e,-n,!0)||eZ.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0&&(e=t)),new e3(e,t)}}eZ.jsonID("text",e3);class e4{constructor(e,t){this.anchor=e,this.head=t}map(e){return new e4(e.map(this.anchor),e.map(this.head))}resolve(e){return e3.between(e.resolve(this.anchor),e.resolve(this.head))}}class e5 extends eZ{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),i=e.resolve(r);return n?eZ.near(i):new e5(i)}content(){return new C(v.from(this.node),0,0)}eq(e){return e instanceof e5&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new e6(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new e5(e.resolve(t.anchor))}static create(e,t){return new e5(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}e5.prototype.visible=!1,eZ.jsonID("node",e5);class e6{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new e4(n,n):new e6(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&e5.isSelectable(n)?new e5(t):eZ.near(t)}}class e8 extends eZ{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=C.empty){if(t==C.empty){e.delete(0,e.doc.content.size);let t=eZ.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new e8(e)}map(e){return new e8(e)}eq(e){return e instanceof e8}getBookmark(){return e7}}eZ.jsonID("all",e8);let e7={map(){return this},resolve:e=>new e8(e)};function e9(e,t,n,r,i,o=!1){if(t.inlineContent)return e3.create(e,n);for(let s=r-(i>0?0:1);i>0?s<t.childCount:s>=0;s+=i){let r=t.child(s);if(r.isAtom){if(!o&&e5.isSelectable(r))return e5.create(e,n-(i<0?r.nodeSize:0))}else{let t=e9(e,r,n+i,i<0?r.childCount:0,i,o);if(t)return t}n+=r.nodeSize*i}return null}function te(e,t,n){let r,i=e.steps.length-1;if(i<t)return;let o=e.steps[i];(o instanceof eO||o instanceof eN)&&(e.mapping.maps[i].forEach((e,t,n,i)=>{null==r&&(r=i)}),e.setSelection(eZ.near(e.doc.resolve(r),n)))}class tt extends eQ{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return S.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||S.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let e=this.doc.resolve(t);i=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,i)),this.selection.empty||this.setSelection(eZ.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function tn(e,t){return t&&e?e.bind(t):e}class tr{constructor(e,t,n){this.name=e,this.init=tn(t.init,n),this.apply=tn(t.apply,n)}}let ti=[new tr("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new tr("selection",{init:(e,t)=>e.selection||eZ.atStart(t.doc),apply:e=>e.selection}),new tr("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new tr("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class to{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=ti.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new tr(e.key,e.spec.state,e))})}}class ts{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let l=r?r[o].n:0,a=r?r[o].state:this,c=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(c&&n.filterTransaction(c,o)){if(c.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<o?{state:n,n:t.length}:{state:this,n:0})}t.push(c),n=n.applyInner(c),i=!0}r&&(r[o]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new ts(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new tt(this)}static create(e){let t=new to(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new ts(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new to(this.schema,e.plugins),n=t.fields,r=new ts(t);for(let t=0;t<n.length;t++){let i=n[t].name;r[i]=this.hasOwnProperty(i)?this[i]:n[t].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],i=r.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let r=new to(e.schema,e.plugins),i=new ts(r);return r.fields.forEach(r=>{if("doc"==r.name)i.doc=B.fromJSON(e.schema,t.doc);else if("selection"==r.name)i.selection=eZ.fromJSON(i.doc,t.selection);else if("storedMarks"==r.name)t.storedMarks&&(i.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let o in n){let s=n[o],l=s.spec.state;if(s.key==r.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,o)){i[r.name]=l.fromJSON.call(s,e,t[o],i);return}}i[r.name]=r.init(e,i)}}),i}}class tl{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,r){for(let i in t){let o=t[i];o instanceof Function?o=o.bind(n):"handleDOMEvents"==i&&(o=e(o,n,{})),r[i]=o}return r}(e.props,this,this.props),this.key=e.key?e.key.key:tc("plugin")}getState(e){return e[this.key]}}let ta=Object.create(null);function tc(e){return e in ta?e+"$"+ ++ta[e]:(ta[e]=0,e+"$")}class td{constructor(e="key"){this.key=tc(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}let th=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},tp=function(e){let t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t},tu=null,tf=function(e,t,n){let r=tu||(tu=document.createRange());return r.setEnd(e,null==n?e.nodeValue.length:n),r.setStart(e,t||0),r},tm=function(){tu=null},tg=function(e,t,n,r){return n&&(tb(e,t,n,r,-1)||tb(e,t,n,r,1))},ty=/^(img|br|input|textarea|hr)$/i;function tb(e,t,n,r,i){for(var o;;){if(e==n&&t==r)return!0;if(t==(i<0?0:tv(e))){let n=e.parentNode;if(!n||1!=n.nodeType||tw(e)||ty.test(e.nodeName)||"false"==e.contentEditable)return!1;t=th(e)+(i<0?0:1),e=n}else{if(1!=e.nodeType)return!1;let n=e.childNodes[t+(i<0?-1:0)];if(1==n.nodeType&&"false"==n.contentEditable)if(null==(o=n.pmViewDesc)||!o.ignoreForSelection)return!1;else t+=i;else e=n,t=i<0?tv(e):0}}}function tv(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function tw(e){let t;for(let n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}let tk=function(e){return e.focusNode&&tg(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)};function tx(e,t){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}let tS="undefined"!=typeof navigator?navigator:null,tM="undefined"!=typeof document?document:null,tC=tS&&tS.userAgent||"",tO=/Edge\/(\d+)/.exec(tC),tN=/MSIE \d/.exec(tC),tA=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(tC),tT=!!(tN||tA||tO),tE=tN?document.documentMode:tA?+tA[1]:tO?+tO[1]:0,tD=!tT&&/gecko\/(\d+)/i.test(tC);tD&&(/Firefox\/(\d+)/.exec(tC)||[0,0])[1];let tR=!tT&&/Chrome\/(\d+)/.exec(tC),tI=!!tR,tP=tR?+tR[1]:0,tj=!tT&&!!tS&&/Apple Computer/.test(tS.vendor),tL=tj&&(/Mobile\/\w+/.test(tC)||!!tS&&tS.maxTouchPoints>2),tz=tL||!!tS&&/Mac/.test(tS.platform),tB=!!tS&&/Win/.test(tS.platform),t$=/Android \d/.test(tC),tV=!!tM&&"webkitFontSmoothing"in tM.documentElement.style,tF=tV?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function tH(e,t){return"number"==typeof e?e:e[t]}function t_(e,t,n){let r=e.someProp("scrollThreshold")||0,i=e.someProp("scrollMargin")||5,o=e.dom.ownerDocument;for(let s=n||e.dom;s;){if(1!=s.nodeType){s=tp(s);continue}let e=s,n=e==o.body,l=n?function(e){let t=e.defaultView&&e.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}(o):function(e){let t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,r=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*r}}(e),a=0,c=0;if(t.top<l.top+tH(r,"top")?c=-(l.top-t.top+tH(i,"top")):t.bottom>l.bottom-tH(r,"bottom")&&(c=t.bottom-t.top>l.bottom-l.top?t.top+tH(i,"top")-l.top:t.bottom-l.bottom+tH(i,"bottom")),t.left<l.left+tH(r,"left")?a=-(l.left-t.left+tH(i,"left")):t.right>l.right-tH(r,"right")&&(a=t.right-l.right+tH(i,"right")),a||c)if(n)o.defaultView.scrollBy(a,c);else{let n=e.scrollLeft,r=e.scrollTop;c&&(e.scrollTop+=c),a&&(e.scrollLeft+=a);let i=e.scrollLeft-n,o=e.scrollTop-r;t={left:t.left-i,top:t.top-o,right:t.right-i,bottom:t.bottom-o}}let d=n?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(d))break;s="absolute"==d?s.offsetParent:tp(s)}}function tq(e){let t=[],n=e.ownerDocument;for(let r=e;r&&(t.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),e!=n);r=tp(r));return t}function tW(e,t){for(let n=0;n<e.length;n++){let{dom:r,top:i,left:o}=e[n];r.scrollTop!=i+t&&(r.scrollTop=i+t),r.scrollLeft!=o&&(r.scrollLeft=o)}}let tK=null;function tJ(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function tU(e){return e.top<e.bottom||e.left<e.right}function tG(e,t){let n=e.getClientRects();if(n.length){let e=n[t<0?0:n.length-1];if(tU(e))return e}return Array.prototype.find.call(n,tU)||e.getBoundingClientRect()}let tY=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function tQ(e,t,n){let{node:r,offset:i,atom:o}=e.docView.domFromPos(t,n<0?-1:1),s=tV||tD;if(3==r.nodeType)if(s&&(tY.test(r.nodeValue)||(n<0?!i:i==r.nodeValue.length))){let e=tG(tf(r,i,i),n);if(tD&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let t=tG(tf(r,i-1,i-1),-1);if(t.top==e.top){let n=tG(tf(r,i,i+1),-1);if(n.top!=e.top)return tX(n,n.left<t.left)}}return e}else{let e=i,t=i,o=n<0?1:-1;return n<0&&!i?(t++,o=-1):n>=0&&i==r.nodeValue.length?(e--,o=1):n<0?e--:t++,tX(tG(tf(r,e,t),o),o<0)}if(!e.state.doc.resolve(t-(o||0)).parent.inlineContent){if(null==o&&i&&(n<0||i==tv(r))){let e=r.childNodes[i-1];if(1==e.nodeType)return tZ(e.getBoundingClientRect(),!1)}if(null==o&&i<tv(r)){let e=r.childNodes[i];if(1==e.nodeType)return tZ(e.getBoundingClientRect(),!0)}return tZ(r.getBoundingClientRect(),n>=0)}if(null==o&&i&&(n<0||i==tv(r))){let e=r.childNodes[i-1],t=3==e.nodeType?tf(e,tv(e)-!s):1!=e.nodeType||"BR"==e.nodeName&&e.nextSibling?null:e;if(t)return tX(tG(t,1),!1)}if(null==o&&i<tv(r)){let e=r.childNodes[i];for(;e.pmViewDesc&&e.pmViewDesc.ignoreForCoords;)e=e.nextSibling;let t=e?3==e.nodeType?tf(e,0,+!s):1==e.nodeType?e:null:null;if(t)return tX(tG(t,-1),!0)}return tX(tG(3==r.nodeType?tf(r):r,-n),n>=0)}function tX(e,t){if(0==e.width)return e;let n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function tZ(e,t){if(0==e.height)return e;let n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function t0(e,t,n){let r=e.state,i=e.root.activeElement;r!=t&&e.updateState(t),i!=e.dom&&e.focus();try{return n()}finally{r!=t&&e.updateState(r),i!=e.dom&&i&&i.focus()}}let t1=/[\u0590-\u08ac]/,t2=null,t3=null,t4=!1;class t5{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){let r;if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode))if(n<0){let n,r;if(e==this.contentDOM)n=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.previousSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}else{let n,r;if(e==this.contentDOM)n=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.nextSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}if(e==this.dom&&this.contentDOM)r=t>th(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!1;break}if(t.previousSibling)break}if(null==r&&t==e.childNodes.length)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!0;break}if(t.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let i=this.getDesc(r),o;if(i&&(!t||i.node))if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==e.nodeType?e:e.parentNode):o==e))return i;else n=!1}}getDesc(e){let t=e.pmViewDesc;for(let e=t;e;e=e.parent)if(e==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let i=this.getDesc(r);if(i)return i.localPosFromDOM(e,t,n)}return -1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],i=n+r.size;if(n==e&&i!=n){for(;!r.border&&r.children.length;)for(let e=0;e<r.children.length;e++){let t=r.children[e];if(t.size){r=t;break}}return r}if(e<i)return r.descAt(e-n-r.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let t=0;n<this.children.length;n++){let i=this.children[n],o=t+i.size;if(o>e||i instanceof nn){r=e-t;break}t=o}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let e;n&&!(e=this.children[n-1]).size&&e instanceof t6&&e.side>=0;n--);if(t<=0){let e,r=!0;for(;(e=n?this.children[n-1]:null)&&e.dom.parentNode!=this.contentDOM;n--,r=!1);return e&&t&&r&&!e.border&&!e.domAtom?e.domFromPos(e.size,t):{node:this.contentDOM,offset:e?th(e.dom)+1:0}}{let e,r=!0;for(;(e=n<this.children.length?this.children[n]:null)&&e.dom.parentNode!=this.contentDOM;n++,r=!1);return e&&r&&!e.border&&!e.domAtom?e.domFromPos(0,t):{node:this.contentDOM,offset:e?th(e.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,i=-1;for(let o=n,s=0;;s++){let n=this.children[s],l=o+n.size;if(-1==r&&e<=l){let i=o+n.border;if(e>=i&&t<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(e,t,i);e=o;for(let t=s;t>0;t--){let n=this.children[t-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=th(n.dom)+1;break}e-=n.size}-1==r&&(r=0)}if(r>-1&&(l>t||s==this.children.length-1)){t=l;for(let e=s+1;e<this.children.length;e++){let n=this.children[e];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){i=th(n.dom);break}t+=n.size}-1==i&&(i=this.contentDOM.childNodes.length);break}o=l}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let i=Math.min(e,t),o=Math.max(e,t);for(let s=0,l=0;s<this.children.length;s++){let a=this.children[s],c=l+a.size;if(i>l&&o<c)return a.setSelection(e-l-a.border,t-l-a.border,n,r);l=c}let s=this.domFromPos(e,e?-1:1),l=t==e?s:this.domFromPos(t,t?-1:1),a=n.root.getSelection(),c=n.domSelectionRange(),d=!1;if((tD||tj)&&e==t){let{node:e,offset:t}=s;if(3==e.nodeType){if((d=!!(t&&"\n"==e.nodeValue[t-1]))&&t==e.nodeValue.length)for(let t=e,n;t;t=t.parentNode){if(n=t.nextSibling){"BR"==n.nodeName&&(s=l={node:n.parentNode,offset:th(n)+1});break}let e=t.pmViewDesc;if(e&&e.node&&e.node.isBlock)break}}else{let n=e.childNodes[t-1];d=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(tD&&c.focusNode&&c.focusNode!=l.node&&1==c.focusNode.nodeType){let e=c.focusNode.childNodes[c.focusOffset];e&&"false"==e.contentEditable&&(r=!0)}if(!(r||d&&tj)&&tg(s.node,s.offset,c.anchorNode,c.anchorOffset)&&tg(l.node,l.offset,c.focusNode,c.focusOffset))return;let h=!1;if((a.extend||e==t)&&!d){a.collapse(s.node,s.offset);try{e!=t&&a.extend(l.node,l.offset),h=!0}catch(e){}}if(!h){if(e>t){let e=s;s=l,l=e}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(s.node,s.offset),a.removeAllRanges(),a.addRange(n)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let i=this.children[r],o=n+i.size;if(n==o?e<=o&&t>=n:e<o&&t>n){let r=n+i.border,s=o-i.border;if(e>=r&&t<=s){this.dirty=e==n||t==o?2:1,e==r&&t==s&&(i.contentLost||i.dom.parentNode!=this.contentDOM)?i.dirty=3:i.markDirty(e-r,t-r);return}i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=o}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}}class t6 extends t5{constructor(e,t,n,r){let i,o=t.type.toDOM;if("function"==typeof o&&(o=o(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:r)),!t.type.spec.raw){if(1!=o.nodeType){let e=document.createElement("span");e.appendChild(o),o=e}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class t8 extends t5{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class t7 extends t5{constructor(e,t,n,r,i){super(e,[],n,r),this.mark=t,this.spec=i}static create(e,t,n,r){let i=r.nodeViews[t.type.name],o=i&&i(t,r,n);return o&&o.dom||(o=ed.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new t7(e,t,o.dom,o.contentDOM||o.dom,o)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let e=this.parent;for(;!e.node;)e=e.parent;e.dirty<this.dirty&&(e.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=t7.create(this.parent,this.mark,!0,n),i=this.children,o=this.size;t<o&&(i=nu(i,t,o,n)),e>0&&(i=nu(i,0,e,n));for(let e=0;e<i.length;e++)i[e].parent=r;return r.children=i,r}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class t9 extends t5{constructor(e,t,n,r,i,o,s,l,a){super(e,[],i,o),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(e,t,n,r,i,o){let s=i.nodeViews[t.type.name],l,a=s&&s(t,i,()=>l?l.parent?l.parent.posBeforeChild(l):void 0:o,n,r),c=a&&a.dom,d=a&&a.contentDOM;if(t.isText)if(c){if(3!=c.nodeType)throw RangeError("Text must be rendered as a DOM text node")}else c=document.createTextNode(t.text);else if(!c){let e=ed.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs);({dom:c,contentDOM:d}=e)}d||t.isText||"BR"==c.nodeName||(c.hasAttribute("contenteditable")||(c.contentEditable="false"),t.type.spec.draggable&&(c.draggable=!0));let h=c;return(c=na(c,n,t),a)?l=new nr(e,t,n,r,c,d||null,h,a,i,o+1):t.isText?new nt(e,t,n,r,c,h,i):new t9(e,t,n,r,c,d||null,h,i,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>v.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&nc(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return+!this.node.isLeaf}updateChildren(e,t){let n=this.node.inlineContent,r=t,i=e.composing?this.localCompositionInfo(e,t):null,o=i&&i.pos>-1?i:null,s=i&&i.pos<0,l=new nh(this,o&&o.node,e);(function(e,t,n,r){let i=t.locals(e),o=0;if(0==i.length){for(let n=0;n<e.childCount;n++){let s=e.child(n);r(s,i,t.forChild(o,s),n),o+=s.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let d,h,p,u;for(;s<i.length&&i[s].to==o;){let e=i[s++];e.widget&&(d?(h||(h=[d])).push(e):d=e)}if(d)if(h){h.sort(np);for(let e=0;e<h.length;e++)n(h[e],c,!!a)}else n(d,c,!!a);if(a)u=-1,p=a,a=null;else if(c<e.childCount)u=c,p=e.child(c++);else break;for(let e=0;e<l.length;e++)l[e].to<=o&&l.splice(e--,1);for(;s<i.length&&i[s].from<=o&&i[s].to>o;)l.push(i[s++]);let f=o+p.nodeSize;if(p.isText){let e=f;s<i.length&&i[s].from<e&&(e=i[s].from);for(let t=0;t<l.length;t++)l[t].to<e&&(e=l[t].to);e<f&&(a=p.cut(e-o),p=p.cut(0,e-o),f=e,u=-1)}else for(;s<i.length&&i[s].to<f;)s++;let m=p.isInline&&!p.isLeaf?l.filter(e=>!e.inline):l.slice();r(p,m,t.forChild(o,p),u),o=f}})(this.node,this.innerDeco,(t,i,o)=>{t.spec.marks?l.syncToMarks(t.spec.marks,n,e):t.type.side>=0&&!o&&l.syncToMarks(i==this.node.childCount?S.none:this.node.child(i).marks,n,e),l.placeWidget(t,e,r)},(t,o,a,c)=>{let d;l.syncToMarks(t.marks,n,e),l.findNodeMatch(t,o,a,c)||s&&e.state.selection.from>r&&e.state.selection.to<r+t.nodeSize&&(d=l.findIndexWithChild(i.node))>-1&&l.updateNodeAt(t,o,a,d,e)||l.updateNextNode(t,o,a,e,c,r)||l.addNode(t,o,a,e,r),r+=t.nodeSize}),l.syncToMarks([],n,e),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||2==this.dirty)&&(o&&this.protectLocalComposition(e,o),function e(t,n,r){let i=t.firstChild,o=!1;for(let s=0;s<n.length;s++){let l=n[s],a=l.dom;if(a.parentNode==t){for(;a!=i;)i=nd(i),o=!0;i=i.nextSibling}else o=!0,t.insertBefore(a,i);if(l instanceof t7){let n=i?i.previousSibling:t.lastChild;e(l.contentDOM,l.children,r),i=n?n.nextSibling:t.firstChild}}for(;i;)i=nd(i),o=!0;o&&r.trackWrites==t&&(r.trackWrites=null)}(this.contentDOM,this.children,e),tL&&function(e){if("UL"==e.nodeName||"OL"==e.nodeName){let t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:r}=e.state.selection;if(!(e.state.selection instanceof e3)||n<t||r>t+this.node.content.size)return null;let i=e.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(!this.node.inlineContent)return{node:i,pos:-1,text:""};{let e=i.nodeValue,o=function(e,t,n,r){for(let i=0,o=0;i<e.childCount&&o<=r;){let s=e.child(i++),l=o;if(o+=s.nodeSize,!s.isText)continue;let a=s.text;for(;i<e.childCount;){let t=e.child(i++);if(o+=t.nodeSize,!t.isText)break;a+=t.text}if(o>=n){if(o>=r&&a.slice(r-t.length-l,r-l)==t)return r-t.length;let e=l<r?a.lastIndexOf(t,r-l-1):-1;if(e>=0&&e+t.length+l>=n)return l+e;if(n==r&&a.length>=r+t.length-l&&a.slice(r-l,r-l+t.length)==t)return r}}return -1}(this.node.content,e,n-t,r-t);return o<0?null:{node:i,pos:o,text:e}}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let o=new t8(this,i,t,r);e.input.compositionNodes.push(o),this.children=nu(this.children,n,n+r.length,e,o)}update(e,t,n,r){return 3!=this.dirty&&!!e.sameMarkup(this.node)&&(this.updateInner(e,t,n,r),!0)}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(nc(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=nl(this.dom,this.nodeDOM,ns(this.outerDeco,this.node,t),ns(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function ne(e,t,n,r,i){na(r,t,e);let o=new t9(void 0,e,t,n,r,r,r,i,0);return o.contentDOM&&o.updateChildren(i,0),o}class nt extends t9{constructor(e,t,n,r,i,o,s){super(e,t,n,r,i,null,o,s,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return 3!=this.dirty&&(0==this.dirty||!!this.inParent())&&!!e.sameMarkup(this.node)&&(this.updateOuterDeco(t),(0!=this.dirty||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let r=this.node.cut(e,t),i=document.createTextNode(r.text);return new nt(this.parent,r,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(0==e||t==this.nodeDOM.nodeValue.length)&&(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class nn extends t5{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class nr extends t9{constructor(e,t,n,r,i,o,s,l,a,c){super(e,t,n,r,i,o,s,a,c),this.spec=l}update(e,t,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,r),i}return(!!this.contentDOM||!!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}let ni=function(e){e&&(this.nodeName=e)};ni.prototype=Object.create(null);let no=[new ni];function ns(e,t,n){if(0==e.length)return no;let r=n?no[0]:new ni,i=[r];for(let o=0;o<e.length;o++){let s=e[o].type.attrs;if(s)for(let e in s.nodeName&&i.push(r=new ni(s.nodeName)),s){let o=s[e];null!=o&&(n&&1==i.length&&i.push(r=new ni(t.isInline?"span":"div")),"class"==e?r.class=(r.class?r.class+" ":"")+o:"style"==e?r.style=(r.style?r.style+";":"")+o:"nodeName"!=e&&(r[e]=o))}}return i}function nl(e,t,n,r){if(n==no&&r==no)return t;let i=t;for(let t=0;t<r.length;t++){let o=r[t],s=n[t];if(t){let t;s&&s.nodeName==o.nodeName&&i!=e&&(t=i.parentNode)&&t.nodeName.toLowerCase()==o.nodeName||((t=document.createElement(o.nodeName)).pmIsDeco=!0,t.appendChild(i),s=no[0]),i=t}!function(e,t,n){for(let r in t)"class"==r||"style"==r||"nodeName"==r||r in n||e.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=t[r]&&e.setAttribute(r,n[r]);if(t.class!=n.class){let r=t.class?t.class.split(" ").filter(Boolean):[],i=n.class?n.class.split(" ").filter(Boolean):[];for(let t=0;t<r.length;t++)-1==i.indexOf(r[t])&&e.classList.remove(r[t]);for(let t=0;t<i.length;t++)-1==r.indexOf(i[t])&&e.classList.add(i[t]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style){let n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,r;for(;r=n.exec(t.style);)e.style.removeProperty(r[1])}n.style&&(e.style.cssText+=n.style)}}(i,s||no[0],o)}return i}function na(e,t,n){return nl(e,e,no,ns(t,n,1!=e.nodeType))}function nc(e,t){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function nd(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}class nh{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(e,t){let n=t,r=n.children.length,i=e.childCount,o=new Map,s=[];n:for(;i>0;){let l;for(;;)if(r){let e=n.children[r-1];if(e instanceof t7)n=e,r=e.children.length;else{l=e,r--;break}}else if(n==t)break n;else r=n.parent.children.indexOf(n),n=n.parent;let a=l.node;if(a){if(a!=e.child(i-1))break;--i,o.set(l,i),s.push(l)}}return{index:i,matched:o,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,i=this.stack.length>>1,o=Math.min(i,e.length);for(;r<o&&(r==i-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&!1!==e[r].type.spec.spanning;)r++;for(;r<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let t=this.index;t<Math.min(this.index+3,this.top.children.length);t++){let n=this.top.children[t];if(n.matchesMark(e[i])&&!this.isLocked(n.dom)){r=t;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=t7.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,r){let i=-1,o;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))i=this.top.children.indexOf(o,this.index);else for(let r=this.index,o=Math.min(this.top.children.length,r+5);r<o;r++){let o=this.top.children[r];if(o.matchesNode(e,t,n)&&!this.preMatch.matched.has(o)){i=r;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,t,n,r,i){let o=this.top.children[r];return 3==o.dirty&&o.dom==o.contentDOM&&(o.dirty=2),!!o.update(e,t,n,i)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return -1;if(t==this.top.contentDOM){let t=e.pmViewDesc;if(t){for(let e=this.index;e<this.top.children.length;e++)if(this.top.children[e]==t)return e}return -1}e=t}}updateNextNode(e,t,n,r,i,o){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof t9){let a=this.preMatch.matched.get(l);if(null!=a&&a!=i)return!1;let c=l.dom,d,h=this.isLocked(c)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&3!=l.dirty&&nc(t,l.outerDeco));if(!h&&l.update(e,t,n,r))return this.destroyBetween(this.index,s),l.dom!=c&&(this.changed=!0),this.index++,!0;if(!h&&(d=this.recreateWrapper(l,e,t,n,r,o)))return this.destroyBetween(this.index,s),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=2,d.updateChildren(r,o+1),d.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,i,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!nc(n,e.outerDeco)||!r.eq(e.innerDeco))return null;let s=t9.create(this.top,t,n,r,i,o);if(s.contentDOM)for(let t of(s.children=e.children,e.children=[],s.children))t.parent=s;return e.destroy(),s}addNode(e,t,n,r,i){let o=t9.create(this.top,e,t,n,r,i);o.contentDOM&&o.updateChildren(r,i+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(r&&r.matchesWidget(e)&&(e==r.widget||!r.widget.type.toDOM.parentNode))this.index++;else{let r=new t6(this.top,e,t,n);this.top.children.splice(this.index++,0,r),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof t7;)e=(t=e).children[t.children.length-1];(!e||!(e instanceof nt)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((tj||tI)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let r=new nn(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function np(e,t){return e.type.side-t.type.side}function nu(e,t,n,r,i){let o=[];for(let s=0,l=0;s<e.length;s++){let a=e[s],c=l,d=l+=a.size;c>=n||d<=t?o.push(a):(c<t&&o.push(a.slice(0,t-c,r)),i&&(o.push(i),i=void 0),d>n&&o.push(a.slice(n-c,a.size,r)))}return o}function nf(e,t=null){let n=e.domSelectionRange(),r=e.state.doc;if(!n.focusNode)return null;let i=e.docView.nearestDesc(n.focusNode),o=i&&0==i.size,s=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;let l=r.resolve(s),a,c;if(tk(n)){for(a=s;i&&!i.node;)i=i.parent;let e=i.node;if(i&&e.isAtom&&e5.isSelectable(e)&&i.parent&&!(e.isInline&&function(e,t,n){for(let r=0==t,i=t==tv(e);r||i;){if(e==n)return!0;let t=th(e);if(!(e=e.parentNode))return!1;r=r&&0==t,i=i&&t==tv(e)}}(n.focusNode,n.focusOffset,i.dom))){let e=i.posBefore;c=new e5(s==e?l:r.resolve(e))}}else{if(n instanceof e.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let t=s,i=s;for(let r=0;r<n.rangeCount;r++){let o=n.getRangeAt(r);t=Math.min(t,e.docView.posFromDOM(o.startContainer,o.startOffset,1)),i=Math.max(i,e.docView.posFromDOM(o.endContainer,o.endOffset,-1))}if(t<0)return null;[a,s]=i==e.state.selection.anchor?[i,t]:[t,i],l=r.resolve(s)}else a=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(a<0)return null}let d=r.resolve(a);if(!c){let n="pointer"==t||e.state.selection.head<l.pos&&!o?1:-1;c=nS(e,d,l,n)}return c}function nm(e){return e.editable?e.hasFocus():nC(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function ng(e,t=!1){let n=e.state.selection;if(nk(e,n),nm(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&tI){let t=e.domSelectionRange(),n=e.domObserver.currentSelection;if(t.anchorNode&&n.anchorNode&&tg(t.anchorNode,t.anchorOffset,n.anchorNode,n.anchorOffset)){e.input.mouseDown.delayedSelectionSync=!0,e.domObserver.setCurSelection();return}}if(e.domObserver.disconnectSelection(),e.cursorWrapper)!function(e){let t=e.domSelection(),n=document.createRange();if(!t)return;let r=e.cursorWrapper.dom,i="IMG"==r.nodeName;i?n.setStart(r.parentNode,th(r)+1):n.setStart(r,0),n.collapse(!0),t.removeAllRanges(),t.addRange(n),!i&&!e.state.selection.visible&&tT&&tE<=11&&(r.disabled=!0,r.disabled=!1)}(e);else{var r;let i,o,s,l,{anchor:a,head:c}=n,d,h;ny&&!(n instanceof e3)&&(n.$from.parent.inlineContent||(d=nb(e,n.from)),n.empty||n.$from.parent.inlineContent||(h=nb(e,n.to))),e.docView.setSelection(a,c,e,t),ny&&(d&&nw(d),h&&nw(h)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&((i=(r=e).dom.ownerDocument).removeEventListener("selectionchange",r.input.hideSelectionGuard),s=(o=r.domSelectionRange()).anchorNode,l=o.anchorOffset,i.addEventListener("selectionchange",r.input.hideSelectionGuard=()=>{(o.anchorNode!=s||o.anchorOffset!=l)&&(i.removeEventListener("selectionchange",r.input.hideSelectionGuard),setTimeout(()=>{(!nm(r)||r.state.selection.visible)&&r.dom.classList.remove("ProseMirror-hideselection")},20))})))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}let ny=tj||tI&&tP<63;function nb(e,t){let{node:n,offset:r}=e.docView.domFromPos(t,0),i=r<n.childNodes.length?n.childNodes[r]:null,o=r?n.childNodes[r-1]:null;if(tj&&i&&"false"==i.contentEditable)return nv(i);if((!i||"false"==i.contentEditable)&&(!o||"false"==o.contentEditable)){if(i)return nv(i);else if(o)return nv(o)}}function nv(e){return e.contentEditable="true",tj&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function nw(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function nk(e,t){if(t instanceof e5){let n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(nx(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else nx(e)}function nx(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function nS(e,t,n,r){return e.someProp("createSelectionBetween",r=>r(e,t,n))||e3.between(t,n,r)}function nM(e){return(!e.editable||!!e.hasFocus())&&nC(e)}function nC(e){let t=e.domSelectionRange();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(e){return!1}}function nO(e,t){let{$anchor:n,$head:r}=e.selection,i=t>0?n.max(r):n.min(r),o=i.parent.inlineContent?i.depth?e.doc.resolve(t>0?i.after():i.before()):null:i;return o&&eZ.findFrom(o,t)}function nN(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function nA(e,t,n){let r=e.state.selection;if(r instanceof e3){if(n.indexOf("s")>-1){let{$head:n}=r,i=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let o=e.state.doc.resolve(n.pos+i.nodeSize*(t<0?-1:1));return nN(e,new e3(r.$anchor,o))}else if(!r.empty)return!1;else if(e.endOfTextblock(t>0?"forward":"backward")){let n=nO(e.state,t);return!!n&&n instanceof e5&&nN(e,n)}else if(!(tz&&n.indexOf("m")>-1)){let n=r.$head,i=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter,o;if(!i||i.isText)return!1;let s=t<0?n.pos-i.nodeSize:n.pos;return!!(i.isAtom||(o=e.docView.descAt(s))&&!o.contentDOM)&&(e5.isSelectable(i)?nN(e,new e5(t<0?e.state.doc.resolve(n.pos-i.nodeSize):n)):!!tV&&nN(e,new e3(e.state.doc.resolve(t<0?s:s+i.nodeSize))))}}else{if(r instanceof e5&&r.node.isInline)return nN(e,new e3(t>0?r.$to:r.$from));let n=nO(e.state,t);return!!n&&nN(e,n)}}function nT(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function nE(e,t){let n=e.pmViewDesc;return n&&0==n.size&&(t<0||e.nextSibling||"BR"!=e.nodeName)}function nD(e,t){return t<0?function(e){let t=e.domSelectionRange(),n=t.focusNode,r=t.focusOffset;if(!n)return;let i,o,s=!1;for(tD&&1==n.nodeType&&r<nT(n)&&nE(n.childNodes[r],-1)&&(s=!0);;)if(r>0)if(1!=n.nodeType)break;else{let e=n.childNodes[r-1];if(nE(e,-1))i=n,o=--r;else if(3==e.nodeType)r=(n=e).nodeValue.length;else break}else if(nR(n))break;else{let t=n.previousSibling;for(;t&&nE(t,-1);)i=n.parentNode,o=th(t),t=t.previousSibling;if(t)r=nT(n=t);else{if((n=n.parentNode)==e.dom)break;r=0}}s?nI(e,n,r):i&&nI(e,i,o)}(e):function(e){let t,n,r=e.domSelectionRange(),i=r.focusNode,o=r.focusOffset;if(!i)return;let s=nT(i);for(;;)if(o<s){if(1!=i.nodeType)break;if(nE(i.childNodes[o],1))t=i,n=++o;else break}else if(nR(i))break;else{let r=i.nextSibling;for(;r&&nE(r,1);)t=r.parentNode,n=th(r)+1,r=r.nextSibling;if(r)o=0,s=nT(i=r);else{if((i=i.parentNode)==e.dom)break;o=s=0}}t&&nI(e,t,n)}(e)}function nR(e){let t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function nI(e,t,n){if(3!=t.nodeType){let e,r;(r=function(e,t){for(;e&&t==e.childNodes.length&&!tw(e);)t=th(e)+1,e=e.parentNode;for(;e&&t<e.childNodes.length;){let n=e.childNodes[t];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=n,t=0}}(t,n))?(t=r,n=0):(e=function(e,t){for(;e&&!t&&!tw(e);)t=th(e),e=e.parentNode;for(;e&&t;){let n=e.childNodes[t-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=(e=n).childNodes.length}}(t,n))&&(t=e,n=e.nodeValue.length)}let r=e.domSelection();if(!r)return;if(tk(r)){let e=document.createRange();e.setEnd(t,n),e.setStart(t,n),r.removeAllRanges(),r.addRange(e)}else r.extend&&r.extend(t,n);e.domObserver.setCurSelection();let{state:i}=e;setTimeout(()=>{e.state==i&&ng(e)},50)}function nP(e,t){let n=e.state.doc.resolve(t);if(!(tI||tB)&&n.parent.inlineContent){let r=e.coordsAtPos(t);if(t>n.start()){let n=e.coordsAtPos(t-1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(t<n.end()){let n=e.coordsAtPos(t+1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(e.dom).direction?"rtl":"ltr"}function nj(e,t,n){let r=e.state.selection;if(r instanceof e3&&!r.empty||n.indexOf("s")>-1||tz&&n.indexOf("m")>-1)return!1;let{$from:i,$to:o}=r;if(!i.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){let n=nO(e.state,t);if(n&&n instanceof e5)return nN(e,n)}if(!i.parent.inlineContent){let n=t<0?i:o,s=r instanceof e8?eZ.near(n,t):eZ.findFrom(n,t);return!!s&&nN(e,s)}return!1}function nL(e,t){if(!(e.state.selection instanceof e3))return!0;let{$head:n,$anchor:r,empty:i}=e.state.selection;if(!n.sameParent(r))return!0;if(!i)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;let o=!n.textOffset&&(t<0?n.nodeBefore:n.nodeAfter);if(o&&!o.isText){let r=e.state.tr;return t<0?r.delete(n.pos-o.nodeSize,n.pos):r.delete(n.pos,n.pos+o.nodeSize),e.dispatch(r),!0}return!1}function nz(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function nB(e,t){e.someProp("transformCopied",n=>{t=n(t,e)});let n=[],{content:r,openStart:i,openEnd:o}=t;for(;i>1&&o>1&&1==r.childCount&&1==r.firstChild.childCount;){i--,o--;let e=r.firstChild;n.push(e.type.name,e.attrs!=e.type.defaultAttrs?e.attrs:null),r=e.content}let s=e.someProp("clipboardSerializer")||ed.fromSchema(e.state.schema),l=nK(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c=a.firstChild,d,h=0;for(;c&&1==c.nodeType&&(d=nq[c.nodeName.toLowerCase()]);){for(let e=d.length-1;e>=0;e--){let t=l.createElement(d[e]);for(;a.firstChild;)t.appendChild(a.firstChild);a.appendChild(t),h++}c=a.firstChild}return c&&1==c.nodeType&&c.setAttribute("data-pm-slice",`${i} ${o}${h?` -${h}`:""} ${JSON.stringify(n)}`),{dom:a,text:e.someProp("clipboardTextSerializer",n=>n(t,e))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function n$(e,t,n,r,i){let o,s,l=i.parent.type.spec.code;if(!n&&!t)return null;let a=t&&(r||l||!n);if(a){if(e.someProp("transformPastedText",n=>{t=n(t,l||r,e)}),l)return t?new C(v.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0):C.empty;let n=e.someProp("clipboardTextParser",n=>n(t,i,r,e));if(n)s=n;else{let n=i.marks(),{schema:r}=e.state,s=ed.fromSchema(r);o=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach(e=>{let t=o.appendChild(document.createElement("p"));e&&t.appendChild(s.serializeNode(r.text(e,n)))})}}else e.someProp("transformPastedHTML",t=>{n=t(n,e)}),o=function(e){var t;let n,r=/^(\s*<meta [^>]*>)*/.exec(e);r&&(e=e.slice(r[0].length));let i=nK().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(e),s;if((s=o&&nq[o[1].toLowerCase()])&&(e=s.map(e=>"<"+e+">").join("")+e+s.map(e=>"</"+e+">").reverse().join("")),i.innerHTML=(t=e,(n=window.trustedTypes)?(nJ||(nJ=n.defaultPolicy||n.createPolicy("ProseMirrorClipboard",{createHTML:e=>e})),nJ.createHTML(t)):t),s)for(let e=0;e<s.length;e++)i=i.querySelector(s[e])||i;return i}(n),tV&&function(e){let t=e.querySelectorAll(tI?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<t.length;n++){let r=t[n];1==r.childNodes.length&&"\xa0"==r.textContent&&r.parentNode&&r.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),r)}}(o);let c=o&&o.querySelector("[data-pm-slice]"),d=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(d&&d[3])for(let e=+d[3];e>0;e--){let e=o.firstChild;for(;e&&1!=e.nodeType;)e=e.nextSibling;if(!e)break;o=e}if(s||(s=(e.someProp("clipboardParser")||e.someProp("domParser")||et.fromSchema(e.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||d),context:i,ruleFromNode:e=>"BR"!=e.nodeName||e.nextSibling||!e.parentNode||nV.test(e.parentNode.nodeName)?null:{ignore:!0}})),d)s=function(e,t){if(!e.size)return e;let n=e.content.firstChild.type.schema,r;try{r=JSON.parse(t)}catch(t){return e}let{content:i,openStart:o,openEnd:s}=e;for(let e=r.length-2;e>=0;e-=2){let t=n.nodes[r[e]];if(!t||t.hasRequiredAttrs())break;i=v.from(t.create(r[e+1],i)),o++,s++}return new C(i,o,s)}(n_(s,+d[1],+d[2]),d[4]);else if((s=C.maxOpen(function(e,t){if(e.childCount<2)return e;for(let n=t.depth;n>=0;n--){let r=t.node(n).contentMatchAt(t.index(n)),i,o=[];if(e.forEach(e=>{if(!o)return;let t=r.findWrapping(e.type),n;if(!t)return o=null;if(n=o.length&&i.length&&function e(t,n,r,i,o){if(o<t.length&&o<n.length&&t[o]==n[o]){let s=e(t,n,r,i.lastChild,o+1);if(s)return i.copy(i.content.replaceChild(i.childCount-1,s));if(i.contentMatchAt(i.childCount).matchType(o==t.length-1?r.type:t[o+1]))return i.copy(i.content.append(v.from(nF(r,t,o+1))))}}(t,i,e,o[o.length-1],0))o[o.length-1]=n;else{o.length&&(o[o.length-1]=function e(t,n){if(0==n)return t;let r=t.content.replaceChild(t.childCount-1,e(t.lastChild,n-1)),i=t.contentMatchAt(t.childCount).fillBefore(v.empty,!0);return t.copy(r.append(i))}(o[o.length-1],i.length));let n=nF(e,t);o.push(n),r=r.matchType(n.type),i=t}}),o)return v.from(o)}return e}(s.content,i),!0)).openStart||s.openEnd){let e=0,t=0;for(let t=s.content.firstChild;e<s.openStart&&!t.type.spec.isolating;e++,t=t.firstChild);for(let e=s.content.lastChild;t<s.openEnd&&!e.type.spec.isolating;t++,e=e.lastChild);s=n_(s,e,t)}return e.someProp("transformPasted",t=>{s=t(s,e)}),s}let nV=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function nF(e,t,n=0){for(let r=t.length-1;r>=n;r--)e=t[r].create(null,v.from(e));return e}function nH(e,t,n,r,i,o){let s=t<0?e.firstChild:e.lastChild,l=s.content;return e.childCount>1&&(o=0),i<r-1&&(l=nH(l,t,n,r,i+1,o)),i>=n&&(l=t<0?s.contentMatchAt(0).fillBefore(l,o<=i).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(v.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,s.copy(l))}function n_(e,t,n){return t<e.openStart&&(e=new C(nH(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new C(nH(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}let nq={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},nW=null;function nK(){return nW||(nW=document.implementation.createHTMLDocument("title"))}let nJ=null,nU={},nG={},nY={touchstart:!0,touchmove:!0};class nQ{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function nX(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function nZ(e){e.someProp("handleDOMEvents",t=>{for(let n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=t=>n0(e,t))})}function n0(e,t){return e.someProp("handleDOMEvents",n=>{let r=n[t.type];return!!r&&(r(e,t)||t.defaultPrevented)})}function n1(e){return{left:e.clientX,top:e.clientY}}function n2(e,t,n,r,i){if(-1==r)return!1;let o=e.state.doc.resolve(r);for(let r=o.depth+1;r>0;r--)if(e.someProp(t,t=>r>o.depth?t(e,n,o.nodeAfter,o.before(r),i,!0):t(e,n,o.node(r),o.before(r),i,!1)))return!0;return!1}function n3(e,t,n){if(e.focused||e.focus(),e.state.selection.eq(t))return;let r=e.state.tr.setSelection(t);"pointer"==n&&r.setMeta("pointer",!0),e.dispatch(r)}nG.keydown=(e,t)=>{if((e.input.shiftKey=16==t.keyCode||t.shiftKey,!n6(e,t))&&(e.input.lastKeyCode=t.keyCode,e.input.lastKeyCodeTime=Date.now(),!t$||!tI||13!=t.keyCode))if(229!=t.keyCode&&e.domObserver.forceFlush(),!tL||13!=t.keyCode||t.ctrlKey||t.altKey||t.metaKey)e.someProp("handleKeyDown",n=>n(e,t))||function(e,t){let n,r=t.keyCode,i=(n="",t.ctrlKey&&(n+="c"),t.metaKey&&(n+="m"),t.altKey&&(n+="a"),t.shiftKey&&(n+="s"),n);if(8==r||tz&&72==r&&"c"==i)return nL(e,-1)||nD(e,-1);if(46==r&&!t.shiftKey||tz&&68==r&&"c"==i)return nL(e,1)||nD(e,1);if(13==r||27==r)return!0;if(37==r||tz&&66==r&&"c"==i){let t=37==r?"ltr"==nP(e,e.state.selection.from)?-1:1:-1;return nA(e,t,i)||nD(e,t)}if(39==r||tz&&70==r&&"c"==i){let t=39==r?"ltr"==nP(e,e.state.selection.from)?1:-1:1;return nA(e,t,i)||nD(e,t)}else if(38==r||tz&&80==r&&"c"==i)return nj(e,-1,i)||nD(e,-1);else if(40==r||tz&&78==r&&"c"==i)return function(e){if(!tj||e.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:n}=e.domSelectionRange();if(t&&1==t.nodeType&&0==n&&t.firstChild&&"false"==t.firstChild.contentEditable){let n=t.firstChild;nz(e,n,"true"),setTimeout(()=>nz(e,n,"false"),20)}return!1}(e)||nj(e,1,i)||nD(e,1);else if(i==(tz?"m":"c")&&(66==r||73==r||89==r||90==r))return!0;return!1}(e,t)?t.preventDefault():nX(e,"key");else{let t=Date.now();e.input.lastIOSEnter=t,e.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{e.input.lastIOSEnter==t&&(e.someProp("handleKeyDown",t=>t(e,tx(13,"Enter"))),e.input.lastIOSEnter=0)},200)}},nG.keyup=(e,t)=>{16==t.keyCode&&(e.input.shiftKey=!1)},nG.keypress=(e,t)=>{if(n6(e,t)||!t.charCode||t.ctrlKey&&!t.altKey||tz&&t.metaKey)return;if(e.someProp("handleKeyPress",n=>n(e,t)))return void t.preventDefault();let n=e.state.selection;if(!(n instanceof e3)||!n.$from.sameParent(n.$to)){let r=String.fromCharCode(t.charCode),i=()=>e.state.tr.insertText(r).scrollIntoView();/[\r\n]/.test(r)||e.someProp("handleTextInput",t=>t(e,n.$from.pos,n.$to.pos,r,i))||e.dispatch(i()),t.preventDefault()}};let n4=tz?"metaKey":"ctrlKey";nU.mousedown=(e,t)=>{e.input.shiftKey=t.shiftKey;let n=re(e),r=Date.now(),i="singleClick";r-e.input.lastClick.time<500&&function(e,t){let n=t.x-e.clientX,r=t.y-e.clientY;return n*n+r*r<100}(t,e.input.lastClick)&&!t[n4]&&e.input.lastClick.button==t.button&&("singleClick"==e.input.lastClick.type?i="doubleClick":"doubleClick"==e.input.lastClick.type&&(i="tripleClick")),e.input.lastClick={time:r,x:t.clientX,y:t.clientY,type:i,button:t.button};let o=e.posAtCoords(n1(t));o&&("singleClick"==i?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new n5(e,o,t,!!n)):("doubleClick"==i?function(e,t,n,r){return n2(e,"handleDoubleClickOn",t,n,r)||e.someProp("handleDoubleClick",n=>n(e,t,r))}:function(e,t,n,r){return n2(e,"handleTripleClickOn",t,n,r)||e.someProp("handleTripleClick",n=>n(e,t,r))||function(e,t,n){if(0!=n.button)return!1;let r=e.state.doc;if(-1==t)return!!r.inlineContent&&(n3(e,e3.create(r,0,r.content.size),"pointer"),!0);let i=r.resolve(t);for(let t=i.depth+1;t>0;t--){let n=t>i.depth?i.nodeAfter:i.node(t),o=i.before(t);if(n.inlineContent)n3(e,e3.create(r,o+1,o+1+n.content.size),"pointer");else{if(!e5.isSelectable(n))continue;n3(e,e5.create(r,o),"pointer")}return!0}}(e,n,r)})(e,o.pos,o.inside,t)?t.preventDefault():nX(e,"pointer"))};class n5{constructor(e,t,n,r){let i,o;if(this.view=e,this.pos=t,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[n4],this.allowDefault=n.shiftKey,t.inside>-1)i=e.state.doc.nodeAt(t.inside),o=t.inside;else{let n=e.state.doc.resolve(t.pos);i=n.parent,o=n.depth?n.before():0}let s=r?null:n.target,l=s?e.docView.nearestDesc(s,!0):null;this.target=l&&1==l.dom.nodeType?l.dom:null;let{selection:a}=e.state;(0==n.button&&i.type.spec.draggable&&!1!==i.type.spec.selectable||a instanceof e5&&a.from<=o&&a.to>o)&&(this.mightDrag={node:i,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&tD&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),nX(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>ng(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;if(this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(n1(e))),this.updateAllowDefault(e),this.allowDefault||!t)nX(this.view,"pointer");else{var n,r,i,o;(n=this.view,r=t.pos,i=t.inside,o=this.selectNode,n2(n,"handleClickOn",r,i,e)||n.someProp("handleClick",t=>t(n,r,e))||(o?function(e,t){if(-1==t)return!1;let n=e.state.selection,r,i;n instanceof e5&&(r=n.node);let o=e.state.doc.resolve(t);for(let e=o.depth+1;e>0;e--){let t=e>o.depth?o.nodeAfter:o.node(e);if(e5.isSelectable(t)){i=r&&n.$from.depth>0&&e>=n.$from.depth&&o.before(n.$from.depth+1)==n.$from.pos?o.before(n.$from.depth):o.before(e);break}}return null!=i&&(n3(e,e5.create(e.state.doc,i),"pointer"),!0)}(n,i):function(e,t){if(-1==t)return!1;let n=e.state.doc.resolve(t),r=n.nodeAfter;return!!(r&&r.isAtom&&e5.isSelectable(r))&&(n3(e,new e5(n),"pointer"),!0)}(n,i)))?e.preventDefault():0==e.button&&(this.flushed||tj&&this.mightDrag&&!this.mightDrag.node.isAtom||tI&&!this.view.state.selection.visible&&2>=Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to)))?(n3(this.view,eZ.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):nX(this.view,"pointer")}}move(e){this.updateAllowDefault(e),nX(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function n6(e,t){return!!e.composing||!!(tj&&500>Math.abs(t.timeStamp-e.input.compositionEndedAt))&&(e.input.compositionEndedAt=-2e8,!0)}nU.touchstart=e=>{e.input.lastTouch=Date.now(),re(e),nX(e,"pointer")},nU.touchmove=e=>{e.input.lastTouch=Date.now(),nX(e,"pointer")},nU.contextmenu=e=>re(e);let n8=t$?5e3:-1;function n7(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout(()=>re(e),t))}function n9(e){let t;for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=((t=document.createEvent("Event")).initEvent("event",!0,!0),t.timeStamp));e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function re(e,t=!1){if(!t$||!(e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),n9(e),t||e.docView&&e.docView.dirty){let n=nf(e),r=e.state.selection;return n&&!n.eq(r)?e.dispatch(e.state.tr.setSelection(n)):(e.markCursor||t)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?e.dispatch(e.state.tr.deleteSelection()):e.updateState(e.state),!0}return!1}}nG.compositionstart=nG.compositionupdate=e=>{if(!e.composing){e.domObserver.flush();let{state:t}=e,n=t.selection.$to;if(t.selection instanceof e3&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some(e=>!1===e.type.spec.inclusive)))e.markCursor=e.state.storedMarks||n.marks(),re(e,!0),e.markCursor=null;else if(re(e,!t.selection.empty),tD&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let t=e.domSelectionRange();for(let n=t.focusNode,r=t.focusOffset;n&&1==n.nodeType&&0!=r;){let t=r<0?n.lastChild:n.childNodes[r-1];if(!t)break;if(3==t.nodeType){let n=e.domSelection();n&&n.collapse(t,t.nodeValue.length);break}n=t,r=-1}}e.input.composing=!0}n7(e,n8)},nG.compositionend=(e,t)=>{e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,e.input.compositionPendingChanges=e.domObserver.pendingRecords().length?e.input.compositionID:0,e.input.compositionNode=null,e.input.compositionPendingChanges&&Promise.resolve().then(()=>e.domObserver.flush()),e.input.compositionID++,n7(e,20))};let rt=tT&&tE<15||tL&&tF<604;function rn(e,t,n,r,i){let o=n$(e,t,n,r,e.state.selection.$from);if(e.someProp("handlePaste",t=>t(e,i,o||C.empty)))return!0;if(!o)return!1;let s=0==o.openStart&&0==o.openEnd&&1==o.content.childCount?o.content.firstChild:null,l=s?e.state.tr.replaceSelectionWith(s,r):e.state.tr.replaceSelection(o);return e.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function rr(e){let t=e.getData("text/plain")||e.getData("Text");if(t)return t;let n=e.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}nU.copy=nG.cut=(e,t)=>{let n=e.state.selection,r="cut"==t.type;if(n.empty)return;let i=rt?null:t.clipboardData,{dom:o,text:s}=nB(e,n.content());i?(t.preventDefault(),i.clearData(),i.setData("text/html",o.innerHTML),i.setData("text/plain",s)):function(e,t){if(!e.dom.parentNode)return;let n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(t),e.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n),e.focus()},50)}(e,o),r&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},nG.paste=(e,t)=>{if(e.composing&&!t$)return;let n=rt?null:t.clipboardData,r=e.input.shiftKey&&45!=e.input.lastKeyCode;n&&rn(e,rr(n),n.getData("text/html"),r,t)?t.preventDefault():function(e,t){if(!e.dom.parentNode)return;let n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,r=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=e.input.shiftKey&&45!=e.input.lastKeyCode;setTimeout(()=>{e.focus(),r.parentNode&&r.parentNode.removeChild(r),n?rn(e,r.value,null,i,t):rn(e,r.textContent,r.innerHTML,i,t)},50)}(e,t)};class ri{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}let ro=tz?"altKey":"ctrlKey";function rs(e,t){let n=e.someProp("dragCopies",e=>!e(t));return null!=n?n:!t[ro]}for(let e in nU.dragstart=(e,t)=>{let n,r=e.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=e.state.selection,o=i.empty?null:e.posAtCoords(n1(t));if(o&&o.pos>=i.from&&o.pos<=(i instanceof e5?i.to-1:i.to));else if(r&&r.mightDrag)n=e5.create(e.state.doc,r.mightDrag.pos);else if(t.target&&1==t.target.nodeType){let r=e.docView.nearestDesc(t.target,!0);r&&r.node.type.spec.draggable&&r!=e.docView&&(n=e5.create(e.state.doc,r.posBefore))}let s=(n||e.state.selection).content(),{dom:l,text:a,slice:c}=nB(e,s);t.dataTransfer.files.length&&tI&&!(tP>120)||t.dataTransfer.clearData(),t.dataTransfer.setData(rt?"Text":"text/html",l.innerHTML),t.dataTransfer.effectAllowed="copyMove",rt||t.dataTransfer.setData("text/plain",a),e.dragging=new ri(c,rs(e,t),n)},nU.dragend=e=>{let t=e.dragging;window.setTimeout(()=>{e.dragging==t&&(e.dragging=null)},50)},nG.dragover=nG.dragenter=(e,t)=>t.preventDefault(),nG.drop=(e,t)=>{let n=e.dragging;if(e.dragging=null,!t.dataTransfer)return;let r=e.posAtCoords(n1(t));if(!r)return;let i=e.state.doc.resolve(r.pos),o=n&&n.slice;o?e.someProp("transformPasted",t=>{o=t(o,e)}):o=n$(e,rr(t.dataTransfer),rt?null:t.dataTransfer.getData("text/html"),!1,i);let s=!!(n&&rs(e,t));if(e.someProp("handleDrop",n=>n(e,t,o||C.empty,s)))return void t.preventDefault();if(!o)return;t.preventDefault();let l=o?e$(e.state.doc,i.pos,o):i.pos;null==l&&(l=i.pos);let a=e.state.tr;if(s){let{node:e}=n;e?e.replace(a):a.deleteSelection()}let c=a.mapping.map(l),d=0==o.openStart&&0==o.openEnd&&1==o.content.childCount,h=a.doc;if(d?a.replaceRangeWith(c,c,o.content.firstChild):a.replaceRange(c,c,o),a.doc.eq(h))return;let p=a.doc.resolve(c);if(d&&e5.isSelectable(o.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(o.content.firstChild))a.setSelection(new e5(p));else{let t=a.mapping.map(l);a.mapping.maps[a.mapping.maps.length-1].forEach((e,n,r,i)=>t=i),a.setSelection(nS(e,p,a.doc.resolve(t)))}e.focus(),e.dispatch(a.setMeta("uiEvent","drop"))},nU.focus=e=>{e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout(()=>{e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelectionRange())&&ng(e)},20))},nU.blur=(e,t)=>{e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),t.relatedTarget&&e.dom.contains(t.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},nU.beforeinput=(e,t)=>{if(tI&&t$&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();let{domChangeCount:t}=e.input;setTimeout(()=>{if(e.input.domChangeCount!=t||(e.dom.blur(),e.focus(),e.someProp("handleKeyDown",t=>t(e,tx(8,"Backspace")))))return;let{$cursor:n}=e.state.selection;n&&n.pos>0&&e.dispatch(e.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}},nG)nU[e]=nG[e];function rl(e,t){if(e==t)return!0;for(let n in e)if(e[n]!==t[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}class ra{constructor(e,t){this.toDOM=e,this.spec=t||ru,this.side=this.spec.side||0}map(e,t,n,r){let{pos:i,deleted:o}=e.mapResult(t.from+r,this.side<0?-1:1);return o?null:new rh(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof ra&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&rl(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class rc{constructor(e,t){this.attrs=e,this.spec=t||ru}map(e,t,n,r){let i=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,o=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return i>=o?null:new rh(i,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof rc&&rl(this.attrs,e.attrs)&&rl(this.spec,e.spec)}static is(e){return e.type instanceof rc}destroy(){}}class rd{constructor(e,t){this.attrs=e,this.spec=t||ru}map(e,t,n,r){let i=e.mapResult(t.from+r,1);if(i.deleted)return null;let o=e.mapResult(t.to+r,-1);return o.deleted||o.pos<=i.pos?null:new rh(i.pos-n,o.pos-n,this)}valid(e,t){let{index:n,offset:r}=e.content.findIndex(t.from),i;return r==t.from&&!(i=e.child(n)).isText&&r+i.nodeSize==t.to}eq(e){return this==e||e instanceof rd&&rl(this.attrs,e.attrs)&&rl(this.spec,e.spec)}destroy(){}}class rh{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new rh(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new rh(e,e,new ra(t,n))}static inline(e,t,n,r){return new rh(e,t,new rc(n,r))}static node(e,t,n,r){return new rh(e,t,new rd(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof rc}get widget(){return this.type instanceof ra}}let rp=[],ru={};class rf{constructor(e,t){this.local=e.length?e:rp,this.children=t.length?t:rp}static create(e,t){return t.length?rw(t,e,0,ru):rm}find(e,t,n){let r=[];return this.findInner(null==e?0:e,null==t?1e9:t,r,0,n),r}findInner(e,t,n,r,i){for(let o=0;o<this.local.length;o++){let s=this.local[o];s.from<=t&&s.to>=e&&(!i||i(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let s=this.children[o]+1;this.children[o+2].findInner(e-s,t-s,n,r+s,i)}}map(e,t,n){return this==rm||0==e.maps.length?this:this.mapInner(e,t,0,0,n||ru)}mapInner(e,t,n,r,i){let o;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(e,n,r);l&&l.type.valid(t,l)?(o||(o=[])).push(l):i.onRemove&&i.onRemove(this.local[s].spec)}return this.children.length?function(e,t,n,r,i,o,s){let l=e.slice();for(let e=0,t=o;e<n.maps.length;e++){let r=0;n.maps[e].forEach((e,n,i,o)=>{let s=o-i-(n-e);for(let i=0;i<l.length;i+=3){let o=l[i+1];if(o<0||e>o+t-r)continue;let a=l[i]+t-r;n>=a?l[i+1]=e<=a?-2:-1:e>=t&&s&&(l[i]+=s,l[i+1]+=s)}r+=s}),t=n.maps[e].map(t,-1)}let a=!1;for(let t=0;t<l.length;t+=3)if(l[t+1]<0){if(-2==l[t+1]){a=!0,l[t+1]=-1;continue}let c=n.map(e[t]+o),d=c-i;if(d<0||d>=r.content.size){a=!0;continue}let h=n.map(e[t+1]+o,-1)-i,{index:p,offset:u}=r.content.findIndex(d),f=r.maybeChild(p);if(f&&u==d&&u+f.nodeSize==h){let r=l[t+2].mapInner(n,f,c+1,e[t]+o+1,s);r!=rm?(l[t]=d,l[t+1]=h,l[t+2]=r):(l[t+1]=-2,a=!0)}else a=!0}if(a){let a=rw(function(e,t,n,r,i,o,s){for(let l=0;l<e.length;l+=3)-1==e[l+1]&&function e(t,o){for(let e=0;e<t.local.length;e++){let l=t.local[e].map(r,i,o);l?n.push(l):s.onRemove&&s.onRemove(t.local[e].spec)}for(let n=0;n<t.children.length;n+=3)e(t.children[n+2],t.children[n]+o+1)}(e[l+2],t[l]+o+1);return n}(l,e,t,n,i,o,s),r,0,s);t=a.local;for(let e=0;e<l.length;e+=3)l[e+1]<0&&(l.splice(e,3),e-=3);for(let e=0,t=0;e<a.children.length;e+=3){let n=a.children[e];for(;t<l.length&&l[t]<n;)t+=3;l.splice(t,0,a.children[e],a.children[e+1],a.children[e+2])}}return new rf(t.sort(rk),l)}(this.children,o||[],e,t,n,r,i):o?new rf(o.sort(rk),rp):rm}add(e,t){return t.length?this==rm?rf.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,i=0;e.forEach((e,o)=>{let s=o+n,l;if(l=rb(t,e,s)){for(r||(r=this.children.slice());i<r.length&&r[i]<o;)i+=3;r[i]==o?r[i+2]=r[i+2].addInner(e,l,s+1):r.splice(i,0,o,o+e.nodeSize,rw(l,e,s+1,ru)),i+=3}});let o=ry(i?rv(t):t,-n);for(let t=0;t<o.length;t++)o[t].type.valid(e,o[t])||o.splice(t--,1);return new rf(o.length?this.local.concat(o).sort(rk):this.local,r||this.children)}remove(e){return 0==e.length||this==rm?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let i,o=n[r]+t,s=n[r+1]+t;for(let t=0,n;t<e.length;t++)(n=e[t])&&n.from>o&&n.to<s&&(e[t]=null,(i||(i=[])).push(n));if(!i)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(i,o+1);l!=rm?n[r+2]=l:(n.splice(r,3),r-=3)}if(r.length){for(let n=0,i;n<e.length;n++)if(i=e[n])for(let e=0;e<r.length;e++)r[e].eq(i,t)&&(r==this.local&&(r=this.local.slice()),r.splice(e--,1))}return n==this.children&&r==this.local?this:r.length||n.length?new rf(r,n):rm}forChild(e,t){let n,r;if(this==rm)return this;if(t.isLeaf)return rf.empty;for(let t=0;t<this.children.length;t+=3)if(this.children[t]>=e){this.children[t]==e&&(n=this.children[t+2]);break}let i=e+1,o=i+t.content.size;for(let e=0;e<this.local.length;e++){let t=this.local[e];if(t.from<o&&t.to>i&&t.type instanceof rc){let e=Math.max(i,t.from)-i,n=Math.min(o,t.to)-i;e<n&&(r||(r=[])).push(t.copy(e,n))}}if(r){let e=new rf(r.sort(rk),rp);return n?new rg([e,n]):e}return n||rm}eq(e){if(this==e)return!0;if(!(e instanceof rf)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return rx(this.localsInner(e))}localsInner(e){if(this==rm)return rp;if(e.inlineContent||!this.local.some(rc.is))return this.local;let t=[];for(let e=0;e<this.local.length;e++)this.local[e].type instanceof rc||t.push(this.local[e]);return t}forEachSet(e){e(this)}}rf.empty=new rf([],[]),rf.removeOverlap=rx;let rm=rf.empty;class rg{constructor(e){this.members=e}map(e,t){let n=this.members.map(n=>n.map(e,t,ru));return rg.from(n)}forChild(e,t){if(t.isLeaf)return rf.empty;let n=[];for(let r=0;r<this.members.length;r++){let i=this.members[r].forChild(e,t);i!=rm&&(i instanceof rg?n=n.concat(i.members):n.push(i))}return rg.from(n)}eq(e){if(!(e instanceof rg)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let i=this.members[r].localsInner(e);if(i.length)if(t){n&&(t=t.slice(),n=!1);for(let e=0;e<i.length;e++)t.push(i[e])}else t=i}return t?rx(n?t:t.sort(rk)):rp}static from(e){switch(e.length){case 0:return rm;case 1:return e[0];default:return new rg(e.every(e=>e instanceof rf)?e:e.reduce((e,t)=>e.concat(t instanceof rf?t:t.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function ry(e,t){if(!t||!e.length)return e;let n=[];for(let r=0;r<e.length;r++){let i=e[r];n.push(new rh(i.from+t,i.to+t,i.type))}return n}function rb(e,t,n){if(t.isLeaf)return null;let r=n+t.nodeSize,i=null;for(let t=0,o;t<e.length;t++)(o=e[t])&&o.from>n&&o.to<r&&((i||(i=[])).push(o),e[t]=null);return i}function rv(e){let t=[];for(let n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function rw(e,t,n,r){let i=[],o=!1;t.forEach((t,s)=>{let l=rb(e,t,s+n);if(l){o=!0;let e=rw(l,t,n+s+1,r);e!=rm&&i.push(s,s+t.nodeSize,e)}});let s=ry(o?rv(e):e,-n).sort(rk);for(let e=0;e<s.length;e++)s[e].type.valid(t,s[e])||(r.onRemove&&r.onRemove(s[e].spec),s.splice(e--,1));return s.length||i.length?new rf(s,i):rm}function rk(e,t){return e.from-t.from||e.to-t.to}function rx(e){let t=e;for(let n=0;n<t.length-1;n++){let r=t[n];if(r.from!=r.to)for(let i=n+1;i<t.length;i++){let o=t[i];if(o.from==r.from){o.to!=r.to&&(t==e&&(t=e.slice()),t[i]=o.copy(o.from,r.to),rS(t,i+1,o.copy(r.to,o.to)));continue}o.from<r.to&&(t==e&&(t=e.slice()),t[n]=r.copy(r.from,o.from),rS(t,i,r.copy(o.from,r.to)));break}}return t}function rS(e,t,n){for(;t<e.length&&rk(n,e[t])>0;)t++;e.splice(t,0,n)}function rM(e){let t=[];return e.someProp("decorations",n=>{let r=n(e.state);r&&r!=rm&&t.push(r)}),e.cursorWrapper&&t.push(rf.create(e.state.doc,[e.cursorWrapper.deco])),rg.from(t)}let rC={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},rO=tT&&tE<=11;class rN{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class rA{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new rN,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(e=>{for(let t=0;t<e.length;t++)this.queue.push(e[t]);tT&&tE<=11&&e.some(e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length)?this.flushSoon():this.flush()}),rO&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,rC)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(nM(this.view)){if(this.suppressingSelectionUpdates)return ng(this.view);if(tT&&tE<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&tg(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,n;for(let n=e.focusNode;n;n=tp(n))t.add(n);for(let r=e.anchorNode;r;r=tp(r))if(t.has(r)){n=r;break}let r=n&&this.view.docView.nearestDesc(n);if(r&&r.ignoreMutation({type:"selection",target:3==n.nodeType?n.parentNode:n}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){var e;let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let n=this.pendingRecords();n.length&&(this.queue=[]);let r=t.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&nM(t)&&!this.ignoreSelectionChange(r),o=-1,s=-1,l=!1,a=[];if(t.editable)for(let e=0;e<n.length;e++){let t=this.registerMutation(n[e],a);t&&(o=o<0?t.from:Math.min(t.from,o),s=s<0?t.to:Math.max(t.to,s),t.typeOver&&(l=!0))}if(tD&&a.length){let e=a.filter(e=>"BR"==e.nodeName);if(2==e.length){let[t,n]=e;t.parentNode&&t.parentNode.parentNode==n.parentNode?n.remove():t.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of e){let e=r.parentNode;e&&"LI"==e.nodeName&&(!n||function(e,t){for(let n=t.parentNode;n&&n!=e.dom;n=n.parentNode){let t=e.docView.nearestDesc(n,!0);if(t&&t.node.isBlock)return n}return null}(t,n)!=e)&&r.remove()}}}let c=null;o<0&&i&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&tk(r)&&(c=nf(t))&&c.eq(eZ.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,ng(t),this.currentSelection.set(r),t.scrollToSelection()):(o>-1||i)&&(o>-1&&(t.docView.markDirty(o,s),e=t,!rT.has(e)&&(rT.set(e,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace))&&(e.requiresGeckoHackNode=tD,rE||(console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),rE=!0))),this.handleDOMChange(o,s,l,a),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(r)||ng(t),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let n=0;n<e.addedNodes.length;n++){let r=e.addedNodes[n];t.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,i=e.nextSibling;if(tT&&tE<=11&&e.addedNodes.length)for(let t=0;t<e.addedNodes.length;t++){let{previousSibling:n,nextSibling:o}=e.addedNodes[t];(!n||0>Array.prototype.indexOf.call(e.addedNodes,n))&&(r=n),(!o||0>Array.prototype.indexOf.call(e.addedNodes,o))&&(i=o)}let o=r&&r.parentNode==e.target?th(r)+1:0,s=n.localPosFromDOM(e.target,o,-1),l=i&&i.parentNode==e.target?th(i):e.target.childNodes.length;return{from:s,to:n.localPosFromDOM(e.target,l,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let rT=new WeakMap,rE=!1;function rD(e,t){let n=t.startContainer,r=t.startOffset,i=t.endContainer,o=t.endOffset,s=e.domAtPos(e.state.selection.anchor);return tg(s.node,s.offset,i,o)&&([n,r,i,o]=[i,o,n,r]),{anchorNode:n,anchorOffset:r,focusNode:i,focusOffset:o}}function rR(e){let t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(tj&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}else if(e.parentNode.lastChild==e||tj&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}let rI=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function rP(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:nS(e,t.resolve(n.anchor),t.resolve(n.head))}function rj(e,t,n){let r=e.depth,i=t?e.end():e.pos;for(;r>0&&(t||e.indexAfter(r)==e.node(r).childCount);)r--,i++,t=!1;if(n){let t=e.node(r).maybeChild(e.indexAfter(r));for(;t&&!t.isLeaf;)t=t.firstChild,i++}return i}function rL(e){if(2!=e.length)return!1;let t=e.charCodeAt(0),n=e.charCodeAt(1);return t>=56320&&t<=57343&&n>=55296&&n<=56319}class rz{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new nQ,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(rH),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=rV(this),r$(this),this.nodeViews=rF(this),this.docView=ne(this.state.doc,rB(this),rM(this),this.dom,this),this.domObserver=new rA(this,(e,t,n,r)=>(function(e,t,n,r,i){let o,s,l,a,c=e.input.compositionPendingChanges||(e.composing?e.input.compositionID:0);if(e.input.compositionPendingChanges=0,t<0){let t=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,n=nf(e,t);if(n&&!e.state.selection.eq(n)){if(tI&&t$&&13===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime&&e.someProp("handleKeyDown",t=>t(e,tx(13,"Enter"))))return;let r=e.state.tr.setSelection(n);"pointer"==t?r.setMeta("pointer",!0):"key"==t&&r.scrollIntoView(),c&&r.setMeta("composition",c),e.dispatch(r)}return}let d=e.state.doc.resolve(t),h=d.sharedDepth(n);t=d.before(h+1),n=e.state.doc.resolve(n).after(h+1);let p=e.state.selection,u=function(e,t,n){let r,{node:i,fromOffset:o,toOffset:s,from:l,to:a}=e.docView.parseRange(t,n),c=e.domSelectionRange(),d=c.anchorNode;if(d&&e.dom.contains(1==d.nodeType?d:d.parentNode)&&(r=[{node:d,offset:c.anchorOffset}],tk(c)||r.push({node:c.focusNode,offset:c.focusOffset})),tI&&8===e.input.lastKeyCode)for(let e=s;e>o;e--){let t=i.childNodes[e-1],n=t.pmViewDesc;if("BR"==t.nodeName&&!n){s=e;break}if(!n||n.size)break}let h=e.state.doc,p=e.someProp("domParser")||et.fromSchema(e.state.schema),u=h.resolve(l),f=null,m=p.parse(i,{topNode:u.parent,topMatch:u.parent.contentMatchAt(u.index()),topOpen:!0,from:o,to:s,preserveWhitespace:"pre"!=u.parent.type.whitespace||"full",findPositions:r,ruleFromNode:rR,context:u});if(r&&null!=r[0].pos){let e=r[0].pos,t=r[1]&&r[1].pos;null==t&&(t=e),f={anchor:e+l,head:t+l}}return{doc:m,sel:f,from:l,to:a}}(e,t,n),f=e.state.doc,m=f.slice(u.from,u.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(o=e.state.selection.to,s="end"):(o=e.state.selection.from,s="start"),e.input.lastKeyCode=null;let g=function(e,t,n,r,i){let o=e.findDiffStart(t,n);if(null==o)return null;let{a:s,b:l}=e.findDiffEnd(t,n+e.size,n+t.size);if("end"==i){let e=Math.max(0,o-Math.min(s,l));r-=s+e-o}if(s<o&&e.size<t.size){let e=r<=o&&r>=s?o-r:0;(o-=e)&&o<t.size&&rL(t.textBetween(o-1,o+1))&&(o+=e?1:-1),l=o+(l-s),s=o}else if(l<o){let t=r<=o&&r>=l?o-r:0;(o-=t)&&o<e.size&&rL(e.textBetween(o-1,o+1))&&(o+=t?1:-1),s=o+(s-l),l=o}return{start:o,endA:s,endB:l}}(m.content,u.doc.content,u.from,o,s);if(g&&e.input.domChangeCount++,(tL&&e.input.lastIOSEnter>Date.now()-225||t$)&&i.some(e=>1==e.nodeType&&!rI.test(e.nodeName))&&(!g||g.endA>=g.endB)&&e.someProp("handleKeyDown",t=>t(e,tx(13,"Enter")))){e.input.lastIOSEnter=0;return}if(!g)if(r&&p instanceof e3&&!p.empty&&p.$head.sameParent(p.$anchor)&&!e.composing&&!(u.sel&&u.sel.anchor!=u.sel.head))g={start:p.from,endA:p.to,endB:p.to};else{if(u.sel){let t=rP(e,e.state.doc,u.sel);if(t&&!t.eq(e.state.selection)){let n=e.state.tr.setSelection(t);c&&n.setMeta("composition",c),e.dispatch(n)}}return}e.state.selection.from<e.state.selection.to&&g.start==g.endB&&e.state.selection instanceof e3&&(g.start>e.state.selection.from&&g.start<=e.state.selection.from+2&&e.state.selection.from>=u.from?g.start=e.state.selection.from:g.endA<e.state.selection.to&&g.endA>=e.state.selection.to-2&&e.state.selection.to<=u.to&&(g.endB+=e.state.selection.to-g.endA,g.endA=e.state.selection.to)),tT&&tE<=11&&g.endB==g.start+1&&g.endA==g.start&&g.start>u.from&&" \xa0"==u.doc.textBetween(g.start-u.from-1,g.start-u.from+1)&&(g.start--,g.endA--,g.endB--);let y=u.doc.resolveNoCache(g.start-u.from),b=u.doc.resolveNoCache(g.endB-u.from),w=f.resolve(g.start),k=y.sameParent(b)&&y.parent.inlineContent&&w.end()>=g.endA;if((tL&&e.input.lastIOSEnter>Date.now()-225&&(!k||i.some(e=>"DIV"==e.nodeName||"P"==e.nodeName))||!k&&y.pos<u.doc.content.size&&(!y.sameParent(b)||!y.parent.inlineContent)&&!/\S/.test(u.doc.textBetween(y.pos,b.pos,"",""))&&(l=eZ.findFrom(u.doc.resolve(y.pos+1),1,!0))&&l.head>y.pos)&&e.someProp("handleKeyDown",t=>t(e,tx(13,"Enter")))){e.input.lastIOSEnter=0;return}if(e.state.selection.anchor>g.start&&function(e,t,n,r,i){if(n-t<=i.pos-r.pos||rj(r,!0,!1)<i.pos)return!1;let o=e.resolve(t);if(!r.parent.isTextblock){let e=o.nodeAfter;return null!=e&&n==t+e.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=e.resolve(rj(o,!0,!0));return!(!s.parent.isTextblock||s.pos>n||rj(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(f,g.start,g.endA,y,b)&&e.someProp("handleKeyDown",t=>t(e,tx(8,"Backspace")))){t$&&tI&&e.domObserver.suppressSelectionUpdates();return}tI&&g.endB==g.start&&(e.input.lastChromeDelete=Date.now()),t$&&!k&&y.start()!=b.start()&&0==b.parentOffset&&y.depth==b.depth&&u.sel&&u.sel.anchor==u.sel.head&&u.sel.head==g.endA&&(g.endB-=2,b=u.doc.resolveNoCache(g.endB-u.from),setTimeout(()=>{e.someProp("handleKeyDown",function(t){return t(e,tx(13,"Enter"))})},20));let x=g.start,S=g.endA,M=t=>{let n=t||e.state.tr.replace(x,S,u.doc.slice(g.start-u.from,g.endB-u.from));if(u.sel){let t=rP(e,n.doc,u.sel);t&&!(tI&&e.composing&&t.empty&&(g.start!=g.endB||e.input.lastChromeDelete<Date.now()-100)&&(t.head==x||t.head==n.mapping.map(S)-1)||tT&&t.empty&&t.head==x)&&n.setSelection(t)}return c&&n.setMeta("composition",c),n.scrollIntoView()};if(k){if(y.pos==b.pos){tT&&tE<=11&&0==y.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout(()=>ng(e),20));let t=M(e.state.tr.delete(x,S)),n=f.resolve(g.start).marksAcross(f.resolve(g.endA));n&&t.ensureMarks(n),e.dispatch(t)}else if(g.endA==g.endB&&(a=function(e,t){let n=e.firstChild.marks,r=t.firstChild.marks,i=n,o=r,s,l,a;for(let e=0;e<r.length;e++)i=r[e].removeFromSet(i);for(let e=0;e<n.length;e++)o=n[e].removeFromSet(o);if(1==i.length&&0==o.length)l=i[0],s="add",a=e=>e.mark(l.addToSet(e.marks));else{if(0!=i.length||1!=o.length)return null;l=o[0],s="remove",a=e=>e.mark(l.removeFromSet(e.marks))}let c=[];for(let e=0;e<t.childCount;e++)c.push(a(t.child(e)));if(v.from(c).eq(e))return{mark:l,type:s}}(y.parent.content.cut(y.parentOffset,b.parentOffset),w.parent.content.cut(w.parentOffset,g.endA-w.start())))){let t=M(e.state.tr);"add"==a.type?t.addMark(x,S,a.mark):t.removeMark(x,S,a.mark),e.dispatch(t)}else if(y.parent.child(y.index()).isText&&y.index()==b.index()-!b.textOffset){let t=y.parent.textBetween(y.parentOffset,b.parentOffset),n=()=>M(e.state.tr.insertText(t,x,S));e.someProp("handleTextInput",r=>r(e,x,S,t,n))||e.dispatch(n())}}else e.dispatch(M())})(this,e,t,n,r)),this.domObserver.start(),function(e){for(let t in nU){let n=nU[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=t=>{!function(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}(e,t)||n0(e,t)||!e.editable&&t.type in nG||n(e,t)},nY[t]?{passive:!0}:void 0)}tj&&e.dom.addEventListener("input",()=>null),nZ(e)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;for(let t in this._props={},e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&nZ(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(rH),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let e in this._props)t[e]=this._props[e];for(let n in t.state=this.state,e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n,r,i;let o=this.state,s=!1,l=!1;e.storedMarks&&this.composing&&(n9(this),l=!0),this.state=e;let a=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(a||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let e=rF(this);(function(e,t){let n=0,r=0;for(let r in e){if(e[r]!=t[r])return!0;n++}for(let e in t)r++;return n!=r})(e,this.nodeViews)&&(this.nodeViews=e,s=!0)}(a||t.handleDOMEvents!=this._props.handleDOMEvents)&&nZ(this),this.editable=rV(this),r$(this);let c=rM(this),d=rB(this),h=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",p=s||!this.docView.matchesNode(e.doc,d,c);(p||!e.selection.eq(o.selection))&&(l=!0);let u="preserve"==h&&l&&null==this.dom.style.overflowAnchor&&function(e){let t,n,r=e.dom.getBoundingClientRect(),i=Math.max(0,r.top);for(let o=(r.left+r.right)/2,s=i+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=e.root.elementFromPoint(o,s);if(!r||r==e.dom||!e.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=i-20){t=r,n=l.top;break}}return{refDOM:t,refTop:n,stack:tq(e.dom)}}(this);if(l){let t,n,l;this.domObserver.stop();let a=p&&(tT||tI)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&(r=o.selection,i=e.selection,l=Math.min(r.$anchor.sharedDepth(r.head),i.$anchor.sharedDepth(i.head)),r.$anchor.start(l)!=i.$anchor.start(l));if(p){let t=tI?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(e){let t=e.domSelectionRange();if(!t.focusNode)return null;let n=function(e,t){for(;;){if(3==e.nodeType&&t)return e;if(1==e.nodeType&&t>0){if("false"==e.contentEditable)return null;t=tv(e=e.childNodes[t-1])}else{if(!e.parentNode||tw(e))return null;t=th(e),e=e.parentNode}}}(t.focusNode,t.focusOffset),r=function(e,t){for(;;){if(3==e.nodeType&&t<e.nodeValue.length)return e;if(1==e.nodeType&&t<e.childNodes.length){if("false"==e.contentEditable)return null;e=e.childNodes[t],t=0}else{if(!e.parentNode||tw(e))return null;t=th(e)+1,e=e.parentNode}}}(t.focusNode,t.focusOffset);if(n&&r&&n!=r){let t=r.pmViewDesc,i=e.domObserver.lastChangedTextNode;if(n==i||r==i)return i;if(!t||!t.isText(r.nodeValue))return r;if(e.input.compositionNode==r){let e=n.pmViewDesc;if(!(!e||!e.isText(n.nodeValue)))return r}}return n||r}(this)),(s||!this.docView.update(e.doc,d,c,this))&&(this.docView.updateOuterDeco(d),this.docView.destroy(),this.docView=ne(e.doc,d,c,this.dom,this)),t&&!this.trackWrites&&(a=!0)}a||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&(t=this.docView.domFromPos(this.state.selection.anchor,0),n=this.domSelectionRange(),tg(t.node,t.offset,n.anchorNode,n.anchorOffset)))?ng(this,a):(nk(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),(null==(n=this.dragging)?void 0:n.node)&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),"reset"==h?this.dom.scrollTop=0:"to selection"==h?this.scrollToSelection():u&&function({refDOM:e,refTop:t,stack:n}){let r=e?e.getBoundingClientRect().top:0;tW(n,0==r?0:r-t)}(u)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode))if(this.someProp("handleScrollToSelection",e=>e(this)));else if(this.state.selection instanceof e5){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&t_(this,t.getBoundingClientRect(),e)}else t_(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let t=this.directPlugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let t=this.state.plugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let e=n.from+(this.state.doc.content.size-t.doc.content.size);(e>0&&this.state.doc.nodeAt(e))==n.node&&(r=e)}this.dragging=new ri(e.slice,e.move,r<0?void 0:e5.create(this.state.doc,r))}someProp(e,t){let n=this._props&&this._props[e],r;if(null!=n&&(r=t?t(n):n))return r;for(let n=0;n<this.directPlugins.length;n++){let i=this.directPlugins[n].props[e];if(null!=i&&(r=t?t(i):i))return r}let i=this.state.plugins;if(i)for(let n=0;n<i.length;n++){let o=i[n].props[e];if(null!=o&&(r=t?t(o):o))return r}}hasFocus(){if(tT){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(tK)return e.focus(tK);let t=tq(e);e.focus(null==tK?{get preventScroll(){return tK={preventScroll:!0},!0}}:void 0),tK||(tK=!1,tW(t,0))}(this.dom),ng(this),this.domObserver.start()}get root(){let e=this._root;if(null==e){for(let e=this.dom.parentNode;e;e=e.parentNode)if(9==e.nodeType||11==e.nodeType&&e.host)return e.getSelection||(Object.getPrototypeOf(e).getSelection=()=>e.ownerDocument.getSelection()),this._root=e}return e||document}updateRoot(){this._root=null}posAtCoords(e){return function(e,t){var n;let r,i,o=e.dom.ownerDocument,s,l=0,a=function(e,t,n){if(e.caretPositionFromPoint)try{let r=e.caretPositionFromPoint(t,n);if(r)return{node:r.offsetNode,offset:Math.min(tv(r.offsetNode),r.offset)}}catch(e){}if(e.caretRangeFromPoint){let r=e.caretRangeFromPoint(t,n);if(r)return{node:r.startContainer,offset:Math.min(tv(r.startContainer),r.startOffset)}}}(o,t.left,t.top);a&&({node:s,offset:l}=a);let c=(e.root.elementFromPoint?e.root:o).elementFromPoint(t.left,t.top);if(!c||!e.dom.contains(1!=c.nodeType?c.parentNode:c)){let n=e.dom.getBoundingClientRect();if(!tJ(t,n)||!(c=function e(t,n,r){let i=t.childNodes.length;if(i&&r.top<r.bottom)for(let o=Math.max(0,Math.min(i-1,Math.floor(i*(n.top-r.top)/(r.bottom-r.top))-2)),s=o;;){let r=t.childNodes[s];if(1==r.nodeType){let t=r.getClientRects();for(let i=0;i<t.length;i++){let o=t[i];if(tJ(n,o))return e(r,n,o)}}if((s=(s+1)%i)==o)break}return t}(e.dom,t,n)))return null}if(tj)for(let e=c;s&&e;e=tp(e))e.draggable&&(s=void 0);if(c=(r=(n=c).parentNode)&&/^li$/i.test(r.nodeName)&&t.left<n.getBoundingClientRect().left?r:n,s){let n;if(tD&&1==s.nodeType&&(l=Math.min(l,s.childNodes.length))<s.childNodes.length){let e=s.childNodes[l],n;"IMG"==e.nodeName&&(n=e.getBoundingClientRect()).right<=t.left&&n.bottom>t.top&&l++}tV&&l&&1==s.nodeType&&1==(n=s.childNodes[l-1]).nodeType&&"false"==n.contentEditable&&n.getBoundingClientRect().top>=t.top&&l--,s==e.dom&&l==s.childNodes.length-1&&1==s.lastChild.nodeType&&t.top>s.lastChild.getBoundingClientRect().bottom?i=e.state.doc.content.size:(0==l||1!=s.nodeType||"BR"!=s.childNodes[l-1].nodeName)&&(i=function(e,t,n,r){let i=-1;for(let n=t,o=!1;n!=e.dom;){let t=e.docView.nearestDesc(n,!0),s;if(!t)return null;if(1==t.dom.nodeType&&(t.node.isBlock&&t.parent||!t.contentDOM)&&((s=t.dom.getBoundingClientRect()).width||s.height)&&(t.node.isBlock&&t.parent&&(!o&&s.left>r.left||s.top>r.top?i=t.posBefore:(!o&&s.right<r.left||s.bottom<r.top)&&(i=t.posAfter),o=!0),!t.contentDOM&&i<0&&!t.node.isText))return(t.node.isBlock?r.top<(s.top+s.bottom)/2:r.left<(s.left+s.right)/2)?t.posBefore:t.posAfter;n=t.dom.parentNode}return i>-1?i:e.docView.posFromDOM(t,n,-1)}(e,s,l,t))}null==i&&(i=function(e,t,n){let{node:r,offset:i}=function e(t,n){let r,i,o,s=2e8,l,a=0,c=n.top,d=n.top;for(let e=t.firstChild,h=0;e;e=e.nextSibling,h++){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=tf(e).getClientRects()}for(let p=0;p<t.length;p++){let u=t[p];if(u.top<=c&&u.bottom>=d){c=Math.max(u.bottom,c),d=Math.min(u.top,d);let t=u.left>n.left?u.left-n.left:u.right<n.left?n.left-u.right:0;if(t<s){o=e,s=t,l=t&&3==o.nodeType?{left:u.right<n.left?u.right:u.left,top:n.top}:n,1==e.nodeType&&t&&(a=h+ +(n.left>=(u.left+u.right)/2));continue}}else u.top>n.top&&!r&&u.left<=n.left&&u.right>=n.left&&(r=e,i={left:Math.max(u.left,Math.min(u.right,n.left)),top:u.top});!o&&(n.left>=u.right&&n.top>=u.top||n.left>=u.left&&n.top>=u.bottom)&&(a=h+1)}}return(!o&&r&&(o=r,l=i,s=0),o&&3==o.nodeType)?function(e,t){let n=e.nodeValue.length,r=document.createRange();for(let i=0;i<n;i++){r.setEnd(e,i+1),r.setStart(e,i);let n=tG(r,1);if(n.top!=n.bottom&&tJ(t,n))return{node:e,offset:i+ +(t.left>=(n.left+n.right)/2)}}return{node:e,offset:0}}(o,l):!o||s&&1==o.nodeType?{node:t,offset:a}:e(o,l)}(t,n),o=-1;if(1==r.nodeType&&!r.firstChild){let e=r.getBoundingClientRect();o=e.left!=e.right&&n.left>(e.left+e.right)/2?1:-1}return e.docView.posFromDOM(r,i,o)}(e,c,t));let d=e.docView.nearestDesc(c,!0);return{pos:i,inside:d?d.posAtStart-d.border:-1}}(this,e)}coordsAtPos(e,t=1){return tQ(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(null==r)throw RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){return function(e,t,n){let r,i;return t2==t&&t3==n?t4:(t2=t,t3=n,t4="up"==n||"down"==n?(r=t.selection,i="up"==n?r.$from:r.$to,t0(e,t,()=>{let{node:t}=e.docView.domFromPos(i.pos,"up"==n?-1:1);for(;;){let n=e.docView.nearestDesc(t,!0);if(!n)break;if(n.node.isBlock){t=n.contentDOM||n.dom;break}t=n.dom.parentNode}let r=tQ(e,i.pos,1);for(let e=t.firstChild;e;e=e.nextSibling){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=tf(e,0,e.nodeValue.length).getClientRects()}for(let e=0;e<t.length;e++){let i=t[e];if(i.bottom>i.top+1&&("up"==n?r.top-i.top>(i.bottom-r.top)*2:i.bottom-r.bottom>(r.bottom-i.top)*2))return!1}}return!0})):function(e,t,n){let{$head:r}=t.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,o=i==r.parent.content.size,s=e.domSelection();return s?t1.test(r.parent.textContent)&&s.modify?t0(e,t,()=>{let{focusNode:t,focusOffset:i,anchorNode:o,anchorOffset:l}=e.domSelectionRange(),a=s.caretBidiLevel;s.modify("move",n,"character");let c=r.depth?e.docView.domAfterPos(r.before()):e.dom,{focusNode:d,focusOffset:h}=e.domSelectionRange(),p=d&&!c.contains(1==d.nodeType?d:d.parentNode)||t==d&&i==h;try{s.collapse(o,l),t&&(t!=o||i!=l)&&s.extend&&s.extend(t,i)}catch(e){}return null!=a&&(s.caretBidiLevel=a),p}):"left"==n||"backward"==n?!i:o:r.pos==r.start()||r.pos==r.end()}(e,t,n))}(this,t||this.state,e)}pasteHTML(e,t){return rn(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return rn(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return nB(this,e)}destroy(){if(this.docView){for(let e in this.domObserver.stop(),this.input.eventHandlers)this.dom.removeEventListener(e,this.input.eventHandlers[e]);clearTimeout(this.input.composingTimeout),clearTimeout(this.input.lastIOSEnterFallbackTimeout),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],rM(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,tm()}}get isDestroyed(){return null==this.docView}dispatchEvent(e){!n0(this,e)&&nU[e.type]&&(this.editable||!(e.type in nG))&&nU[e.type](this,e)}domSelectionRange(){let e=this.domSelection();return e?tj&&11===this.root.nodeType&&function(e){let t=e.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}(this.dom.ownerDocument)==this.dom&&function(e,t){let n;if(t.getComposedRanges){let n=t.getComposedRanges(e.root)[0];if(n)return rD(e,n)}function r(e){e.preventDefault(),e.stopImmediatePropagation(),n=e.getTargetRanges()[0]}return e.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),e.dom.removeEventListener("beforeinput",r,!0),n?rD(e,n):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function rB(e){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),e.someProp("attributes",n=>{if("function"==typeof n&&(n=n(e.state)),n)for(let e in n)"class"==e?t.class+=" "+n[e]:"style"==e?t.style=(t.style?t.style+";":"")+n[e]:t[e]||"contenteditable"==e||"nodeName"==e||(t[e]=String(n[e]))}),t.translate||(t.translate="no"),[rh.node(0,e.state.doc.content.size,t)]}function r$(e){if(e.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:rh.widget(e.state.selection.from,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function rV(e){return!e.someProp("editable",t=>!1===t(e.state))}function rF(e){let t=Object.create(null);function n(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function rH(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw RangeError("Plugins passed directly to the view must not have a state component")}rz.prototype.dispatch=function(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))};for(var r_={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},rq={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},rW="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),rK="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),rJ=0;rJ<10;rJ++)r_[48+rJ]=r_[96+rJ]=String(rJ);for(var rJ=1;rJ<=24;rJ++)r_[rJ+111]="F"+rJ;for(var rJ=65;rJ<=90;rJ++)r_[rJ]=String.fromCharCode(rJ+32),rq[rJ]=String.fromCharCode(rJ);for(var rU in r_)rq.hasOwnProperty(rU)||(rq[rU]=r_[rU]);let rG="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),rY="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function rQ(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function rX(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let e=0;e<o.length-1;e++){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))rG?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+s)}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),i&&(s="Meta-"+s),r&&(s="Shift-"+s),s}(n)]=e[n];return t}(e);return function(e,n){var r;let i=("Esc"==(r=!(rW&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||rK&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?rq:r_)[n.keyCode]||n.key||"Unidentified")&&(r="Escape"),"Del"==r&&(r="Delete"),"Left"==r&&(r="ArrowLeft"),"Up"==r&&(r="ArrowUp"),"Right"==r&&(r="ArrowRight"),"Down"==r&&(r="ArrowDown"),r),o,s=t[rQ(i,n)];if(s&&s(e.state,e.dispatch,e))return!0;if(1==i.length&&" "!=i){if(n.shiftKey){let r=t[rQ(i,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(rY&&n.ctrlKey&&n.altKey)&&(o=r_[n.keyCode])&&o!=i){let r=t[rQ(o,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}let rZ=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function r0(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let r1=(e,t,n)=>{let r=r0(e,n);if(!r)return!1;let i=r8(r);if(!i){let n=r.blockRange(),i=n&&eE(n);return null!=i&&(t&&t(e.tr.lift(n,i).scrollIntoView()),!0)}let o=i.nodeBefore;if(ip(e,i,t,-1))return!0;if(0==r.parent.content.size&&(r5(o,"end")||e5.isSelectable(o)))for(let n=r.depth;;n--){let s=eV(e.doc,r.before(n),r.after(n),C.empty);if(s&&s.slice.size<s.to-s.from){if(t){let n=e.tr.step(s);n.setSelection(r5(o,"end")?eZ.findFrom(n.doc.resolve(n.mapping.map(i.pos,-1)),-1):e5.create(n.doc,i.pos-o.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!!o.isAtom&&i.depth==r.depth-1&&(t&&t(e.tr.delete(i.pos-o.nodeSize,i.pos).scrollIntoView()),!0)},r2=(e,t,n)=>{let r=r0(e,n);if(!r)return!1;let i=r8(r);return!!i&&r4(e,i,t)},r3=(e,t,n)=>{let r=r7(e,n);if(!r)return!1;let i=it(r);return!!i&&r4(e,i,t)};function r4(e,t,n){let r=t.nodeBefore,i=t.pos-1;for(;!r.isTextblock;i--){if(r.type.spec.isolating)return!1;let e=r.lastChild;if(!e)return!1;r=e}let o=t.nodeAfter,s=t.pos+1;for(;!o.isTextblock;s++){if(o.type.spec.isolating)return!1;let e=o.firstChild;if(!e)return!1;o=e}let l=eV(e.doc,i,s,C.empty);if(!l||l.from!=i||l instanceof eO&&l.slice.size>=s-i)return!1;if(n){let t=e.tr.step(l);t.setSelection(e3.create(t.doc,i)),n(t.scrollIntoView())}return!0}function r5(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let r6=(e,t,n)=>{let{$head:r,empty:i}=e.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):r.parentOffset>0)return!1;o=r8(r)}let s=o&&o.nodeBefore;return!!s&&!!e5.isSelectable(s)&&(t&&t(e.tr.setSelection(e5.create(e.doc,o.pos-s.nodeSize)).scrollIntoView()),!0)};function r8(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function r7(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let r9=(e,t,n)=>{let r=r7(e,n);if(!r)return!1;let i=it(r);if(!i)return!1;let o=i.nodeAfter;if(ip(e,i,t,1))return!0;if(0==r.parent.content.size&&(r5(o,"start")||e5.isSelectable(o))){let n=eV(e.doc,r.before(),r.after(),C.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(r5(o,"start")?eZ.findFrom(r.doc.resolve(r.mapping.map(i.pos)),1):e5.create(r.doc,r.mapping.map(i.pos))),t(r.scrollIntoView())}return!0}}return!!o.isAtom&&i.depth==r.depth-1&&(t&&t(e.tr.delete(i.pos,i.pos+o.nodeSize).scrollIntoView()),!0)},ie=(e,t,n)=>{let{$head:r,empty:i}=e.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):r.parentOffset<r.parent.content.size)return!1;o=it(r)}let s=o&&o.nodeAfter;return!!s&&!!e5.isSelectable(s)&&(t&&t(e.tr.setSelection(e5.create(e.doc,o.pos)).scrollIntoView()),!0)};function it(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let ir=(e,t)=>{let n=e.selection,r=n instanceof e5,i;if(r){if(n.node.isTextblock||!eL(e.doc,n.from))return!1;i=n.from}else if(null==(i=eB(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(i);r&&n.setSelection(e5.create(n.doc,i-e.doc.resolve(i).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},ii=(e,t)=>{let n=e.selection,r;if(n instanceof e5){if(n.node.isTextblock||!eL(e.doc,n.to))return!1;r=n.to}else if(null==(r=eB(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(r).scrollIntoView()),!0},io=(e,t)=>{let{$from:n,$to:r}=e.selection,i=n.blockRange(r),o=i&&eE(i);return null!=o&&(t&&t(e.tr.lift(i,o).scrollIntoView()),!0)},is=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!!n.parent.type.spec.code&&!!n.sameParent(r)&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function il(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let ia=(e,t)=>{let{$head:n,$anchor:r}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let i=n.node(-1),o=n.indexAfter(-1),s=il(i.contentMatchAt(o));if(!s||!i.canReplaceWith(o,o,s))return!1;if(t){let r=n.after(),i=e.tr.replaceWith(r,r,s.createAndFill());i.setSelection(eZ.near(i.doc.resolve(r),1)),t(i.scrollIntoView())}return!0},ic=(e,t)=>{let n=e.selection,{$from:r,$to:i}=n;if(n instanceof e8||r.parent.inlineContent||i.parent.inlineContent)return!1;let o=il(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(t){let n=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,s=e.tr.insert(n,o.createAndFill());s.setSelection(e3.create(s.doc,n+1)),t(s.scrollIntoView())}return!0},id=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if(ej(e.doc,r))return t&&t(e.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),i=r&&eE(r);return null!=i&&(t&&t(e.tr.lift(r,i).scrollIntoView()),!0)},ih=(e,t)=>{let{$from:n,to:r}=e.selection,i,o=n.sharedDepth(r);return 0!=o&&(i=n.before(o),t&&t(e.tr.setSelection(e5.create(e.doc,i))),!0)};function ip(e,t,n,r){let i,o,s,l=t.nodeBefore,a=t.nodeAfter,c,d,h=l.type.spec.isolating||a.type.spec.isolating;if(!h&&(i=t.nodeBefore,o=t.nodeAfter,s=t.index(),i&&o&&i.type.compatibleContent(o.type)&&(!i.content.size&&t.parent.canReplace(s-1,s)?(n&&n(e.tr.delete(t.pos-i.nodeSize,t.pos).scrollIntoView()),!0):!!t.parent.canReplace(s,s+1)&&!!(o.isTextblock||eL(e.doc,t.pos))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let p=!h&&t.parent.canReplace(t.index(),t.index()+1);if(p&&(c=(d=l.contentMatchAt(l.childCount)).findWrapping(a.type))&&d.matchType(c[0]||a.type).validEnd){if(n){let r=t.pos+a.nodeSize,i=v.empty;for(let e=c.length-1;e>=0;e--)i=v.from(c[e].create(null,i));i=v.from(l.copy(i));let o=e.tr.step(new eN(t.pos-1,r,t.pos,r,new C(i,1,0),c.length,!0)),s=o.doc.resolve(r+2*c.length);s.nodeAfter&&s.nodeAfter.type==l.type&&eL(o.doc,s.pos)&&o.join(s.pos),n(o.scrollIntoView())}return!0}let u=a.type.spec.isolating||r>0&&h?null:eZ.findFrom(t,1),f=u&&u.$from.blockRange(u.$to),m=f&&eE(f);if(null!=m&&m>=t.depth)return n&&n(e.tr.lift(f,m).scrollIntoView()),!0;if(p&&r5(a,"start",!0)&&r5(l,"end")){let r=l,i=[];for(;i.push(r),!r.isTextblock;)r=r.lastChild;let o=a,s=1;for(;!o.isTextblock;o=o.firstChild)s++;if(r.canReplace(r.childCount,r.childCount,o.content)){if(n){let r=v.empty;for(let e=i.length-1;e>=0;e--)r=v.from(i[e].copy(r));n(e.tr.step(new eN(t.pos-i.length,t.pos+a.nodeSize,t.pos+s,t.pos+a.nodeSize-s,new C(r,i.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function iu(e){return function(t,n){let r=t.selection,i=e<0?r.$from:r.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return!!i.node(o).isTextblock&&(n&&n(t.tr.setSelection(e3.create(t.doc,e<0?i.start(o):i.end(o)))),!0)}}let im=iu(-1),ig=iu(1);function iy(e,t=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(o,s,(r,o)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(e,t)))if(r.type==e)i=!0;else{let t=n.doc.resolve(o),r=t.index();i=t.parent.canReplaceWith(r,r+1,e)}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];i.setBlockType(o,s,e,t)}r(i.scrollIntoView())}return!0}}function ib(...e){return function(t,n,r){for(let i=0;i<e.length;i++)if(e[i](t,n,r))return!0;return!1}}let iv=ib(rZ,r1,r6),iw=ib(rZ,r9,ie),ik={Enter:ib(is,ic,id,(e,t)=>{let{$from:n,$to:r}=e.selection;if(e.selection instanceof e5&&e.selection.node.isBlock)return!!n.parentOffset&&!!ej(e.doc,n.pos)&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let i=[],o,s,l=!1,a=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;l=n.end(e)==n.pos+(n.depth-e),a=n.start(e)==n.pos-(n.depth-e),s=il(n.node(e-1).contentMatchAt(n.indexAfter(e-1)));i.unshift(t||(l&&s?{type:s}:null)),o=e;break}if(1==e)return!1;i.unshift(null)}let c=e.tr;(e.selection instanceof e3||e.selection instanceof e8)&&c.deleteSelection();let d=c.mapping.map(n.pos),h=ej(c.doc,d,i.length,i);if(h||(i[0]=s?{type:s}:null,h=ej(c.doc,d,i.length,i)),!h)return!1;if(c.split(d,i.length,i),!l&&a&&n.node(o).type!=s){let e=c.mapping.map(n.before(o)),t=c.doc.resolve(e);s&&n.node(o-1).canReplaceWith(t.index(),t.index()+1,s)&&c.setNodeMarkup(c.mapping.map(n.before(o)),s)}return t&&t(c.scrollIntoView()),!0}),"Mod-Enter":ia,Backspace:iv,"Mod-Backspace":iv,"Shift-Backspace":iv,Delete:iw,"Mod-Delete":iw,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new e8(e.doc))),!0)},ix={"Ctrl-h":ik.Backspace,"Alt-Backspace":ik["Mod-Backspace"],"Ctrl-d":ik.Delete,"Ctrl-Alt-Backspace":ik["Mod-Delete"],"Alt-Delete":ik["Mod-Delete"],"Alt-d":ik["Mod-Delete"],"Ctrl-a":im,"Ctrl-e":ig};for(let e in ik)ix[e]=ik[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();let iS=["ol",0],iM=["ul",0],iC=["li",0];function iO(e){let{state:t,transaction:n}=e,{selection:r}=n,{doc:i}=n,{storedMarks:o}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return o},get selection(){return r},get doc(){return i},get tr(){return r=n.selection,i=n.doc,o=n.storedMarks,n}}}class iN{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:e,editor:t,state:n}=this,{view:r}=t,{tr:i}=n,o=this.buildProps(i);return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,(...e)=>{let n=t(...e)(o);return i.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(i),n}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s=[],l=!!e,a=e||i.tr,c={...Object.fromEntries(Object.entries(n).map(([e,n])=>[e,(...e)=>{let r=this.buildProps(a,t),i=n(...e)(r);return s.push(i),c}])),run:()=>(l||!t||a.getMeta("preventDispatch")||this.hasCustomState||o.dispatch(a),s.every(e=>!0===e))};return c}createCan(e){let{rawCommands:t,state:n}=this,r=e||n.tr,i=this.buildProps(r,!1);return{...Object.fromEntries(Object.entries(t).map(([e,t])=>[e,(...e)=>t(...e)({...i,dispatch:void 0})])),chain:()=>this.createChain(r,!1)}}buildProps(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s={tr:e,editor:r,view:o,state:iO({state:i,transaction:e}),dispatch:t?()=>void 0:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([e,t])=>[e,(...e)=>t(...e)(s)]))}};return s}}class iA{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){let n=this.callbacks[e];return n&&n.forEach(e=>e.apply(this,t)),this}off(e,t){let n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(e=>e!==t):delete this.callbacks[e]),this}once(e,t){let n=(...r)=>{this.off(e,n),t.apply(this,r)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function iT(e,t,n){return void 0===e.config[t]&&e.parent?iT(e.parent,t,n):"function"==typeof e.config[t]?e.config[t].bind({...n,parent:e.parent?iT(e.parent,t,n):null}):e.config[t]}function iE(e){let t=e.filter(e=>"extension"===e.type);return{baseExtensions:t,nodeExtensions:e.filter(e=>"node"===e.type),markExtensions:e.filter(e=>"mark"===e.type)}}function iD(e){let t=[],{nodeExtensions:n,markExtensions:r}=iE(e),i=[...n,...r],o={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage,extensions:i},r=iT(e,"addGlobalAttributes",n);r&&r().forEach(e=>{e.types.forEach(n=>{Object.entries(e.attributes).forEach(([e,r])=>{t.push({type:n,name:e,attribute:{...o,...r}})})})})}),i.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage},r=iT(e,"addAttributes",n);r&&Object.entries(r()).forEach(([n,r])=>{let i={...o,...r};"function"==typeof(null==i?void 0:i.default)&&(i.default=i.default()),(null==i?void 0:i.isRequired)&&(null==i?void 0:i.default)===void 0&&delete i.default,t.push({type:e.name,name:n,attribute:i})})}),t}function iR(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function iI(...e){return e.filter(e=>!!e).reduce((e,t)=>{let n={...e};return Object.entries(t).forEach(([e,t])=>{if(!n[e]){n[e]=t;return}if("class"===e){let r=t?String(t).split(" "):[],i=n[e]?n[e].split(" "):[],o=r.filter(e=>!i.includes(e));n[e]=[...i,...o].join(" ")}else if("style"===e){let r=t?t.split(";").map(e=>e.trim()).filter(Boolean):[],i=n[e]?n[e].split(";").map(e=>e.trim()).filter(Boolean):[],o=new Map;i.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),r.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),n[e]=Array.from(o.entries()).map(([e,t])=>`${e}: ${t}`).join("; ")}else n[e]=t}),n},{})}function iP(e,t){return t.filter(t=>t.type===e.type.name).filter(e=>e.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]}).reduce((e,t)=>iI(e,t),{})}function ij(e){return"function"==typeof e}function iL(e,t,...n){return ij(e)?t?e.bind(t)(...n):e(...n):e}function iz(e,t){return"style"in e?e:{...e,getAttrs:n=>{let r=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===r)return!1;let i=t.reduce((e,t)=>{var r;let i=t.attribute.parseHTML?t.attribute.parseHTML(n):"string"!=typeof(r=n.getAttribute(t.name))?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):"true"===r||"false"!==r&&r;return null==i?e:{...e,[t.name]:i}},{});return{...r,...i}}}}function iB(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>!("attrs"===e&&function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t))}function i$(e,t){var n;let r=iD(e),{nodeExtensions:i,markExtensions:o}=iE(e),s=null==(n=i.find(e=>iT(e,"topNode")))?void 0:n.name;return new Z({topNode:s,nodes:Object.fromEntries(i.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=iB({...e.reduce((e,t)=>{let r=iT(t,"extendNodeSchema",o);return{...e,...r?r(n):{}}},{}),content:iL(iT(n,"content",o)),marks:iL(iT(n,"marks",o)),group:iL(iT(n,"group",o)),inline:iL(iT(n,"inline",o)),atom:iL(iT(n,"atom",o)),selectable:iL(iT(n,"selectable",o)),draggable:iL(iT(n,"draggable",o)),code:iL(iT(n,"code",o)),whitespace:iL(iT(n,"whitespace",o)),linebreakReplacement:iL(iT(n,"linebreakReplacement",o)),defining:iL(iT(n,"defining",o)),isolating:iL(iT(n,"isolating",o)),attrs:Object.fromEntries(i.map(e=>{var t;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default}]}))}),l=iL(iT(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>iz(e,i)));let a=iT(n,"renderHTML",o);a&&(s.toDOM=e=>a({node:e,HTMLAttributes:iP(e,i)}));let c=iT(n,"renderText",o);return c&&(s.toText=c),[n.name,s]})),marks:Object.fromEntries(o.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=iB({...e.reduce((e,t)=>{let r=iT(t,"extendMarkSchema",o);return{...e,...r?r(n):{}}},{}),inclusive:iL(iT(n,"inclusive",o)),excludes:iL(iT(n,"excludes",o)),group:iL(iT(n,"group",o)),spanning:iL(iT(n,"spanning",o)),code:iL(iT(n,"code",o)),attrs:Object.fromEntries(i.map(e=>{var t;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default}]}))}),l=iL(iT(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>iz(e,i)));let a=iT(n,"renderHTML",o);return a&&(s.toDOM=e=>a({mark:e,HTMLAttributes:iP(e,i)})),[n.name,s]}))})}function iV(e,t){return t.nodes[e]||t.marks[e]||null}function iF(e,t){return Array.isArray(t)?t.some(t=>("string"==typeof t?t:t.name)===e.name):t}function iH(e,t){let n=ed.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}let i_=(e,t=500)=>{let n="",r=e.parentOffset;return e.parent.nodesBetween(Math.max(0,r-t),r,(e,t,i,o)=>{var s,l;let a=(null==(l=(s=e.type.spec).toText)?void 0:l.call(s,{node:e,pos:t,parent:i,index:o}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?a:a.slice(0,Math.max(0,r-t))}),n};function iq(e){return"[object RegExp]"===Object.prototype.toString.call(e)}class iW{constructor(e){this.find=e.find,this.handler=e.handler}}let iK=(e,t)=>{if(iq(t))return t.exec(e);let n=t(e);if(!n)return null;let r=[n.text];return r.index=n.index,r.input=e,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function iJ(e){var t;let{editor:n,from:r,to:i,text:o,rules:s,plugin:l}=e,{view:a}=n;if(a.composing)return!1;let c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(null==(t=c.nodeBefore||c.nodeAfter)?void 0:t.marks.find(e=>e.type.spec.code)))return!1;let d=!1,h=i_(c)+o;return s.forEach(e=>{if(d)return;let t=iK(h,e.find);if(!t)return;let s=a.state.tr,c=iO({state:a.state,transaction:s}),p={from:r-(t[0].length-o.length),to:i},{commands:u,chain:f,can:m}=new iN({editor:n,state:c});null!==e.handler({state:c,range:p,match:t,commands:u,chain:f,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:r,to:i,text:o}),a.dispatch(s),d=!0)}),d}function iU(e){return"Object"===Object.prototype.toString.call(e).slice(8,-1)&&e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype}function iG(e,t){let n={...e};return iU(e)&&iU(t)&&Object.keys(t).forEach(r=>{iU(t[r])&&iU(e[r])?n[r]=iG(e[r],t[r]):n[r]=t[r]}),n}class iY{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=iL(iT(this,"addOptions",{name:this.name}))),this.storage=iL(iT(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new iY(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>iG(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new iY(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=iL(iT(t,"addOptions",{name:t.name})),t.storage=iL(iT(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){let{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){let i=r.marks();if(!i.find(e=>(null==e?void 0:e.type.name)===t.name))return!1;let o=i.find(e=>(null==e?void 0:e.type.name)===t.name);return o&&n.removeStoredMark(o),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}}class iQ{constructor(e){this.find=e.find,this.handler=e.handler}}let iX=(e,t,n)=>{if(iq(t))return[...e.matchAll(t)];let r=t(e,n);return r?r.map(t=>{let n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n}):[]},iZ=null,i0=e=>{var t;let n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null==(t=n.clipboardData)||t.setData("text/html",e),n};class i1{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=i1.resolve(e),this.schema=i$(this.extensions,t),this.setupExtensions()}static resolve(e){var t;let n=i1.sort(i1.flatten(e)),r=Array.from(new Set((t=n.map(e=>e.name)).filter((e,n)=>t.indexOf(e)!==n)));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(e=>`'${e}'`).join(", ")}]. This can lead to issues.`),n}static flatten(e){return e.map(e=>{let t={name:e.name,options:e.options,storage:e.storage},n=iT(e,"addExtensions",t);return n?[e,...this.flatten(n())]:e}).flat(10)}static sort(e){return e.sort((e,t)=>{let n=iT(e,"priority")||100,r=iT(t,"priority")||100;return n>r?-1:+(n<r)})}get commands(){return this.extensions.reduce((e,t)=>{let n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:iV(t.name,this.schema)},r=iT(t,"addCommands",n);return r?{...e,...r()}:e},{})}get plugins(){let{editor:e}=this,t=i1.sort([...this.extensions].reverse()),n=[],r=[],i=t.map(t=>{let i={name:t.name,options:t.options,storage:t.storage,editor:e,type:iV(t.name,this.schema)},o=[],s=iT(t,"addKeyboardShortcuts",i),l={};if("mark"===t.type&&iT(t,"exitable",i)&&(l.ArrowRight=()=>iY.handleExit({editor:e,mark:t})),s){let t=Object.fromEntries(Object.entries(s()).map(([t,n])=>[t,()=>n({editor:e})]));l={...l,...t}}let a=new tl({props:{handleKeyDown:rX(l)}});o.push(a);let c=iT(t,"addInputRules",i);iF(t,e.options.enableInputRules)&&c&&n.push(...c());let d=iT(t,"addPasteRules",i);iF(t,e.options.enablePasteRules)&&d&&r.push(...d());let h=iT(t,"addProseMirrorPlugins",i);if(h){let e=h();o.push(...e)}return o}).flat();return[function(e){let{editor:t,rules:n}=e,r=new tl({state:{init:()=>null,apply(e,i,o){let s=e.getMeta(r);if(s)return s;let l=e.getMeta("applyInputRules");return l&&setTimeout(()=>{let{text:e}=l;"string"==typeof e||(e=iH(v.from(e),o.schema));let{from:i}=l,s=i+e.length;iJ({editor:t,from:i,to:s,text:e,rules:n,plugin:r})}),e.selectionSet||e.docChanged?null:i}},props:{handleTextInput:(e,i,o,s)=>iJ({editor:t,from:i,to:o,text:s,rules:n,plugin:r}),handleDOMEvents:{compositionend:e=>(setTimeout(()=>{let{$cursor:i}=e.state.selection;i&&iJ({editor:t,from:i.pos,to:i.pos,text:"",rules:n,plugin:r})}),!1)},handleKeyDown(e,i){if("Enter"!==i.key)return!1;let{$cursor:o}=e.state.selection;return!!o&&iJ({editor:t,from:o.pos,to:o.pos,text:"\n",rules:n,plugin:r})}},isInputRules:!0});return r}({editor:e,rules:n}),...function(e){let t,{editor:n,rules:r}=e,i=null,o=!1,s=!1,l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}let a=({state:e,from:r,to:i,rule:o,pasteEvt:s})=>{let a=e.tr;if(function(e){let{editor:t,state:n,from:r,to:i,rule:o,pasteEvent:s,dropEvent:l}=e,{commands:a,chain:c,can:d}=new iN({editor:t,state:n}),h=[];return n.doc.nodesBetween(r,i,(e,t)=>{if(!e.isTextblock||e.type.spec.code)return;let p=Math.max(r,t),u=Math.min(i,t+e.content.size);iX(e.textBetween(p-t,u-t,void 0,"￼"),o.find,s).forEach(e=>{if(void 0===e.index)return;let t=p+e.index+1,r=t+e[0].length,i={from:n.tr.mapping.map(t),to:n.tr.mapping.map(r)},u=o.handler({state:n,range:i,match:e,commands:a,chain:c,can:d,pasteEvent:s,dropEvent:l});h.push(u)})}),h.every(e=>null!==e)}({editor:n,state:iO({state:e,transaction:a}),from:Math.max(r-1,0),to:i.b-1,rule:o,pasteEvent:s,dropEvent:t})&&a.steps.length){try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}return l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,a}};return r.map(e=>new tl({view(e){let t=t=>{var r;(i=(null==(r=e.dom.parentElement)?void 0:r.contains(t.target))?e.dom.parentElement:null)&&(iZ=n)},r=()=>{iZ&&(iZ=null)};return window.addEventListener("dragstart",t),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",t),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(e,n)=>{if(s=i===e.dom.parentElement,t=n,!s){let e=iZ;e&&setTimeout(()=>{let t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})},10)}return!1},paste:(e,t)=>{var n;let r=null==(n=t.clipboardData)?void 0:n.getData("text/html");return l=t,o=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,r)=>{let i=t[0],c="paste"===i.getMeta("uiEvent")&&!o,d="drop"===i.getMeta("uiEvent")&&!s,h=i.getMeta("applyPasteRules"),p=!!h;if(!c&&!d&&!p)return;if(p){let{text:t}=h;"string"==typeof t||(t=iH(v.from(t),r.schema));let{from:n}=h,i=n+t.length;return a({rule:e,state:r,from:n,to:{b:i},pasteEvt:i0(t)})}let u=n.doc.content.findDiffStart(r.doc.content),f=n.doc.content.findDiffEnd(r.doc.content);if("number"==typeof u&&f&&u!==f.b)return a({rule:e,state:r,from:u,to:f,pasteEvt:l})}}))}({editor:e,rules:r}),...i]}get attributes(){return iD(this.extensions)}get nodeViews(){let{editor:e}=this,{nodeExtensions:t}=iE(this.extensions);return Object.fromEntries(t.filter(e=>!!iT(e,"addNodeView")).map(t=>{let n=this.attributes.filter(e=>e.type===t.name),r={name:t.name,options:t.options,storage:t.storage,editor:e,type:iR(t.name,this.schema)},i=iT(t,"addNodeView",r);return i?[t.name,(r,o,s,l,a)=>{let c=iP(r,n);return i()({node:r,view:o,getPos:s,decorations:l,innerDecorations:a,editor:e,extension:t,HTMLAttributes:c})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;let n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:iV(e.name,this.schema)};"mark"===e.type&&(null==(t=iL(iT(e,"keepOnSplit",n)))||t)&&this.splittableMarks.push(e.name);let r=iT(e,"onBeforeCreate",n),i=iT(e,"onCreate",n),o=iT(e,"onUpdate",n),s=iT(e,"onSelectionUpdate",n),l=iT(e,"onTransaction",n),a=iT(e,"onFocus",n),c=iT(e,"onBlur",n),d=iT(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),i&&this.editor.on("create",i),o&&this.editor.on("update",o),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),c&&this.editor.on("blur",c),d&&this.editor.on("destroy",d)})}}class i2{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=iL(iT(this,"addOptions",{name:this.name}))),this.storage=iL(iT(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new i2(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>iG(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new i2({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=iL(iT(t,"addOptions",{name:t.name})),t.storage=iL(iT(t,"addStorage",{name:t.name,options:t.options})),t}}function i3(e,t,n){let{from:r,to:i}=t,{blockSeparator:o="\n\n",textSerializers:s={}}=n||{},l="";return e.nodesBetween(r,i,(e,n,a,c)=>{var d;e.isBlock&&n>r&&(l+=o);let h=null==s?void 0:s[e.type.name];if(h)return a&&(l+=h({node:e,pos:n,parent:a,index:c,range:t})),!1;e.isText&&(l+=null==(d=null==e?void 0:e.text)?void 0:d.slice(Math.max(r,n)-n,i-n))}),l}function i4(e){return Object.fromEntries(Object.entries(e.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}let i5=i2.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new tl({key:new td("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:e}=this,{state:t,schema:n}=e,{doc:r,selection:i}=t,{ranges:o}=i,s=Math.min(...o.map(e=>e.$from.pos)),l=Math.max(...o.map(e=>e.$to.pos)),a=i4(n);return i3(r,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function i6(e,t,n={strict:!0}){let r=Object.keys(t);return!r.length||r.every(r=>n.strict?t[r]===e[r]:iq(t[r])?t[r].test(e[r]):t[r]===e[r])}function i8(e,t,n={}){return e.find(e=>e.type===t&&i6(Object.fromEntries(Object.keys(n).map(t=>[t,e.attrs[t]])),n))}function i7(e,t,n={}){return!!i8(e,t,n)}function i9(e,t,n){var r;if(!e||!t)return;let i=e.parent.childAfter(e.parentOffset);if(i.node&&i.node.marks.some(e=>e.type===t)||(i=e.parent.childBefore(e.parentOffset)),!i.node||!i.node.marks.some(e=>e.type===t)||(n=n||(null==(r=i.node.marks[0])?void 0:r.attrs),!i8([...i.node.marks],t,n)))return;let o=i.index,s=e.start()+i.offset,l=o+1,a=s+i.node.nodeSize;for(;o>0&&i7([...e.parent.child(o-1).marks],t,n);)o-=1,s-=e.parent.child(o).nodeSize;for(;l<e.parent.childCount&&i7([...e.parent.child(l).marks],t,n);)a+=e.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function oe(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function ot(e){return e instanceof e3}function on(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function or(e,t=null){if(!t)return null;let n=eZ.atStart(e),r=eZ.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return r;let i=n.from,o=r.to;return"all"===t?e3.create(e,on(0,i,o),on(e.content.size,i,o)):e3.create(e,on(t,i,o),on(t,i,o))}function oi(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}let oo=e=>{let t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){let r=t[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?e.removeChild(r):1===r.nodeType&&oo(r)}return e};function ol(e){let t=`<body>${e}</body>`;return oo(new window.DOMParser().parseFromString(t,"text/html").body)}function oa(e,t,n){if(e instanceof B||e instanceof v)return e;n={slice:!0,parseOptions:{},...n};let r="object"==typeof e&&null!==e,i="string"==typeof e;if(r)try{if(Array.isArray(e)&&e.length>0)return v.fromArray(e.map(e=>t.nodeFromJSON(e)));let r=t.nodeFromJSON(e);return n.errorOnInvalidContent&&r.check(),r}catch(r){if(n.errorOnInvalidContent)throw Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",r),oa("",t,n)}if(i){if(n.errorOnInvalidContent){let r=!1,i="",o=new Z({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(r=!0,i="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?et.fromSchema(o).parseSlice(ol(e),n.parseOptions):et.fromSchema(o).parse(ol(e),n.parseOptions),n.errorOnInvalidContent&&r)throw Error("[tiptap error]: Invalid HTML content",{cause:Error(`Invalid element found: ${i}`)})}let r=et.fromSchema(t);return n.slice?r.parseSlice(ol(e),n.parseOptions).content:r.parse(ol(e),n.parseOptions)}return oa("",t,n)}let oc=e=>!("type"in e);function od(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function oh(e,t,n={}){let{from:r,to:i,empty:o}=e.selection,s=t?iR(t,e.schema):null,l=[];e.doc.nodesBetween(r,i,(e,t)=>{if(e.isText)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);l.push({node:e,from:n,to:o})});let a=i-r,c=l.filter(e=>!s||s.name===e.node.type.name).filter(e=>i6(e.node.attrs,n,{strict:!1}));return o?!!c.length:c.reduce((e,t)=>e+t.to-t.from,0)>=a}function op(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function ou(e,t){let n="string"==typeof t?[t]:t;return Object.keys(e).reduce((t,r)=>(n.includes(r)||(t[r]=e[r]),t),{})}function of(e,t,n={},r={}){return oa(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function om(e,t){let n=oe(t,e.schema),{from:r,to:i,empty:o}=e.selection,s=[];o?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(r,i,e=>{s.push(...e.marks)});let l=s.find(e=>e.type.name===n.name);return l?{...l.attrs}:{}}function og(e){return t=>(function(e,t){for(let n=e.depth;n>0;n-=1){let r=e.node(n);if(t(r))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:r}}})(t.$from,e)}function oy(e,t){let n=op("string"==typeof t?t:t.name,e.schema);if("node"===n){let n=iR(t,e.schema),{from:r,to:i}=e.selection,o=[];e.doc.nodesBetween(r,i,e=>{o.push(e)});let s=o.reverse().find(e=>e.type.name===n.name);return s?{...s.attrs}:{}}return"mark"===n?om(e,t):{}}function ob(e,t,n){let r=[];return e===t?n.resolve(e).marks().forEach(t=>{let i=i9(n.resolve(e),t.type);i&&r.push({mark:t,...i})}):n.nodesBetween(e,t,(e,t)=>{e&&(null==e?void 0:e.nodeSize)!==void 0&&r.push(...e.marks.map(n=>({from:t,to:t+e.nodeSize,mark:n})))}),r}function ov(e,t,n){return Object.fromEntries(Object.entries(n).filter(([n])=>{let r=e.find(e=>e.type===t&&e.name===n);return!!r&&r.attribute.keepOnSplit}))}function ow(e,t,n={}){let{empty:r,ranges:i}=e.selection,o=t?oe(t,e.schema):null;if(r)return!!(e.storedMarks||e.selection.$from.marks()).filter(e=>!o||o.name===e.type.name).find(e=>i6(e.attrs,n,{strict:!1}));let s=0,l=[];if(i.forEach(({$from:t,$to:n})=>{let r=t.pos,i=n.pos;e.doc.nodesBetween(r,i,(e,t)=>{if(!e.isText&&!e.marks.length)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);s+=o-n,l.push(...e.marks.map(e=>({mark:e,from:n,to:o})))})}),0===s)return!1;let a=l.filter(e=>!o||o.name===e.mark.type.name).filter(e=>i6(e.mark.attrs,n,{strict:!1})).reduce((e,t)=>e+t.to-t.from,0),c=l.filter(e=>!o||e.mark.type!==o&&e.mark.type.excludes(o)).reduce((e,t)=>e+t.to-t.from,0);return(a>0?a+c:a)>=s}function ok(e,t){let{nodeExtensions:n}=iE(t),r=n.find(t=>t.name===e);if(!r)return!1;let i={name:r.name,options:r.options,storage:r.storage},o=iL(iT(r,"group",i));return"string"==typeof o&&o.split(" ").includes("list")}function ox(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!=(r=e.text)?r:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let r=!0;return e.content.forEach(e=>{!1!==r&&(ox(e,{ignoreWhitespace:n,checkChildren:t})||(r=!1))}),r}return!1}function oS(e,t){let n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){let r=n.filter(e=>null==t?void 0:t.includes(e.type.name));e.tr.ensureMarks(r)}}let oM=(e,t)=>{let n=og(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&eL(e.doc,n.pos))||(e.join(n.pos),!0)},oC=(e,t)=>{let n=og(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&eL(e.doc,r))||(e.join(r),!0)};var oO=Object.freeze({__proto__:null,blur:()=>({editor:e,view:t})=>(requestAnimationFrame(()=>{var n;e.isDestroyed||(t.dom.blur(),null==(n=null==window?void 0:window.getSelection())||n.removeAllRanges())}),!0),clearContent:(e=!1)=>({commands:t})=>t.setContent("",e),clearNodes:()=>({state:e,tr:t,dispatch:n})=>{let{selection:r}=t,{ranges:i}=r;return!n||(i.forEach(({$from:n,$to:r})=>{e.doc.nodesBetween(n.pos,r.pos,(e,n)=>{if(e.type.isText)return;let{doc:r,mapping:i}=t,o=r.resolve(i.map(n)),s=r.resolve(i.map(n+e.nodeSize)),l=o.blockRange(s);if(!l)return;let a=eE(l);if(e.type.isTextblock){let{defaultType:e}=o.parent.contentMatchAt(o.index());t.setNodeMarkup(l.start,e)}(a||0===a)&&t.lift(l,a)})}),!0)},command:e=>t=>e(t),createParagraphNear:()=>({state:e,dispatch:t})=>ic(e,t),cut:(e,t)=>({editor:n,tr:r})=>{let{state:i}=n,o=i.doc.slice(e.from,e.to);r.deleteRange(e.from,e.to);let s=r.mapping.map(t);return r.insert(s,o.content),r.setSelection(new e3(r.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,r=n.$anchor.node();if(r.content.size>0)return!1;let i=e.selection.$anchor;for(let n=i.depth;n>0;n-=1)if(i.node(n).type===r.type){if(t){let t=i.before(n),r=i.after(n);e.delete(t,r).scrollIntoView()}return!0}return!1},deleteNode:e=>({tr:t,state:n,dispatch:r})=>{let i=iR(e,n.schema),o=t.selection.$anchor;for(let e=o.depth;e>0;e-=1)if(o.node(e).type===i){if(r){let n=o.before(e),r=o.after(e);t.delete(n,r).scrollIntoView()}return!0}return!1},deleteRange:e=>({tr:t,dispatch:n})=>{let{from:r,to:i}=e;return n&&t.delete(r,i),!0},deleteSelection:()=>({state:e,dispatch:t})=>rZ(e,t),enter:()=>({commands:e})=>e.keyboardShortcut("Enter"),exitCode:()=>({state:e,dispatch:t})=>ia(e,t),extendMarkRange:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let o=oe(e,r.schema),{doc:s,selection:l}=n,{$from:a,from:c,to:d}=l;if(i){let e=i9(a,o,t);if(e&&e.from<=c&&e.to>=d){let t=e3.create(s,e.from,e.to);n.setSelection(t)}}return!0},first:e=>t=>{let n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1},focus:(e=null,t={})=>({editor:n,view:r,tr:i,dispatch:o})=>{t={scrollIntoView:!0,...t};let s=()=>{(oi()||"Android"===navigator.platform||/android/i.test(navigator.userAgent))&&r.dom.focus(),requestAnimationFrame(()=>{!n.isDestroyed&&(r.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())})};if(r.hasFocus()&&null===e||!1===e)return!0;if(o&&null===e&&!ot(n.state.selection))return s(),!0;let l=or(i.doc,e)||n.state.selection,a=n.state.selection.eq(l);return o&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),s()),!0},forEach:(e,t)=>n=>e.every((e,r)=>t(e,{...n,index:r})),insertContent:(e,t)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),insertContentAt:(e,t,n)=>({tr:r,dispatch:i,editor:o})=>{var s;if(i){let i,l;n={parseOptions:o.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};let a=e=>{o.emit("contentError",{editor:o,error:e,disableCollaboration:()=>{o.storage.collaboration&&(o.storage.collaboration.isDisabled=!0)}})},c={preserveWhitespace:"full",...n.parseOptions};if(!n.errorOnInvalidContent&&!o.options.enableContentCheck&&o.options.emitContentError)try{oa(t,o.schema,{parseOptions:c,errorOnInvalidContent:!0})}catch(e){a(e)}try{i=oa(t,o.schema,{parseOptions:c,errorOnInvalidContent:null!=(s=n.errorOnInvalidContent)?s:o.options.enableContentCheck})}catch(e){return a(e),!1}let{from:d,to:h}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},p=!0,u=!0;if((oc(i)?i:[i]).forEach(e=>{e.check(),p=!!p&&e.isText&&0===e.marks.length,u=!!u&&e.isBlock}),d===h&&u){let{parent:e}=r.doc.resolve(d);!e.isTextblock||e.type.spec.code||e.childCount||(d-=1,h+=1)}if(p){if(Array.isArray(t))l=t.map(e=>e.text||"").join("");else if(t instanceof v){let e="";t.forEach(t=>{t.text&&(e+=t.text)}),l=e}else l="object"==typeof t&&t&&t.text?t.text:t;r.insertText(l,d,h)}else l=i,r.replaceWith(d,h,l);n.updateSelection&&function(e,t,n){let r=e.steps.length-1;if(r<t)return;let i=e.steps[r];if(!(i instanceof eO||i instanceof eN))return;let o=e.mapping.maps[r],s=0;o.forEach((e,t,n,r)=>{0===s&&(s=r)}),e.setSelection(eZ.near(e.doc.resolve(s),-1))}(r,r.steps.length-1,0),n.applyInputRules&&r.setMeta("applyInputRules",{from:d,text:l}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:d,text:l})}return!0},joinBackward:()=>({state:e,dispatch:t})=>r1(e,t),joinDown:()=>({state:e,dispatch:t})=>ii(e,t),joinForward:()=>({state:e,dispatch:t})=>r9(e,t),joinItemBackward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=eB(e.doc,e.selection.$from.pos,-1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinItemForward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=eB(e.doc,e.selection.$from.pos,1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinTextblockBackward:()=>({state:e,dispatch:t})=>r2(e,t),joinTextblockForward:()=>({state:e,dispatch:t})=>r3(e,t),joinUp:()=>({state:e,dispatch:t})=>ir(e,t),keyboardShortcut:e=>({editor:t,view:n,tr:r,dispatch:i})=>{let o=(function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"===s&&(s=" ");for(let e=0;e<o.length-1;e+=1){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))oi()||od()?i=!0:n=!0;else throw Error(`Unrecognized modifier name: ${s}`)}return t&&(s=`Alt-${s}`),n&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),r&&(s=`Shift-${s}`),s})(e).split(/-(?!$)/),s=o.find(e=>!["Alt","Ctrl","Meta","Shift"].includes(e)),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:o.includes("Alt"),ctrlKey:o.includes("Ctrl"),metaKey:o.includes("Meta"),shiftKey:o.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction(()=>{n.someProp("handleKeyDown",e=>e(n,l))});return null==a||a.steps.forEach(e=>{let t=e.map(r.mapping);t&&i&&r.maybeStep(t)}),!0},lift:(e,t={})=>({state:n,dispatch:r})=>{let i=iR(e,n.schema);return!!oh(n,i,t)&&io(n,r)},liftEmptyBlock:()=>({state:e,dispatch:t})=>id(e,t),liftListItem:e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);return!!o&&(!n||(r.node(o.depth-1).type==e?function(e,t,n,r){let i=e.tr,o=r.end,s=r.$to.end(r.depth);o<s&&(i.step(new eN(o-1,s,o,s,new C(v.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new L(i.doc.resolve(r.$from.pos),i.doc.resolve(s),r.depth));let l=eE(r);if(null==l)return!1;i.lift(r,l);let a=i.doc.resolve(i.mapping.map(o,-1)-1);return eL(i.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&i.join(a.pos),t(i.scrollIntoView()),!0}(t,n,e,o):function(e,t,n){let r=e.tr,i=n.parent;for(let e=n.end,t=n.endIndex-1,o=n.startIndex;t>o;t--)e-=i.child(t).nodeSize,r.delete(e-1,e+1);let o=r.doc.resolve(n.start),s=o.nodeAfter;if(r.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let l=0==n.startIndex,a=n.endIndex==i.childCount,c=o.node(-1),d=o.index(-1);if(!c.canReplace(d+ +!l,d+1,s.content.append(a?v.empty:v.from(i))))return!1;let h=o.pos,p=h+s.nodeSize;return r.step(new eN(h-!!l,p+ +!!a,h+1,p-1,new C((l?v.empty:v.from(i.copy(v.empty))).append(a?v.empty:v.from(i.copy(v.empty))),+!l,+!a),+!l)),t(r.scrollIntoView()),!0}(t,n,o)))}})(iR(e,t.schema))(t,n),newlineInCode:()=>({state:e,dispatch:t})=>is(e,t),resetAttributes:(e,t)=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=op("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=iR(e,r.schema)),"mark"===l&&(s=oe(e,r.schema)),i&&n.selection.ranges.forEach(e=>{r.doc.nodesBetween(e.$from.pos,e.$to.pos,(e,r)=>{o&&o===e.type&&n.setNodeMarkup(r,void 0,ou(e.attrs,t)),s&&e.marks.length&&e.marks.forEach(i=>{s===i.type&&n.addMark(r,r+e.nodeSize,s.create(ou(i.attrs,t)))})})}),!0)},scrollIntoView:()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),selectAll:()=>({tr:e,dispatch:t})=>{if(t){let t=new e8(e.doc);e.setSelection(t)}return!0},selectNodeBackward:()=>({state:e,dispatch:t})=>r6(e,t),selectNodeForward:()=>({state:e,dispatch:t})=>ie(e,t),selectParentNode:()=>({state:e,dispatch:t})=>ih(e,t),selectTextblockEnd:()=>({state:e,dispatch:t})=>ig(e,t),selectTextblockStart:()=>({state:e,dispatch:t})=>im(e,t),setContent:(e,t=!1,n={},r={})=>({editor:i,tr:o,dispatch:s,commands:l})=>{var a,c;let{doc:d}=o;if("full"!==n.preserveWhitespace){let l=of(e,i.schema,n,{errorOnInvalidContent:null!=(a=r.errorOnInvalidContent)?a:i.options.enableContentCheck});return s&&o.replaceWith(0,d.content.size,l).setMeta("preventUpdate",!t),!0}return s&&o.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:d.content.size},e,{parseOptions:n,errorOnInvalidContent:null!=(c=r.errorOnInvalidContent)?c:i.options.enableContentCheck})},setMark:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let{selection:o}=n,{empty:s,ranges:l}=o,a=oe(e,r.schema);if(i)if(s){let e=om(r,a);n.addStoredMark(a.create({...e,...t}))}else l.forEach(e=>{let i=e.$from.pos,o=e.$to.pos;r.doc.nodesBetween(i,o,(e,r)=>{let s=Math.max(r,i),l=Math.min(r+e.nodeSize,o);e.marks.find(e=>e.type===a)?e.marks.forEach(e=>{a===e.type&&n.addMark(s,l,a.create({...e.attrs,...t}))}):n.addMark(s,l,a.create(t))})});return function(e,t,n){var r;let{selection:i}=t,o=null;if(ot(i)&&(o=i.$cursor),o){let t=null!=(r=e.storedMarks)?r:o.marks();return!!n.isInSet(t)||!t.some(e=>e.type.excludes(n))}let{ranges:s}=i;return s.some(({$from:t,$to:r})=>{let i=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,r.pos,(e,t,r)=>{if(i)return!1;if(e.isInline){let t=!r||r.type.allowsMarkType(n),o=!!n.isInSet(e.marks)||!e.marks.some(e=>e.type.excludes(n));i=t&&o}return!i}),i})}(r,n,a)},setMeta:(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),setNode:(e,t={})=>({state:n,dispatch:r,chain:i})=>{let o,s=iR(e,n.schema);return(n.selection.$anchor.sameParent(n.selection.$head)&&(o=n.selection.$anchor.parent.attrs),s.isTextblock)?i().command(({commands:e})=>!!iy(s,{...o,...t})(n)||e.clearNodes()).command(({state:e})=>iy(s,{...o,...t})(e,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,r=on(e,0,n.content.size),i=e5.create(n,r);t.setSelection(i)}return!0},setTextSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,{from:r,to:i}="number"==typeof e?{from:e,to:e}:e,o=e3.atStart(n).from,s=e3.atEnd(n).to,l=on(r,o,s),a=on(i,o,s),c=e3.create(n,l,a);t.setSelection(c)}return!0},sinkListItem:e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);if(!o)return!1;let s=o.startIndex;if(0==s)return!1;let l=o.parent,a=l.child(s-1);if(a.type!=e)return!1;if(n){let r=a.lastChild&&a.lastChild.type==l.type,i=v.from(r?e.create():null),s=new C(v.from(e.create(null,v.from(l.type.create(null,i)))),r?3:1,0),c=o.start,d=o.end;n(t.tr.step(new eN(c-(r?3:1),d,c,d,s,1,!0)).scrollIntoView())}return!0}})(iR(e,t.schema))(t,n),splitBlock:({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:r,editor:i})=>{let{selection:o,doc:s}=t,{$from:l,$to:a}=o,c=ov(i.extensionManager.attributes,l.node().type.name,l.node().attrs);if(o instanceof e5&&o.node.isBlock)return!!l.parentOffset&&!!ej(s,l.pos)&&(r&&(e&&oS(n,i.extensionManager.splittableMarks),t.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;let d=a.parentOffset===a.parent.content.size,h=0===l.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1))),p=d&&h?[{type:h,attrs:c}]:void 0,u=ej(t.doc,t.mapping.map(l.pos),1,p);if(!p&&!u&&ej(t.doc,t.mapping.map(l.pos),1,h?[{type:h}]:void 0)&&(u=!0,p=h?[{type:h,attrs:c}]:void 0),r){if(u&&(o instanceof e3&&t.deleteSelection(),t.split(t.mapping.map(l.pos),1,p),h&&!d&&!l.parentOffset&&l.parent.type!==h)){let e=t.mapping.map(l.before()),n=t.doc.resolve(e);l.node(-1).canReplaceWith(n.index(),n.index()+1,h)&&t.setNodeMarkup(t.mapping.map(l.before()),h)}e&&oS(n,i.extensionManager.splittableMarks),t.scrollIntoView()}return u},splitListItem:(e,t={})=>({tr:n,state:r,dispatch:i,editor:o})=>{var s;let l=iR(e,r.schema),{$from:a,$to:c}=r.selection,d=r.selection.node;if(d&&d.isBlock||a.depth<2||!a.sameParent(c))return!1;let h=a.node(-1);if(h.type!==l)return!1;let p=o.extensionManager.attributes;if(0===a.parent.content.size&&a.node(-1).childCount===a.indexAfter(-1)){if(2===a.depth||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let e=v.empty,r=a.index(-1)?1:a.index(-2)?2:3;for(let t=a.depth-r;t>=a.depth-3;t-=1)e=v.from(a.node(t).copy(e));let i=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,o={...ov(p,a.node().type.name,a.node().attrs),...t},c=(null==(s=l.contentMatch.defaultType)?void 0:s.createAndFill(o))||void 0;e=e.append(v.from(l.createAndFill(null,c)||void 0));let d=a.before(a.depth-(r-1));n.replace(d,a.after(-i),new C(e,4-r,0));let h=-1;n.doc.nodesBetween(d,n.doc.content.size,(e,t)=>{if(h>-1)return!1;e.isTextblock&&0===e.content.size&&(h=t+1)}),h>-1&&n.setSelection(e3.near(n.doc.resolve(h))),n.scrollIntoView()}return!0}let u=c.pos===a.end()?h.contentMatchAt(0).defaultType:null,f={...ov(p,h.type.name,h.attrs),...t},m={...ov(p,a.node().type.name,a.node().attrs),...t};n.delete(a.pos,c.pos);let g=u?[{type:l,attrs:f},{type:u,attrs:m}]:[{type:l,attrs:f}];if(!ej(n.doc,a.pos,2))return!1;if(i){let{selection:e,storedMarks:t}=r,{splittableMarks:s}=o.extensionManager,l=t||e.$to.parentOffset&&e.$from.marks();if(n.split(a.pos,2,g).scrollIntoView(),!l||!i)return!0;let c=l.filter(e=>s.includes(e.type.name));n.ensureMarks(c)}return!0},toggleList:(e,t,n,r={})=>({editor:i,tr:o,state:s,dispatch:l,chain:a,commands:c,can:d})=>{let{extensions:h,splittableMarks:p}=i.extensionManager,u=iR(e,s.schema),f=iR(t,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:b}=m,v=y.blockRange(b),w=g||m.$to.parentOffset&&m.$from.marks();if(!v)return!1;let k=og(e=>ok(e.type.name,h))(m);if(v.depth>=1&&k&&v.depth-k.depth<=1){if(k.node.type===u)return c.liftListItem(f);if(ok(k.node.type.name,h)&&u.validContent(k.node.content)&&l)return a().command(()=>(o.setNodeMarkup(k.pos,u),!0)).command(()=>oM(o,u)).command(()=>oC(o,u)).run()}return n&&w&&l?a().command(()=>{let e=d().wrapInList(u,r),t=w.filter(e=>p.includes(e.type.name));return o.ensureMarks(t),!!e||c.clearNodes()}).wrapInList(u,r).command(()=>oM(o,u)).command(()=>oC(o,u)).run():a().command(()=>!!d().wrapInList(u,r)||c.clearNodes()).wrapInList(u,r).command(()=>oM(o,u)).command(()=>oC(o,u)).run()},toggleMark:(e,t={},n={})=>({state:r,commands:i})=>{let{extendEmptyMarkRange:o=!1}=n,s=oe(e,r.schema);return ow(r,s,t)?i.unsetMark(s,{extendEmptyMarkRange:o}):i.setMark(s,t)},toggleNode:(e,t,n={})=>({state:r,commands:i})=>{let o,s=iR(e,r.schema),l=iR(t,r.schema),a=oh(r,s,n);return(r.selection.$anchor.sameParent(r.selection.$head)&&(o=r.selection.$anchor.parent.attrs),a)?i.setNode(l,o):i.setNode(s,{...o,...n})},toggleWrap:(e,t={})=>({state:n,commands:r})=>{let i=iR(e,n.schema);return oh(n,i,t)?r.lift(i):r.wrapIn(i,t)},undoInputRule:()=>({state:e,dispatch:t})=>{let n=e.plugins;for(let r=0;r<n.length;r+=1){let i,o=n[r];if(o.spec.isInputRules&&(i=o.getState(e))){if(t){let t=e.tr,n=i.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(i.text){let n=t.doc.resolve(i.from).marks();t.replaceWith(i.from,i.to,e.schema.text(i.text,n))}else t.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,{empty:r,ranges:i}=n;return!!r||(t&&i.forEach(t=>{e.removeMark(t.$from.pos,t.$to.pos)}),!0)},unsetMark:(e,t={})=>({tr:n,state:r,dispatch:i})=>{var o;let{extendEmptyMarkRange:s=!1}=t,{selection:l}=n,a=oe(e,r.schema),{$from:c,empty:d,ranges:h}=l;if(!i)return!0;if(d&&s){let{from:e,to:t}=l,r=null==(o=c.marks().find(e=>e.type===a))?void 0:o.attrs,i=i9(c,a,r);i&&(e=i.from,t=i.to),n.removeMark(e,t,a)}else h.forEach(e=>{n.removeMark(e.$from.pos,e.$to.pos,a)});return n.removeStoredMark(a),!0},updateAttributes:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=op("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=iR(e,r.schema)),"mark"===l&&(s=oe(e,r.schema)),i&&n.selection.ranges.forEach(e=>{let i,l,a,c,d=e.$from.pos,h=e.$to.pos;n.selection.empty?r.doc.nodesBetween(d,h,(e,t)=>{o&&o===e.type&&(a=Math.max(t,d),c=Math.min(t+e.nodeSize,h),i=t,l=e)}):r.doc.nodesBetween(d,h,(e,r)=>{r<d&&o&&o===e.type&&(a=Math.max(r,d),c=Math.min(r+e.nodeSize,h),i=r,l=e),r>=d&&r<=h&&(o&&o===e.type&&n.setNodeMarkup(r,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach(i=>{if(s===i.type){let o=Math.max(r,d),l=Math.min(r+e.nodeSize,h);n.addMark(o,l,s.create({...i.attrs,...t}))}}))}),l&&(void 0!==i&&n.setNodeMarkup(i,void 0,{...l.attrs,...t}),s&&l.marks.length&&l.marks.forEach(e=>{s===e.type&&n.addMark(a,c,s.create({...e.attrs,...t}))}))}),!0)},wrapIn:(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o),l=s&&eD(s,e,t);return!!l&&(r&&r(n.tr.wrap(s,l).scrollIntoView()),!0)}})(iR(e,n.schema),t)(n,r),wrapInList:(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o);if(!s)return!1;let l=r?n.tr:null;return!!function(e,t,n,r=null){let i=!1,o=t,s=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=s.resolve(t.start-2);o=new L(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new L(t.$from,s.resolve(t.$to.end(t.depth)),t.depth)),i=!0}let l=eD(o,n,r,t);return!!l&&(e&&function(e,t,n,r,i){let o=v.empty;for(let e=n.length-1;e>=0;e--)o=v.from(n[e].type.create(n[e].attrs,o));e.step(new eN(t.start-2*!!r,t.end,t.start,t.end,new C(o,0,0),n.length,!0));let s=0;for(let e=0;e<n.length;e++)n[e].type==i&&(s=e+1);let l=n.length-s,a=t.start+n.length-2*!!r,c=t.parent;for(let n=t.startIndex,r=t.endIndex,i=!0;n<r;n++,i=!1)!i&&ej(e.doc,a,l)&&(e.split(a,l),a+=2*l),a+=c.child(n).nodeSize}(e,t,l,i,n),!0)}(l,s,e,t)&&(r&&r(l.scrollIntoView()),!0)}})(iR(e,n.schema),t)(n,r)});let oN=i2.create({name:"commands",addCommands:()=>({...oO})}),oA=i2.create({name:"drop",addProseMirrorPlugins(){return[new tl({key:new td("tiptapDrop"),props:{handleDrop:(e,t,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:r})}}})]}}),oT=i2.create({name:"editable",addProseMirrorPlugins(){return[new tl({key:new td("editable"),props:{editable:()=>this.editor.options.editable}})]}}),oE=new td("focusEvents"),oD=i2.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:e}=this;return[new tl({key:oE,props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;let r=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1},blur:(t,n)=>{e.isFocused=!1;let r=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1}}}})]}}),oR=i2.create({name:"keymap",addKeyboardShortcuts(){let e=()=>this.editor.commands.first(({commands:e})=>[()=>e.undoInputRule(),()=>e.command(({tr:t})=>{let{selection:n,doc:r}=t,{empty:i,$anchor:o}=n,{pos:s,parent:l}=o,a=o.parent.isTextblock&&s>0?t.doc.resolve(s-1):o,c=a.parent.type.spec.isolating,d=o.pos-o.parentOffset,h=c&&1===a.parent.childCount?d===o.pos:eZ.atStart(r).from===s;return!!i&&!!l.type.isTextblock&&!l.textContent.length&&!!h&&(!h||"paragraph"!==o.parent.type.name)&&e.clearNodes()}),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()]),t=()=>this.editor.commands.first(({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},r={...n},i={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return oi()||od()?i:r},addProseMirrorPlugins(){return[new tl({key:new td("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some(e=>e.getMeta("composition")))return;let r=e.some(e=>e.docChanged)&&!t.doc.eq(n.doc),i=e.some(e=>e.getMeta("preventClearDocument"));if(!r||i)return;let{empty:o,from:s,to:l}=t.selection,a=eZ.atStart(t.doc).from,c=eZ.atEnd(t.doc).to;if(o||s!==a||l!==c||!ox(n.doc))return;let d=n.tr,h=iO({state:n,transaction:d}),{commands:p}=new iN({editor:this.editor,state:h});if(p.clearNodes(),d.steps.length)return d}})]}}),oI=i2.create({name:"paste",addProseMirrorPlugins(){return[new tl({key:new td("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),oP=i2.create({name:"tabindex",addProseMirrorPlugins(){return[new tl({key:new td("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class oj{get name(){return this.node.type.name}constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!=(e=this.actualDepth)?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+ +!this.node.isText}get parent(){if(0===this.depth)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1);return new oj(this.resolvedPos.doc.resolve(e),this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new oj(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new oj(e,this.editor)}get children(){let e=[];return this.node.content.forEach((t,n)=>{let r=t.isBlock&&!t.isTextblock,i=t.isAtom&&!t.isText,o=this.pos+n+ +!i,s=this.resolvedPos.doc.resolve(o);if(!r&&s.depth<=this.depth)return;let l=new oj(s,this.editor,r,r?t:null);r&&(l.actualDepth=this.depth+1),e.push(new oj(s,this.editor,r,r?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e)if(Object.keys(t).length>0){let e=r.node.attrs,n=Object.keys(t);for(let r=0;r<n.length;r+=1){let i=n[r];if(e[i]!==t[i])break}}else n=r;r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;let i=Object.keys(t);return this.children.forEach(o=>{(!n||!(r.length>0))&&(o.node.type.name===e&&i.every(e=>t[e]===o.node.attrs[e])&&r.push(o),n&&r.length>0||(r=r.concat(o.querySelectorAll(e,t,n))))}),r}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}let oL=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;class oz extends iA{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n)),this.on("paste",({event:e,slice:t})=>this.options.onPaste(e,t)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){let r=document.querySelector("style[data-tiptap-style]");if(null!==r)return r;let i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(oL,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){let n=ij(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(e){if(this.isDestroyed)return;let t=this.state.plugins,n=t;if([].concat(e).forEach(e=>{let t="string"==typeof e?`${e}$`:e.key;n=n.filter(e=>!e.key.startsWith(t))}),t.length===n.length)return;let r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var e,t;let n=[...this.options.enableCoreExtensions?[oT,i5.configure({blockSeparator:null==(t=null==(e=this.options.coreExtensionOptions)?void 0:e.clipboardTextSerializer)?void 0:t.blockSeparator}),oN,oD,oR,oP,oA,oI].filter(e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name]):[],...this.options.extensions].filter(e=>["extension","node","mark"].includes(null==e?void 0:e.type));this.extensionManager=new i1(n,this)}createCommandManager(){this.commandManager=new iN({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=of(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(e){if(!(e instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(e.message))throw e;this.emit("contentError",{editor:this,error:e,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(e=>"collaboration"!==e.name),this.createExtensionManager()}}),t=of(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}let n=or(t,this.options.autofocus);this.view=new rz(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null==(e=this.options.editorProps)?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:ts.create({doc:t,selection:n||void 0})});let r=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(r),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;let t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(e=>{var t;return null==(t=this.capturedTransaction)?void 0:t.step(e)});return}let t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});let r=e.getMeta("focus"),i=e.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:e}),i&&this.emit("blur",{editor:this,event:i.event,transaction:e}),!e.docChanged||e.getMeta("preventUpdate")||this.emit("update",{editor:this,transaction:e})}getAttributes(e){return oy(this.state,e)}isActive(e,t){let n="string"==typeof e?e:null,r="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return oh(e,null,n)||ow(e,null,n);let r=op(t,e.schema);return"node"===r?oh(e,t,n):"mark"===r&&ow(e,t,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return iH(this.state.doc.content,this.schema)}getText(e){let{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){let n={from:0,to:e.content.size};return i3(e,n,t)}(this.state.doc,{blockSeparator:t,textSerializers:{...i4(this.schema),...n}})}get isEmpty(){return ox(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){let e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(null==(e=this.view)?void 0:e.docView)}$node(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelectorAll(e,t))||null}$pos(e){return new oj(this.state.doc.resolve(e),this)}get $doc(){return this.$pos(0)}}function oB(e){return new iW({find:e.find,handler:({state:t,range:n,match:r})=>{let i=iL(e.getAttributes,void 0,r);if(!1===i||null===i)return null;let{tr:o}=t,s=r[r.length-1],l=r[0];if(s){let r=l.search(/\S/),a=n.from+l.indexOf(s),c=a+s.length;if(ob(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>a).length)return null;c<n.to&&o.delete(c,n.to),a>n.from&&o.delete(n.from+r,a);let d=n.from+r+s.length;o.addMark(n.from+r,d,e.type.create(i||{})),o.removeStoredMark(e.type)}}})}function o$(e){return new iW({find:e.find,handler:({state:t,range:n,match:r})=>{let i=t.doc.resolve(n.from),o=iL(e.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,o)}})}function oV(e){return new iW({find:e.find,handler:({state:t,range:n,match:r,chain:i})=>{let o=iL(e.getAttributes,void 0,r)||{},s=t.tr.delete(n.from,n.to),l=s.doc.resolve(n.from).blockRange(),a=l&&eD(l,e.type,o);if(!a)return null;if(s.wrap(l,a),e.keepMarks&&e.editor){let{selection:n,storedMarks:r}=t,{splittableMarks:i}=e.editor.extensionManager,o=r||n.$to.parentOffset&&n.$from.marks();if(o){let e=o.filter(e=>i.includes(e.type.name));s.ensureMarks(e)}}if(e.keepAttributes){let t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";i().updateAttributes(t,o).run()}let c=s.doc.resolve(n.from-1).nodeBefore;c&&c.type===e.type&&eL(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(r,c))&&s.join(n.from-1)}})}class oF{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=iL(iT(this,"addOptions",{name:this.name}))),this.storage=iL(iT(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new oF(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>iG(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new oF(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=iL(iT(t,"addOptions",{name:t.name})),t.storage=iL(iT(t,"addStorage",{name:t.name,options:t.options})),t}}function oH(e){return new iQ({find:e.find,handler:({state:t,range:n,match:r,pasteEvent:i})=>{let o=iL(e.getAttributes,void 0,r,i);if(!1===o||null===o)return null;let{tr:s}=t,l=r[r.length-1],a=r[0],c=n.to;if(l){let r=a.search(/\S/),i=n.from+a.indexOf(l),d=i+l.length;if(ob(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>i).length)return null;d<n.to&&s.delete(d,n.to),i>n.from&&s.delete(n.from+r,i),c=n.from+r+l.length,s.addMark(n.from+r,c,e.type.create(o||{})),s.removeStoredMark(e.type)}}})}var o_={exports:{}},oq={};o_.exports=function(){if(i)return oq;i=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=l.useState,n=l.useEffect,r=l.useLayoutEffect,o=l.useDebugValue;function s(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!e(t,r)}catch(e){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,i){var l=i(),a=t({inst:{value:l,getSnapshot:i}}),c=a[0].inst,d=a[1];return r(function(){c.value=l,c.getSnapshot=i,s(c)&&d({inst:c})},[e,l,i]),n(function(){return s(c)&&d({inst:c}),e(function(){s(c)&&d({inst:c})})},[e]),o(l),l};return oq.useSyncExternalStore=void 0!==l.useSyncExternalStore?l.useSyncExternalStore:a,oq}();var oW=o_.exports;let oK=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},oJ=({contentComponent:e})=>{let t=oW.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return l.createElement(l.Fragment,null,Object.values(t))};class oU extends l.Component{constructor(e){var t;super(e),this.editorContentRef=l.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(t=e.editor)?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:y.createPortal(r.reactElement,r.element,n)},e.forEach(e=>e())},removeRenderer(n){let r={...t};delete r[n],t=r,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;let t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){let{editor:e,innerRef:t,...n}=this.props;return l.createElement(l.Fragment,null,l.createElement("div",{ref:oK(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&l.createElement(oJ,{contentComponent:e.contentComponent}))}}let oG=(0,l.forwardRef)((e,t)=>{let n=l.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[e.editor]);return l.createElement(oU,{key:n,innerRef:t,...e})}),oY=l.memo(oG);var oQ=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;for(i of t.entries())if(!e(i[1],n.get(i[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(t[i]!==n[i])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,s=o[i];if(("_owner"!==s||!t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}),oX={exports:{}},oZ={};oX.exports=function(){if(o)return oZ;o=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=oW.useSyncExternalStore,n=l.useRef,r=l.useEffect,i=l.useMemo,s=l.useDebugValue;return oZ.useSyncExternalStoreWithSelector=function(o,l,a,c,d){var h=n(null);if(null===h.current){var p={hasValue:!1,value:null};h.current=p}else p=h.current;var u=t(o,(h=i(function(){function t(t){if(!i){if(i=!0,n=t,t=c(t),void 0!==d&&p.hasValue){var o=p.value;if(d(o,t))return r=o}return r=t}if(o=r,e(n,t))return o;var s=c(t);return void 0!==d&&d(o,s)?o:(n=t,r=s)}var n,r,i=!1,o=void 0===a?null:a;return[function(){return t(l())},null===o?void 0:function(){return t(o())}]},[l,a,c,d]))[0],h[1]);return r(function(){p.hasValue=!0,p.value=u},[u]),s(u),u},oZ}();var o0=oX.exports;let o1="undefined"!=typeof window?l.useLayoutEffect:l.useEffect;class o2{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}let o3="undefined"==typeof window,o4=o3||!!("undefined"!=typeof window&&window.next);class o5{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?o3||o4?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){return new oz({...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBeforeCreate)?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBlur)?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onCreate)?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDestroy)?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null==(n=(t=this.options.current).onFocus)?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onSelectionUpdate)?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null==(n=(t=this.options.current).onTransaction)?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onUpdate)?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null==(n=(t=this.options.current).onContentError)?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDrop)?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null==(n=(t=this.options.current).onPaste)?void 0:n.call(t,...e)}})}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var r;return e===(null==(r=t.extensions)?void 0:r[n])}):e[n]===t[n]))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?o5.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}let o6=((0,l.createContext)({editor:null}).Consumer,(0,l.createContext)({onDragStart:void 0})),o8=()=>(0,l.useContext)(o6);l.forwardRef((e,t)=>{let{onDragStart:n}=o8(),r=e.as||"div";return l.createElement(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})});class o7{constructor(e,{editor:t,props:n={},as:r="div",className:i=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=e,this.editor=t,this.props=n,this.element=document.createElement(r),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),this.editor.isInitialized?flushSync(()=>{this.render()}):this.render()}render(){var e,t;let n=this.component,r=this.props,i=this.editor,o=function(){try{if(version)return parseInt(version.split(".")[0],10)>=19}catch{}return!1}(),s=!!("function"==typeof n&&n.prototype&&n.prototype.isReactComponent),l="object"==typeof n&&(null==(t=n.$$typeof)?void 0:t.toString())==="Symbol(react.forward_ref)",a={...r};!a.ref&&(o?a.ref=e=>{this.ref=e}:(s||l)&&(a.ref=e=>{this.ref=e})),this.reactElement=React.createElement(n,{...a}),null==(e=null==i?void 0:i.contentComponent)||e.setRenderer(this.id,this)}updateProps(e={}){this.props={...this.props,...e},this.render()}destroy(){var e;let t=this.editor;null==(e=null==t?void 0:t.contentComponent)||e.removeRenderer(this.id)}updateAttributes(e){Object.keys(e).forEach(t=>{this.element.setAttribute(t,e[t])})}}n(14185);let o9=/^\s*>\s$/,se=oF.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[oV({find:o9,type:this.type})]}}),st=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,sn=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,sr=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,si=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,so=iY.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[oB({find:st,type:this.type}),oB({find:sr,type:this.type})]},addPasteRules(){return[oH({find:sn,type:this.type}),oH({find:si,type:this.type})]}}),ss="textStyle",sl=/^\s*([-+*])\s$/,sa=oF.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(ss)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=oV({find:sl,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=oV({find:sl,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(ss),editor:this.editor})),[e]}}),sc=/(^|[^`])`([^`]+)`(?!`)/,sd=/(^|[^`])`([^`]+)`(?!`)/g,sh=iY.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[oB({find:sc,type:this.type})]},addPasteRules(){return[oH({find:sd,type:this.type})]}}),sp=/^```([a-z]+)?[\s\n]$/,su=/^~~~([a-z]+)?[\s\n]$/,sf=oF.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options,r=[...(null==(t=e.firstElementChild)?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",iI(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!!o&&!!s&&e.chain().command(({tr:e})=>(e.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:r}=t,{$from:i,empty:o}=n;if(!o||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let s=i.after();return void 0!==s&&(r.nodeAt(s)?e.commands.command(({tr:e})=>(e.setSelection(eZ.near(r.resolve(s))),!0)):e.commands.exitCode())}}},addInputRules(){return[o$({find:sp,type:this.type,getAttributes:e=>({language:e[1]})}),o$({find:su,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new tl({key:new td("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=null==i?void 0:i.mode;if(!n||!o)return!1;let{tr:s,schema:l}=e.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(e3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}}),sm=oF.create({name:"doc",topNode:!0,content:"block+"});class sg{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=t.width)?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i,o=this.editorView.dom,s=o.getBoundingClientRect(),l=s.width/o.offsetWidth,a=s.height/o.offsetHeight;if(r){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let r=n.getBoundingClientRect(),o=e?r.bottom:r.top;e&&t&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let s=this.width/2*a;i={left:r.left,right:r.right,top:o-s,bottom:o+s}}}}if(!i){let e=this.editorView.coordsAtPos(this.cursorPos),t=this.width/2*l;i={left:e.left-t,right:e.left+t,top:e.top,bottom:e.bottom}}let c=this.editorView.dom.offsetParent;if(!this.element&&(this.element=c.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),c&&(c!=document.body||"static"!=getComputedStyle(c).position)){let n=c.getBoundingClientRect(),r=n.width/c.offsetWidth,i=n.height/c.offsetHeight;e=n.left-c.scrollLeft*r,t=n.top-c.scrollTop*i}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=(i.left-e)/l+"px",this.element.style.top=(i.top-t)/a+"px",this.element.style.width=(i.right-i.left)/l+"px",this.element.style.height=(i.bottom-i.top)/a+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,t,e):r;if(t&&!i){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=e$(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}let sy=i2.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new tl({view:t=>new sg(t,e)})}(this.options)]}});class sb extends eZ{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return sb.valid(n)?new sb(n):eZ.near(n)}content(){return C.empty}eq(e){return e instanceof sb&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new sb(e.resolve(t.pos))}getBookmark(){return new sv(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){r:for(;;){if(!n&&sb.valid(e))return e;let r=e.pos,i=null;for(let n=e.depth;;n--){let o=e.node(n);if(t>0?e.indexAfter(n)<o.childCount:e.index(n)>0){i=o.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let s=e.doc.resolve(r);if(sb.valid(s))return s}for(;;){let o=t>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!e5.isSelectable(i)){e=e.doc.resolve(r+i.nodeSize*t),n=!1;continue r}break}i=o,r+=t;let s=e.doc.resolve(r);if(sb.valid(s))return s}return null}}}sb.prototype.visible=!1,sb.findFrom=sb.findGapCursorFrom,eZ.jsonID("gapcursor",sb);class sv{constructor(e){this.pos=e}map(e){return new sv(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return sb.valid(t)?new sb(t):eZ.near(t)}}let sw=rX({ArrowLeft:sk("horiz",-1),ArrowRight:sk("horiz",1),ArrowUp:sk("vert",-1),ArrowDown:sk("vert",1)});function sk(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,i){let o=e.selection,s=t>0?o.$to:o.$from,l=o.empty;if(o instanceof e3){if(!i.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=sb.findGapCursorFrom(s,t,l);return!!a&&(r&&r(e.tr.setSelection(new sb(a))),!0)}}function sx(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!sb.valid(r))return!1;let i=e.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&e5.isSelectable(e.state.doc.nodeAt(i.inside)))&&(e.dispatch(e.state.tr.setSelection(new sb(r))),!0)}function sS(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof sb))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let i=v.empty;for(let e=r.length-1;e>=0;e--)i=v.from(r[e].createAndFill(null,i));let o=e.state.tr.replace(n.pos,n.pos,new C(i,0,0));return o.setSelection(e3.near(o.doc.resolve(n.pos+1))),e.dispatch(o),!1}function sM(e){if(!(e.selection instanceof sb))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",rf.create(e.doc,[rh.widget(e.selection.head,t,{key:"gapcursor"})])}let sC=i2.create({name:"gapCursor",addProseMirrorPlugins:()=>[new tl({props:{decorations:sM,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&sb.valid(n)?new sb(n):null,handleClick:sx,handleKeyDown:sw,handleDOMEvents:{beforeinput:sS}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!=(t=iL(iT(e,"allowGapCursor",n)))?t:null}}}),sO=oF.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",iI(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:i}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=r.extensionManager,l=i||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&l&&o){let t=l.filter(e=>s.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),sN=oF.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,iI(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,...{[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}}),{})},addInputRules(){return this.options.levels.map(e=>o$({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}});var sA=function(){};sA.prototype.append=function(e){return e.length?(e=sA.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},sA.prototype.prepend=function(e){return e.length?sA.from(e).append(this):this},sA.prototype.appendInner=function(e){return new sE(this,e)},sA.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?sA.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},sA.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},sA.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},sA.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(t,n){return r.push(e(t,n))},t,n),r},sA.from=function(e){return e instanceof sA?e:e&&e.length?new sT(e):sA.empty};var sT=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var i=t;i<n;i++)if(!1===e(this.values[i],r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var i=t-1;i>=n;i--)if(!1===e(this.values[i],r+i))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(sA);sA.empty=new sT([]);var sE=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var i=this.left.length;if(t<i&&!1===this.left.forEachInner(e,t,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(e,Math.max(t-i,0),Math.min(this.length,n)-i,r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){var i=this.left.length;if(t>i&&!1===this.right.forEachInvertedInner(e,t-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(e,Math.min(t,i),n,r))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(sA);class sD{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,r,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}t&&(r=(n=this.remapping(s,this.items.length)).maps.length);let l=e.tr,a=[],c=[];return this.items.forEach((e,t)=>{if(!e.step){n||(r=(n=this.remapping(s,t+1)).maps.length),r--,c.push(e);return}if(n){c.push(new sR(e.map));let t=e.step.map(n.slice(r)),i;t&&l.maybeStep(t).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new sR(i,void 0,void 0,a.length+c.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(e.step);if(e.selection)return i=n?e.selection.map(n.slice(r)):e.selection,o=new sD(this.items.slice(0,s).append(c.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:l,selection:i}}addTransform(e,t,n,r){var i,o;let s,l=[],a=this.eventCount,c=this.items,d=!r&&c.length?c.get(c.length-1):null;for(let n=0;n<e.steps.length;n++){let i=e.steps[n].invert(e.docs[n]),o=new sR(e.mapping.maps[n],i,t),s;(s=d&&d.merge(o))&&(o=s,n?l.pop():c=c.slice(0,c.length-1)),l.push(o),t&&(a++,t=void 0),r||(d=o)}let h=a-n.depth;return h>sP&&(i=c,o=h,i.forEach((e,t)=>{if(e.selection&&0==o--)return s=t,!1}),c=i.slice(s),a-=h),new sD(c.append(l),a)}remapping(e,t){let n=new ey;return this.items.forEach((t,r)=>{let i=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,i)},e,t),n}addMaps(e){return 0==this.eventCount?this:new sD(this.items.append(e.map(e=>new sR(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),i=e.mapping,o=e.steps.length,s=this.eventCount;this.items.forEach(e=>{e.selection&&s--},r);let l=t;this.items.forEach(t=>{let r=i.getMirror(--l);if(null==r)return;o=Math.min(o,r);let a=i.maps[r];if(t.step){let o=e.steps[r].invert(e.docs[r]),c=t.selection&&t.selection.map(i.slice(l+1,r));c&&s++,n.push(new sR(a,o,c))}else n.push(new sR(a))},r);let a=[];for(let e=t;e<o;e++)a.push(new sR(i.maps[e]));let c=new sD(this.items.slice(0,r).append(a).append(n),s);return c.emptyItemCount()>500&&(c=c.compress(this.items.length-n.length)),c}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],i=0;return this.items.forEach((o,s)=>{if(s>=e)r.push(o),o.selection&&i++;else if(o.step){let e=o.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=o.selection&&o.selection.map(t.slice(n));l&&i++;let a=new sR(s.invert(),e,l),c,d=r.length-1;(c=r.length&&r[d].merge(a))?r[d]=c:r.push(a)}}else o.map&&n--},this.items.length,0),new sD(sA.from(r.reverse()),i)}}sD.empty=new sD(sA.empty,0);class sR{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new sR(t.getMap().invert(),t,this.selection)}}}class sI{constructor(e,t,n,r,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let sP=20;function sj(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,r,i)=>t.push(r,i));return t}function sL(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let i=t.map(e[r],1),o=t.map(e[r+1],-1);i<=o&&n.push(i,o)}return n}let sz=!1,sB=null;function s$(e){let t=e.plugins;if(sB!=t){sz=!1,sB=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){sz=!0;break}}return sz}let sV=new td("history"),sF=new td("closeHistory");function sH(e,t){return(n,r)=>{let i=sV.getState(n);if(!i||0==(e?i.undone:i.done).eventCount)return!1;if(r){let o=function(e,t,n){let r=s$(t),i=sV.get(t).spec.config,o=(n?e.undone:e.done).popEvent(t,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(n?e.done:e.undone).addTransform(o.transform,t.selection.getBookmark(),i,r),a=new sI(n?l:o.remaining,n?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(sV,{redo:n,historyState:a})}(i,n,e);o&&r(t?o.scrollIntoView():o)}return!0}}let s_=sH(!1,!0),sq=sH(!0,!0);sH(!1,!1),sH(!0,!1);let sW=i2.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>s_(e,t),redo:()=>({state:e,dispatch:t})=>sq(e,t)}),addProseMirrorPlugins(){return[function(e={}){return new tl({key:sV,state:{init:()=>new sI(sD.empty,sD.empty,null,0,-1),apply:(t,n,r)=>(function(e,t,n,r){let i=n.getMeta(sV),o;if(i)return i.historyState;n.getMeta(sF)&&(e=new sI(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(sV))if(s.getMeta(sV).redo)return new sI(e.done.addTransform(n,void 0,r,s$(t)),e.undone,sj(n.mapping.maps),e.prevTime,e.prevComposition);else return new sI(e.done,e.undone.addTransform(n,void 0,r,s$(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))if(o=n.getMeta("rebased"))return new sI(e.done.rebased(n,o),e.undone.rebased(n,o),sL(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);else return new sI(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),sL(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let i=n.getMeta("composition"),o=0==e.prevTime||!s&&e.prevComposition!=i&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,r)=>{for(let i=0;i<t.length;i+=2)e<=t[i+1]&&r>=t[i]&&(n=!0)}),n}(n,e.prevRanges)),l=s?sL(e.prevRanges,n.mapping):sj(n.mapping.maps);return new sI(e.done.addTransform(n,o?t.selection.getBookmark():void 0,r,s$(t)),sD.empty,l,n.time,null==i?e.prevComposition:i)}})(n,r,t,e)},config:e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?s_:"historyRedo"==n?sq:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),sK=oF.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",iI(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{let{selection:n}=t,{$from:r,$to:i}=n,o=e();return 0===r.parentOffset?o.insertContentAt({from:Math.max(r.pos-1,0),to:i.pos},{type:this.name}):n instanceof e5?o.insertContentAt(i.pos,{type:this.name}):o.insertContent({type:this.name}),o.command(({tr:e,dispatch:t})=>{var n;if(t){let{$to:t}=e.selection,r=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(e3.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(e5.create(e.doc,t.pos)):e.setSelection(e3.create(e.doc,t.pos));else{let i=null==(n=t.parent.type.contentMatch.defaultType)?void 0:n.create();i&&(e.insert(r,i),e.setSelection(e3.create(e.doc,r+1)))}e.scrollIntoView()}return!0}).run()}}},addInputRules(){var e;return[new iW({find:(e={find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type}).find,handler:({state:t,range:n,match:r})=>{let i=iL(e.getAttributes,void 0,r)||{},{tr:o}=t,s=n.from,l=n.to,a=e.type.create(i);if(r[1]){let e=s+r[0].lastIndexOf(r[1]);e>l?e=l:l=e+r[1].length;let t=r[0][r[0].length-1];o.insertText(t,s+r[0].length-1),o.replaceWith(e,l,a)}else if(r[0]){let t=e.type.isInline?s:s-1;o.insert(t,e.type.create(i)).delete(o.mapping.map(s),o.mapping.map(l))}o.scrollIntoView()}})]}}),sJ=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,sU=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,sG=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,sY=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,sQ=iY.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[oB({find:sJ,type:this.type}),oB({find:sG,type:this.type})]},addPasteRules(){return[oH({find:sU,type:this.type}),oH({find:sY,type:this.type})]}}),sX=oF.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",iI(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),sZ="textStyle",s0=/^(\d+)\.\s$/,s1=oF.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",iI(this.options.HTMLAttributes,n),0]:["ol",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(sZ)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=oV({find:s0,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=oV({find:s0,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(sZ)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),s2=oF.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),s3=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,s4=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,s5=iY.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[oB({find:s3,type:this.type})]},addPasteRules(){return[oH({find:s4,type:this.type})]}}),s6=oF.create({name:"text",group:"inline"}),s8=i2.create({name:"starterKit",addExtensions(){let e=[];return!1!==this.options.bold&&e.push(so.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(se.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(sa.configure(this.options.bulletList)),!1!==this.options.code&&e.push(sh.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(sf.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(sm.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(sy.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(sC.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(sO.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(sN.configure(this.options.heading)),!1!==this.options.history&&e.push(sW.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(sK.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(sQ.configure(this.options.italic)),!1!==this.options.listItem&&e.push(sX.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(s1.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(s2.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(s5.configure(this.options.strike)),!1!==this.options.text&&e.push(s6.configure(this.options.text)),e}}),s7=i2.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new tl({key:new td("placeholder"),props:{decorations:({doc:e,selection:t})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=t,i=[];if(!n)return null;let o=this.editor.isEmpty;return e.descendants((e,t)=>{let n=r>=t&&r<=t+e.nodeSize,s=!e.isLeaf&&ox(e);if((n||!this.options.showOnlyCurrent)&&s){let r=[this.options.emptyNodeClass];o&&r.push(this.options.emptyEditorClass);let s=rh.node(t,t+e.nodeSize,{class:r.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:e,pos:t,hasAnchor:n}):this.options.placeholder});i.push(s)}return this.options.includeChildren}),rf.create(e,i)}}})]}}),s9=i2.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:e=>e.length,wordCounter:e=>e.split(" ").filter(e=>""!==e).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc;if("textSize"===((null==e?void 0:e.mode)||this.options.mode)){let e=t.textBetween(0,t.content.size,void 0," ");return this.options.textCounter(e)}return t.nodeSize},this.storage.words=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc,n=t.textBetween(0,t.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let e=!1;return[new tl({key:new td("characterCount"),appendTransaction:(t,n,r)=>{if(e)return;let i=this.options.limit;if(null==i||0===i){e=!0;return}let o=this.storage.characters({node:r.doc});if(o>i){console.warn(`[CharacterCount] Initial content exceeded limit of ${i} characters. Content was automatically trimmed.`);let t=r.tr.deleteRange(0,o-i);return e=!0,t}e=!0},filterTransaction:(e,t)=>{let n=this.options.limit;if(!e.docChanged||0===n||null==n)return!0;let r=this.storage.characters({node:t.doc}),i=this.storage.characters({node:e.doc});if(i<=n||r>n&&i>n&&i<=r)return!0;if(r>n&&i>n&&i>r||!e.getMeta("paste"))return!1;let o=e.selection.$head.pos;return e.deleteRange(o-(i-n),o),!(this.storage.characters({node:e.doc})>n)}})]}}),le=(e,t)=>{for(let n in t)e[n]=t[n];return e},lt="numeric",ln="ascii",lr="alpha",li="asciinumeric",lo="alphanumeric",ls="domain",ll="emoji",la="whitespace";function lc(e,t,n){for(let r in t[lt]&&(t[li]=!0,t[lo]=!0),t[ln]&&(t[li]=!0,t[lr]=!0),t[li]&&(t[lo]=!0),t[lr]&&(t[lo]=!0),t[lo]&&(t[ls]=!0),t[ll]&&(t[ls]=!0),t){let t=(r in n||(n[r]=[]),n[r]);0>t.indexOf(e)&&t.push(e)}}function ld(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}ld.groups={},ld.prototype={accepts(){return!!this.t},go(e){let t=this.j[e];if(t)return t;for(let t=0;t<this.jr.length;t++){let n=this.jr[t][0],r=this.jr[t][1];if(r&&n.test(e))return r}return this.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,r){for(let i=0;i<e.length;i++)this.tt(e[i],t,n,r)},tr(e,t,n,r){let i;return r=r||ld.groups,t&&t.j?i=t:(i=new ld(t),n&&r&&lc(t,n,r)),this.jr.push([e,i]),i},ts(e,t,n,r){let i=this,o=e.length;if(!o)return i;for(let t=0;t<o-1;t++)i=i.tt(e[t]);return i.tt(e[o-1],t,n,r)},tt(e,t,n,r){if(r=r||ld.groups,t&&t.j)return this.j[e]=t,t;let i,o=this.go(e);return o?(le((i=new ld).j,o.j),i.jr.push.apply(i.jr,o.jr),i.jd=o.jd,i.t=o.t):i=new ld,t&&(r&&(i.t&&"string"==typeof i.t?lc(t,le(function(e,t){let n={};for(let r in t)t[r].indexOf(e)>=0&&(n[r]=!0);return n}(i.t,r),n),r):n&&lc(t,n,r)),i.t=t),this.j[e]=i,i}};let lh=(e,t,n,r,i)=>e.ta(t,n,r,i),lp=(e,t,n,r,i)=>e.tr(t,n,r,i),lu=(e,t,n,r,i)=>e.ts(t,n,r,i),lf=(e,t,n,r,i)=>e.tt(t,n,r,i),lm="WORD",lg="UWORD",ly="ASCIINUMERICAL",lb="ALPHANUMERICAL",lv="LOCALHOST",lw="UTLD",lk="SCHEME",lx="SLASH_SCHEME",lS="OPENBRACE",lM="CLOSEBRACE",lC="OPENBRACKET",lO="CLOSEBRACKET",lN="OPENPAREN",lA="CLOSEPAREN",lT="OPENANGLEBRACKET",lE="CLOSEANGLEBRACKET",lD="FULLWIDTHLEFTPAREN",lR="FULLWIDTHRIGHTPAREN",lI="LEFTCORNERBRACKET",lP="RIGHTCORNERBRACKET",lj="LEFTWHITECORNERBRACKET",lL="RIGHTWHITECORNERBRACKET",lz="FULLWIDTHLESSTHAN",lB="FULLWIDTHGREATERTHAN",l$="AMPERSAND",lV="APOSTROPHE",lF="ASTERISK",lH="BACKSLASH",l_="BACKTICK",lq="CARET",lW="COLON",lK="COMMA",lJ="DOLLAR",lU="EQUALS",lG="EXCLAMATION",lY="HYPHEN",lQ="PERCENT",lX="PIPE",lZ="PLUS",l0="POUND",l1="QUERY",l2="QUOTE",l3="FULLWIDTHMIDDLEDOT",l4="SEMI",l5="SLASH",l6="TILDE",l8="UNDERSCORE",l7="EMOJI";var l9=Object.freeze({__proto__:null,ALPHANUMERICAL:lb,AMPERSAND:l$,APOSTROPHE:lV,ASCIINUMERICAL:ly,ASTERISK:lF,AT:"AT",BACKSLASH:lH,BACKTICK:l_,CARET:lq,CLOSEANGLEBRACKET:lE,CLOSEBRACE:lM,CLOSEBRACKET:lO,CLOSEPAREN:lA,COLON:lW,COMMA:lK,DOLLAR:lJ,DOT:"DOT",EMOJI:l7,EQUALS:lU,EXCLAMATION:lG,FULLWIDTHGREATERTHAN:lB,FULLWIDTHLEFTPAREN:lD,FULLWIDTHLESSTHAN:lz,FULLWIDTHMIDDLEDOT:l3,FULLWIDTHRIGHTPAREN:lR,HYPHEN:lY,LEFTCORNERBRACKET:lI,LEFTWHITECORNERBRACKET:lj,LOCALHOST:lv,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:lT,OPENBRACE:lS,OPENBRACKET:lC,OPENPAREN:lN,PERCENT:lQ,PIPE:lX,PLUS:lZ,POUND:l0,QUERY:l1,QUOTE:l2,RIGHTCORNERBRACKET:lP,RIGHTWHITECORNERBRACKET:lL,SCHEME:lk,SEMI:l4,SLASH:l5,SLASH_SCHEME:lx,SYM:"SYM",TILDE:l6,TLD:"TLD",UNDERSCORE:l8,UTLD:lw,UWORD:lg,WORD:lm,WS:"WS"});let ae=/[a-z]/,at=/\p{L}/u,an=/\p{Emoji}/u,ar=/\d/,ai=/\s/,ao=null,as=null;function al(e,t){let n=function(e){let t=[],n=e.length,r=0;for(;r<n;){let i,o=e.charCodeAt(r),s=o<55296||o>56319||r+1===n||(i=e.charCodeAt(r+1))<56320||i>57343?e[r]:e.slice(r,r+2);t.push(s),r+=s.length}return t}(t.replace(/[A-Z]/g,e=>e.toLowerCase())),r=n.length,i=[],o=0,s=0;for(;s<r;){let l=e,a=null,c=0,d=null,h=-1,p=-1;for(;s<r&&(a=l.go(n[s]));)(l=a).accepts()?(h=0,p=0,d=l):h>=0&&(h+=n[s].length,p++),c+=n[s].length,o+=n[s].length,s++;o-=h,s-=p,c-=h,i.push({t:d.t,v:t.slice(o-c,o),s:o-c,e:o})}return i}function aa(e,t,n,r,i){let o,s=t.length;for(let n=0;n<s-1;n++){let s=t[n];e.j[s]?o=e.j[s]:((o=new ld(r)).jr=i.slice(),e.j[s]=o),e=o}return(o=new ld(n)).jr=i.slice(),e.j[t[s-1]]=o,o}function ac(e){let t=[],n=[],r=0;for(;r<e.length;){let i=0;for(;"0123456789".indexOf(e[r+i])>=0;)i++;if(i>0){t.push(n.join(""));for(let t=parseInt(e.substring(r,r+i),10);t>0;t--)n.pop();r+=i}else n.push(e[r]),r++}return t}let ad={defaultProtocol:"http",events:null,format:ap,formatHref:ap,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function ah(e,t=null){let n=le({},ad);e&&(n=le(n,e instanceof ah?e.o:e));let r=n.ignoreTags,i=[];for(let e=0;e<r.length;e++)i.push(r[e].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=i}function ap(e){return e}function au(e,t){this.t="token",this.v=e,this.tk=t}function af(e,t){class n extends au{constructor(t,n){super(t,n),this.t=e}}for(let e in t)n.prototype[e]=t[e];return n.t=e,n}ah.prototype={o:ad,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){let r=null!=t,i=this.o[e];return i&&("object"==typeof i?"function"==typeof(i=n.t in i?i[n.t]:ad[e])&&r&&(i=i(t,n)):"function"==typeof i&&r&&(i=i(t,n.t,n))),i},getObj(e,t,n){let r=this.o[e];return"function"==typeof r&&null!=t&&(r=r(t,n.t,n)),r},render(e){let t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},au.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){let t=this.toString(),n=e.get("truncate",t,this),r=e.get("format",t,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=ad.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){let t=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",t,this),r=e.get("tagName",t,this),i=this.toFormattedString(e),o={},s=e.get("className",t,this),l=e.get("target",t,this),a=e.get("rel",t,this),c=e.getObj("attributes",t,this),d=e.getObj("events",t,this);return o.href=n,s&&(o.class=s),l&&(o.target=l),a&&(o.rel=a),c&&le(o,c),{tagName:r,attributes:o,content:i,eventListeners:d}}};let am=af("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),ag=af("text"),ay=af("nl"),ab=af("url",{isLink:!0,toHref(e=ad.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){let e=this.tk;return e.length>=2&&e[0].t!==lv&&e[1].t===lW}}),av=e=>new ld(e);function aw(e,t,n){let r=n[0].s,i=n[n.length-1].e;return new e(t.slice(r,i),n)}let ak="undefined"!=typeof console&&console&&console.warn||(()=>{}),ax={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function aS(e,t=!1){if(ax.initialized&&ak(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);ax.customSchemes.push([e,t])}function aM(e){return ax.initialized||function(){ax.scanner=function(e=[]){let t={};ld.groups=t;let n=new ld;null==ao&&(ao=ac("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==as&&(as=ac("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),lf(n,"'",lV),lf(n,"{",lS),lf(n,"}",lM),lf(n,"[",lC),lf(n,"]",lO),lf(n,"(",lN),lf(n,")",lA),lf(n,"<",lT),lf(n,">",lE),lf(n,"（",lD),lf(n,"）",lR),lf(n,"「",lI),lf(n,"」",lP),lf(n,"『",lj),lf(n,"』",lL),lf(n,"＜",lz),lf(n,"＞",lB),lf(n,"&",l$),lf(n,"*",lF),lf(n,"@","AT"),lf(n,"`",l_),lf(n,"^",lq),lf(n,":",lW),lf(n,",",lK),lf(n,"$",lJ),lf(n,".","DOT"),lf(n,"=",lU),lf(n,"!",lG),lf(n,"-",lY),lf(n,"%",lQ),lf(n,"|",lX),lf(n,"+",lZ),lf(n,"#",l0),lf(n,"?",l1),lf(n,'"',l2),lf(n,"/",l5),lf(n,";",l4),lf(n,"~",l6),lf(n,"_",l8),lf(n,"\\",lH),lf(n,"・",l3);let r=lp(n,ar,"NUM",{[lt]:!0});lp(r,ar,r);let i=lp(r,ae,ly,{[li]:!0}),o=lp(r,at,lb,{[lo]:!0}),s=lp(n,ae,lm,{[ln]:!0});lp(s,ar,i),lp(s,ae,s),lp(i,ar,i),lp(i,ae,i);let l=lp(n,at,lg,{[lr]:!0});lp(l,ae),lp(l,ar,o),lp(l,at,l),lp(o,ar,o),lp(o,ae),lp(o,at,o);let a=lf(n,"\n","NL",{[la]:!0}),c=lf(n,"\r","WS",{[la]:!0}),d=lp(n,ai,"WS",{[la]:!0});lf(n,"￼",d),lf(c,"\n",a),lf(c,"￼",d),lp(c,ai,d),lf(d,"\r"),lf(d,"\n"),lp(d,ai,d),lf(d,"￼",d);let h=lp(n,an,l7,{[ll]:!0});lf(h,"#"),lp(h,an,h),lf(h,"️",h);let p=lf(h,"‍");lf(p,"#"),lp(p,an,h);let u=[[ae,s],[ar,i]],f=[[ae,null],[at,l],[ar,o]];for(let e=0;e<ao.length;e++)aa(n,ao[e],"TLD",lm,u);for(let e=0;e<as.length;e++)aa(n,as[e],lw,lg,f);lc("TLD",{tld:!0,ascii:!0},t),lc(lw,{utld:!0,alpha:!0},t),aa(n,"file",lk,lm,u),aa(n,"mailto",lk,lm,u),aa(n,"http",lx,lm,u),aa(n,"https",lx,lm,u),aa(n,"ftp",lx,lm,u),aa(n,"ftps",lx,lm,u),lc(lk,{scheme:!0,ascii:!0},t),lc(lx,{slashscheme:!0,ascii:!0},t),e=e.sort((e,t)=>e[0]>t[0]?1:-1);for(let t=0;t<e.length;t++){let r=e[t][0],i=e[t][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?i[ls]=!0:ae.test(r)?ar.test(r)?i[li]=!0:i[ln]=!0:i[lt]=!0,lu(n,r,r,i)}return lu(n,"localhost",lv,{ascii:!0}),n.jd=new ld("SYM"),{start:n,tokens:le({groups:t},l9)}}(ax.customSchemes);for(let e=0;e<ax.tokenQueue.length;e++)ax.tokenQueue[e][1]({scanner:ax.scanner});ax.parser=function({groups:e}){let t=e.domain.concat([l$,lF,"AT",lH,l_,lq,lJ,lU,lY,"NUM",lQ,lX,lZ,l0,l5,"SYM",l6,l8]),n=[lV,lW,lK,"DOT",lG,lQ,l1,l2,l4,lT,lE,lS,lM,lO,lC,lN,lA,lD,lR,lI,lP,lj,lL,lz,lB],r=[l$,lV,lF,lH,l_,lq,lJ,lU,lY,lS,lM,lQ,lX,lZ,l0,l1,l5,"SYM",l6,l8],i=av(),o=lf(i,l6);lh(o,r,o),lh(o,e.domain,o);let s=av(),l=av(),a=av();lh(i,e.domain,s),lh(i,e.scheme,l),lh(i,e.slashscheme,a),lh(s,r,o),lh(s,e.domain,s);let c=lf(s,"AT");lf(o,"AT",c),lf(l,"AT",c),lf(a,"AT",c);let d=lf(o,"DOT");lh(d,r,o),lh(d,e.domain,o);let h=av();lh(c,e.domain,h),lh(h,e.domain,h);let p=lf(h,"DOT");lh(p,e.domain,h);let u=av(am);lh(p,e.tld,u),lh(p,e.utld,u),lf(c,lv,u);let f=lf(h,lY);lf(f,lY,f),lh(f,e.domain,h),lh(u,e.domain,h),lf(u,"DOT",p),lf(u,lY,f),lh(lf(u,lW),e.numeric,am);let m=lf(s,lY),g=lf(s,"DOT");lf(m,lY,m),lh(m,e.domain,s),lh(g,r,o),lh(g,e.domain,s);let y=av(ab);lh(g,e.tld,y),lh(g,e.utld,y),lh(y,e.domain,s),lh(y,r,o),lf(y,"DOT",g),lf(y,lY,m),lf(y,"AT",c);let b=lf(y,lW),v=av(ab);lh(b,e.numeric,v);let w=av(ab),k=av();lh(w,t,w),lh(w,n,k),lh(k,t,w),lh(k,n,k),lf(y,l5,w),lf(v,l5,w);let x=lf(l,lW),S=lf(a,lW),M=lf(S,l5),C=lf(M,l5);lh(l,e.domain,s),lf(l,"DOT",g),lf(l,lY,m),lh(a,e.domain,s),lf(a,"DOT",g),lf(a,lY,m),lh(x,e.domain,w),lf(x,l5,w),lf(x,l1,w),lh(C,e.domain,w),lh(C,t,w),lf(C,l5,w);let O=[[lS,lM],[lC,lO],[lN,lA],[lT,lE],[lD,lR],[lI,lP],[lj,lL],[lz,lB]];for(let e=0;e<O.length;e++){let[r,i]=O[e],o=lf(w,r);lf(k,r,o),lf(o,i,w);let s=av(ab);lh(o,t,s);let l=av();lh(o,n),lh(s,t,s),lh(s,n,l),lh(l,t,s),lh(l,n,l),lf(s,i,w),lf(l,i,w)}return lf(i,lv,y),lf(i,"NL",ay),{start:i,tokens:l9}}(ax.scanner.tokens);for(let e=0;e<ax.pluginQueue.length;e++)ax.pluginQueue[e][1]({scanner:ax.scanner,parser:ax.parser});ax.initialized=!0}(),function(e,t,n){let r=n.length,i=0,o=[],s=[];for(;i<r;){let l=e,a=null,c=null,d=0,h=null,p=-1;for(;i<r&&!(a=l.go(n[i].t));)s.push(n[i++]);for(;i<r&&(c=a||l.go(n[i].t));)a=null,(l=c).accepts()?(p=0,h=l):p>=0&&p++,i++,d++;if(p<0)(i-=d)<r&&(s.push(n[i]),i++);else{s.length>0&&(o.push(aw(ag,t,s)),s=[]),i-=p,d-=p;let e=h.t,r=n.slice(i-d,i);o.push(aw(e,t,r))}}return s.length>0&&o.push(aw(ag,t,s)),o}(ax.parser.start,e,al(ax.scanner.start,e))}function aC(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}let r=new ah(n),i=aM(e),o=[];for(let e=0;e<i.length;e++){let n=i[e];n.isLink&&(!t||n.t===t)&&r.check(n)&&o.push(n.toFormattedObject(r))}return o}aM.scan=al;let aO=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function aN(e,t){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(e=>{let t="string"==typeof e?e:e.scheme;t&&n.push(t)}),!e||e.replace(aO,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let aA=iY.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if("string"==typeof e)return void aS(e);aS(e.scheme,e.optionalSlashes)})},onDestroy(){ld.groups={},ax.scanner=null,ax.parser=null,ax.tokenQueue=[],ax.pluginQueue=[],ax.customSchemes=[],ax.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!aN(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{let t=e.getAttribute("href");return!!t&&!!this.options.isAllowedUri(t,{defaultValidate:e=>!!aN(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!aN(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",iI(this.options.HTMLAttributes,e),0]:["a",iI(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!aN(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!aN(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[oH({find:e=>{let t=[];if(e){let{protocols:n,defaultProtocol:r}=this.options,i=aC(e).filter(e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!aN(e,n),protocols:n,defaultProtocol:r}));i.length&&i.forEach(e=>t.push({text:e.value,data:{href:e.href},index:e.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:null==(t=e.data)?void 0:t.href}}})]},addProseMirrorPlugins(){var e,t,n;let r=[],{protocols:i,defaultProtocol:o}=this.options;return this.options.autolink&&r.push((e={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!aN(e,i),protocols:i,defaultProtocol:o}),shouldAutoLink:this.options.shouldAutoLink},new tl({key:new td("autolink"),appendTransaction:(t,n,r)=>{let i=t.some(e=>e.docChanged)&&!n.doc.eq(r.doc),o=t.some(e=>e.getMeta("preventAutolink"));if(!i||o)return;let{tr:s}=r;if((function(e){let{mapping:t,steps:n}=e,r=[];t.maps.forEach((e,i)=>{let o=[];if(e.ranges.length)e.forEach((e,t)=>{o.push({from:e,to:t})});else{let{from:e,to:t}=n[i];if(void 0===e||void 0===t)return;o.push({from:e,to:t})}o.forEach(({from:e,to:n})=>{let o=t.slice(i).map(e,-1),s=t.slice(i).map(n),l=t.invert().map(o,-1),a=t.invert().map(s);r.push({oldRange:{from:l,to:a},newRange:{from:o,to:s}})})});let i=function(e,t=JSON.stringify){let n={};return e.filter(e=>{let r=t(e);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)})}(r);return 1===i.length?i:i.filter((e,t)=>!i.filter((e,n)=>n!==t).some(t=>e.oldRange.from>=t.oldRange.from&&e.oldRange.to<=t.oldRange.to&&e.newRange.from>=t.newRange.from&&e.newRange.to<=t.newRange.to))})(function(e,t){let n=new eQ(e);return t.forEach(e=>{e.steps.forEach(e=>{n.step(e)})}),n}(n.doc,[...t])).forEach(({newRange:t})=>{let n,i,o=function(e,t,n){let r=[];return e.nodesBetween(t.from,t.to,(e,t)=>{n(e)&&r.push({node:e,pos:t})}),r}(r.doc,t,e=>e.isTextblock);if(o.length>1?(n=o[0],i=r.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ")):o.length&&r.doc.textBetween(t.from,t.to," "," ").endsWith(" ")&&(n=o[0],i=r.doc.textBetween(n.pos,t.to,void 0," ")),n&&i){let t=i.split(" ").filter(e=>""!==e);if(t.length<=0)return!1;let o=t[t.length-1],l=n.pos+i.lastIndexOf(o);if(!o)return!1;let a=aM(o).map(t=>t.toObject(e.defaultProtocol));if(!(1===a.length?a[0].isLink:3===a.length&&!!a[1].isLink&&["()","[]"].includes(a[0].value+a[2].value)))return!1;a.filter(e=>e.isLink).map(e=>({...e,from:l+e.start+1,to:l+e.end+1})).filter(e=>!r.schema.marks.code||!r.doc.rangeHasMark(e.from,e.to,r.schema.marks.code)).filter(t=>e.validate(t.value)).filter(t=>e.shouldAutoLink(t.value)).forEach(t=>{ob(t.from,t.to,r.doc).some(t=>t.mark.type===e.type)||s.addMark(t.from,t.to,e.type.create({href:t.href}))})}}),s.steps.length)return s}}))),!0===this.options.openOnClick&&r.push((t={type:this.type},new tl({key:new td("handleClickLink"),props:{handleClick:(e,n,r)=>{var i,o;if(0!==r.button||!e.editable)return!1;let s=r.target,l=[];for(;"DIV"!==s.nodeName;)l.push(s),s=s.parentNode;if(!l.find(e=>"A"===e.nodeName))return!1;let a=oy(e.state,t.type.name),c=r.target,d=null!=(i=null==c?void 0:c.href)?i:a.href,h=null!=(o=null==c?void 0:c.target)?o:a.target;return!!c&&!!d&&(window.open(d,h),!0)}}}))),this.options.linkOnPaste&&r.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new tl({key:new td("handlePasteLink"),props:{handlePaste:(e,t,r)=>{let{state:i}=e,{selection:o}=i,{empty:s}=o;if(s)return!1;let l="";r.content.forEach(e=>{l+=e.textContent});let a=aC(l,{defaultProtocol:n.defaultProtocol}).find(e=>e.isLink&&e.value===l);return!!l&&!!a&&n.editor.commands.setMark(n.type,{href:a.href})}}}))),r}}),aT=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,aE=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,aD=iY.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:e=>e.getAttribute("data-color")||e.style.backgroundColor,renderHTML:e=>e.color?{"data-color":e.color,style:`background-color: ${e.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:e}){return["mark",iI(this.options.HTMLAttributes,e),0]},addCommands(){return{setHighlight:e=>({commands:t})=>t.setMark(this.name,e),toggleHighlight:e=>({commands:t})=>t.toggleMark(this.name,e),unsetHighlight:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[oB({find:aT,type:this.type})]},addPasteRules(){return[oH({find:aE,type:this.type})]}});var aR=n(27777),aI=n(11922),aP=n(44692),aj=n(45984),aL=n(69169),az=n(21782),aB=n(25366),a$=n(14290),aV=n(98916),aF=n(80375),aH=n(47342),a_=n(54388),aq=n(31110);n(28947);var aW=n(13861);n(53411),n(15574),n(48730);var aK=n(5336),aJ=n(93613),aU=n(35071),aG=n(25541),aY=n(79216);function aQ({content:e,targetKeyword:t,metaDescription:n,title:r,wordCount:i}){let[o,a]=(0,l.useState)(0),[d,h]=(0,l.useState)([]),p=e=>e>=80?"text-green-400":e>=60?"text-yellow-400":"text-red-400",u=e=>e>=80?"from-green-500 to-emerald-500":e>=60?"from-yellow-500 to-orange-500":"from-red-500 to-pink-500",f=e=>{switch(e){case"good":return(0,s.jsx)(aK.A,{className:"w-4 h-4 text-green-400"});case"warning":return(0,s.jsx)(aJ.A,{className:"w-4 h-4 text-yellow-400"});case"error":return(0,s.jsx)(aU.A,{className:"w-4 h-4 text-red-400"});default:return null}};return(0,s.jsxs)(aY.A,{variant:"glass",className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"relative w-24 h-24 mx-auto mb-4",children:[(0,s.jsxs)("svg",{className:"w-24 h-24 transform -rotate-90",viewBox:"0 0 100 100",children:[(0,s.jsx)("circle",{cx:"50",cy:"50",r:"40",stroke:"rgba(255, 255, 255, 0.1)",strokeWidth:"8",fill:"none"}),(0,s.jsx)(c.P.circle,{cx:"50",cy:"50",r:"40",stroke:"url(#gradient)",strokeWidth:"8",fill:"none",strokeLinecap:"round",strokeDasharray:`${2*Math.PI*40}`,initial:{strokeDashoffset:2*Math.PI*40},animate:{strokeDashoffset:2*Math.PI*40*(1-o/100)},transition:{duration:1,ease:"easeOut"}}),(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,s.jsx)("stop",{offset:"0%",className:`stop-color-${u(o).split(" ")[0].replace("from-","")}`}),(0,s.jsx)("stop",{offset:"100%",className:`stop-color-${u(o).split(" ")[1].replace("to-","")}`})]})})]}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("span",{className:`text-2xl font-bold ${p(o)}`,children:o})})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-1",children:"SEO Score"}),(0,s.jsx)("p",{className:"text-white/60 text-sm",children:o>=80?"Excellent":o>=60?"Good":"Needs Improvement"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white/80 uppercase tracking-wider",children:"SEO Analysis"}),d.map((e,t)=>(0,s.jsxs)(c.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"flex items-start gap-3 p-3 rounded-xl bg-white/5 hover:bg-white/10 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[e.icon,f(e.status)]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-white",children:e.label}),(0,s.jsxs)("span",{className:`text-xs font-medium ${p(e.score)}`,children:[e.score,"%"]})]}),(0,s.jsx)("p",{className:"text-xs text-white/60 leading-relaxed",children:e.message})]})]},e.id))]}),(0,s.jsxs)("div",{className:"pt-4 border-t border-white/10",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white/80 mb-3",children:"Quick Tips"}),(0,s.jsxs)("div",{className:"space-y-2 text-xs text-white/60",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(aG.A,{className:"w-3 h-3 text-blue-400"}),(0,s.jsx)("span",{children:"Use your target keyword naturally throughout the content"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"w-3 h-3 text-purple-400"}),(0,s.jsx)("span",{children:"Structure content with clear headings (H1, H2, H3)"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(aW.A,{className:"w-3 h-3 text-green-400"}),(0,s.jsx)("span",{children:"Write compelling meta descriptions to improve CTR"})]})]})]})]})}let aX={base:"max-w-none",enhanced:"prose-headings:font-bold prose-headings:text-inherit prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-p:text-white/90 prose-p:leading-relaxed prose-p:mb-6 prose-p:text-left prose-p:tracking-wide prose-strong:text-white prose-strong:font-semibold prose-em:text-orange-200 prose-code:text-yellow-200 prose-code:bg-orange-500/15 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:border prose-code:border-orange-500/30 prose-blockquote:border-l-4 prose-blockquote:border-blue-400 prose-blockquote:text-blue-100 prose-blockquote:bg-blue-500/10 prose-blockquote:pl-4 prose-blockquote:py-2 prose-a:text-orange-200 prose-a:no-underline hover:prose-a:text-orange-300 hover:prose-a:underline prose-ul:text-white/90 prose-ul:my-4 prose-ul:space-y-0 prose-ol:text-white/90 prose-ol:my-4 prose-ol:space-y-0 prose-li:text-white/90 prose-li:my-0 prose-li:leading-relaxed prose-li:mb-1 prose-pre:bg-gray-900/50 prose-pre:border prose-pre:border-white/20"};function aZ({content:e="",onChange:t,placeholder:n="Start writing your content...",className:r="",showSEOMeter:i=!0,targetKeyword:o="",metaDescription:a="",title:d=""}){let[h,p]=(0,l.useState)([]),u=function(e={},t=[]){let n=(0,l.useRef)(e);n.current=e;let[r]=(0,l.useState)(()=>new o5(n)),i=oW.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,l.useDebugValue)(i),(0,l.useEffect)(r.onRender(t)),!function(e){var t;let[n]=(0,l.useState)(()=>new o2(e.editor)),r=o0.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!=(t=e.equalityFn)?t:oQ);o1(()=>n.watch(e.editor),[e.editor,n]),(0,l.useDebugValue)(r)}({editor:i,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),i}({extensions:[s8.configure({bulletList:{keepMarks:!0,keepAttributes:!1,HTMLAttributes:{class:"tight-list"}},orderedList:{keepMarks:!0,keepAttributes:!1,HTMLAttributes:{class:"tight-list"}},listItem:{HTMLAttributes:{class:"tight-list-item"}}}),s7.configure({placeholder:n}),s9.configure({limit:1e4}),aA.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-400 hover:text-blue-300 underline"}}),aD.configure({multicolor:!0})],content:e&&[/^#{1,6}\s+/m,/\*\*.*?\*\*/,/\*.*?\*/,/^\s*[-*+]\s+/m,/^\s*\d+\.\s+/m,/```[\s\S]*?```/,/`.*?`/,/^\s*>\s+/m,/\[.*?\]\(.*?\)/,/!\[.*?\]\(.*?\)/].some(t=>t.test(e))?function(e){if(!e)return"";try{let t=e,n=[];t=t.replace(/```([\s\S]*?)```/g,(e,t)=>{let r=n.length;return n.push(`<pre><code>${t.trim()}</code></pre>`),`__CODE_BLOCK_${r}__`});let r=[],i=(t=(t=(t=(t=(t=t.replace(/`([^`]+)`/g,(e,t)=>{let n=r.length;return r.push(`<code>${t}</code>`),`__INLINE_CODE_${n}__`})).replace(/^#### (.*$)/gim,"<h4>$1</h4>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>")).replace(/\*\*\*(.*?)\*\*\*/g,"<strong><em>$1</em></strong>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/__(.*?)__/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/_(.*?)_/g,"<em>$1</em>")).replace(/!\[([^\]]*)\]\(([^)]+)\)/g,'<img src="$2" alt="$1" />').replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')).replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>")).split("\n"),o=[],s=!1,l="";for(let e=0;e<i.length;e++){let t=i[e].trim();t.match(/^[-*+] /)?(s&&"ul"===l||(s&&o.push(`</${l}>`),o.push("<ul>"),s=!0,l="ul"),o.push(`<li>${t.replace(/^[-*+] /,"")}</li>`)):t.match(/^\d+\. /)?(s&&"ol"===l||(s&&o.push(`</${l}>`),o.push("<ol>"),s=!0,l="ol"),o.push(`<li>${t.replace(/^\d+\. /,"")}</li>`)):(s&&(o.push(`</${l}>`),s=!1,l=""),o.push(t))}s&&o.push(`</${l}>`);let a=(t=o.join("\n")).split("\n"),c=[],d=!1;for(let e=0;e<a.length;e++){let t=a[e].trim();if(!t){d&&(c.push("</p>"),d=!1);continue}t.match(/^<(h[1-6]|ul|ol|li|blockquote|\/ul|\/ol|\/blockquote)/)?d&&(c.push("</p>"),d=!1):d||(c.push("<p>"),d=!0),c.push(t)}return d&&c.push("</p>"),t=(t=c.join("\n")).replace(/<p><\/p>/g,"").replace(/\n+/g,"\n").replace(/\n/g," ").replace(/<\/p>\s*<p>/g,"</p>\n<p>").replace(/<\/(h[1-6]|ul|ol|blockquote)>\s*<p>/g,"</$1>\n<p>").replace(/<\/(h[1-6]|ul|ol|blockquote)>/g,"</$1>\n").replace(/<(h[1-6]|ul|ol|blockquote)/g,"\n<$1").trim(),n.forEach((e,n)=>{t=t.replace(`__CODE_BLOCK_${n}__`,e)}),r.forEach((e,n)=>{t=t.replace(`__INLINE_CODE_${n}__`,e)}),t}catch(t){return console.error("Error converting markdown to HTML:",t),e}}(e):e,onUpdate:({editor:e})=>{let n=e.getHTML();t?.(n);let r=[];e.isActive("bold")&&r.push("bold"),e.isActive("italic")&&r.push("italic"),e.isActive("underline")&&r.push("underline"),e.isActive("bulletList")&&r.push("bulletList"),e.isActive("orderedList")&&r.push("orderedList"),e.isActive("blockquote")&&r.push("blockquote"),e.isActive("code")&&r.push("code"),e.isActive("link")&&r.push("link"),e.isActive("heading",{level:1})&&r.push("h1"),e.isActive("heading",{level:2})&&r.push("h2"),e.isActive("heading",{level:3})&&r.push("h3"),p(r)},editorProps:{attributes:{class:`${function(e=!0){return e?`${aX.base} ${aX.enhanced}`:aX.enhanced}()} focus:outline-none vibrant-editor-theme`,"data-tiptap-editor":"true","data-editor-theme":"vibrant-red-orange"}}});if(!u)return(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-12 bg-white/10 rounded-xl mb-4"}),(0,s.jsx)("div",{className:"h-96 bg-white/5 rounded-2xl"})]});let f=({onClick:e,isActive:t,disabled:n=!1,children:r,title:i})=>(0,s.jsx)(c.P.button,{whileHover:n?{}:{scale:1.05},whileTap:n?{}:{scale:.95},onClick:e,title:i,disabled:n,className:`
        toolbar-button
        ${t?"is-active":""}
      `,children:r}),m=u.storage.characterCount.words(),g=u.storage.characterCount.characters();return(0,s.jsxs)("div",{className:`space-y-6 ${r}`,children:[(0,s.jsxs)(c.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-gradient-to-r from-white/10 via-white/5 to-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-white/10",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-400"}),(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-400"}),(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-400"}),(0,s.jsx)("span",{className:"text-white/70 text-sm font-medium ml-4",children:"Content Editor"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"text-white/60 text-sm",children:[u.storage.characterCount.characters()," characters"]}),(0,s.jsx)("div",{className:"w-px h-4 bg-white/20"}),(0,s.jsxs)("div",{className:"text-white/60 text-sm",children:[u.storage.characterCount.words()," words"]})]})]}),(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-wrap",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleBold().run(),isActive:h.includes("bold"),title:"Bold",children:(0,s.jsx)(aR.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleItalic().run(),isActive:h.includes("italic"),title:"Italic",children:(0,s.jsx)(aI.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleStrike().run(),isActive:u.isActive("strike"),title:"Strikethrough",children:(0,s.jsx)(aP.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleHeading({level:1}).run(),isActive:h.includes("h1"),title:"Heading 1",children:(0,s.jsx)(aj.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleHeading({level:2}).run(),isActive:h.includes("h2"),title:"Heading 2",children:(0,s.jsx)(aL.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleHeading({level:3}).run(),isActive:h.includes("h3"),title:"Heading 3",children:(0,s.jsx)(az.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleBulletList().run(),isActive:h.includes("bulletList"),title:"Bullet List",children:(0,s.jsx)(aB.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleOrderedList().run(),isActive:h.includes("orderedList"),title:"Numbered List",children:(0,s.jsx)(a$.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleBlockquote().run(),isActive:h.includes("blockquote"),title:"Quote",children:(0,s.jsx)(aV.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().toggleCode().run(),isActive:h.includes("code"),title:"Inline Code",children:(0,s.jsx)(aF.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>{let e=window.prompt("Enter URL:");e&&u.chain().focus().setLink({href:e}).run()},isActive:h.includes("link"),title:"Add Link",children:(0,s.jsx)(aH.A,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/5 rounded-lg p-1 border border-white/10",children:[(0,s.jsx)(f,{onClick:()=>u.chain().focus().undo().run(),disabled:!u.can().undo(),title:"Undo",children:(0,s.jsx)(a_.A,{className:"w-4 h-4"})}),(0,s.jsx)(f,{onClick:()=>u.chain().focus().redo().run(),disabled:!u.can().redo(),title:"Redo",children:(0,s.jsx)(aq.A,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:"flex-1"}),(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{className:"text-sm text-white/60",children:[m," words • ",g," characters"]})})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)(c.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:`${i?"lg:col-span-2":"lg:col-span-3"}`,children:(0,s.jsxs)("div",{className:"bg-gradient-to-br from-white/10 via-white/5 to-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 border-b border-white/10",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-blue-400 animate-pulse"}),(0,s.jsx)("span",{className:"text-white/70 text-sm font-medium",children:"Edit Mode"})]}),(0,s.jsx)("div",{className:"text-white/50 text-xs",children:"Auto-save enabled"})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)("div",{className:"relative","data-tiptap-editor":"true",children:[(0,s.jsx)(oY,{editor:u}),(0,s.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black/50 backdrop-blur-lg rounded-lg px-3 py-1 text-xs text-white/70",children:[m," words"]})]})})]})}),i&&(0,s.jsx)(c.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"lg:col-span-1",children:(0,s.jsx)(aQ,{content:u.getHTML(),targetKeyword:o,metaDescription:a,title:d,wordCount:m})})]})]})}var a0=n(81822),a1=n(14487);function a2(){let e=(0,a.useRouter)();(0,a.useSearchParams)();let[t,n]=(0,l.useState)(""),[r,i]=(0,l.useState)(""),[o,y]=(0,l.useState)(""),[b,v]=(0,l.useState)(""),[w,k]=(0,l.useState)(""),[x,S]=(0,l.useState)(!1),[M,C]=(0,l.useState)(""),[O,N]=(0,l.useState)(""),A=async()=>{S(!0),C("");try{await new Promise(e=>setTimeout(e,1e3)),C("Content saved successfully!"),setTimeout(()=>C(""),3e3)}catch(e){N("Failed to save content")}finally{S(!1)}},T=async()=>{try{await navigator.clipboard.writeText(t),C("Content copied to clipboard!"),setTimeout(()=>C(""),3e3)}catch(e){N("Failed to copy content")}},E=()=>{e.push("/blog")};return(0,s.jsx)(g.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)(c.P.div,{className:"mb-6",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(a0.A,{variant:"secondary",size:"sm",onClick:E,icon:(0,s.jsx)(d.A,{className:"w-4 h-4"}),children:"Back to Generator"}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600",children:(0,s.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Blog Editor"}),(0,s.jsx)("p",{className:"text-white/70",children:"Edit and refine your generated content"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(a0.A,{variant:"secondary",size:"sm",onClick:T,icon:(0,s.jsx)(p.A,{className:"w-4 h-4"}),children:"Copy"}),(0,s.jsx)(a0.A,{variant:"secondary",size:"sm",onClick:()=>{let e=new Blob([t],{type:"text/markdown"}),n=URL.createObjectURL(e),i=document.createElement("a");i.href=n,i.download=`blog-post-${r.replace(/\s+/g,"-").toLowerCase()||"untitled"}.md`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n)},icon:(0,s.jsx)(u.A,{className:"w-4 h-4"}),children:"Download"}),(0,s.jsx)(a0.A,{variant:"primary",size:"sm",onClick:A,loading:x,icon:(0,s.jsx)(f.A,{className:"w-4 h-4"}),children:x?"Saving...":"Save"})]})]}),(0,s.jsx)(a1.A,{type:"error",message:O,show:!!O,onClose:()=>N(""),autoClose:!0}),M&&(0,s.jsx)(a1.A,{type:"success",message:M,show:!!M,onClose:()=>C(""),autoClose:!0})]}),(0,s.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:t?(0,s.jsx)(aZ,{content:t,onChange:n,placeholder:"Start writing your blog post...",showSEOMeter:!0,targetKeyword:o,metaDescription:"",title:r}):(0,s.jsxs)(aY.A,{variant:"glass",className:"text-center py-16",children:[(0,s.jsx)(m.A,{className:"w-16 h-16 text-white/40 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white/80 mb-2",children:"No content to edit"}),(0,s.jsx)("p",{className:"text-white/60 mb-6",children:"Generate content from the blog generator to start editing"}),(0,s.jsx)(a0.A,{variant:"primary",onClick:E,icon:(0,s.jsx)(d.A,{className:"w-4 h-4"}),children:"Go to Blog Generator"})]})})]})})}function a3(){return(0,s.jsx)(l.Suspense,{fallback:(0,s.jsx)(g.A,{children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-white/70",children:"Loading editor..."})]})})})}),children:(0,s.jsx)(a2,{})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11922:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},13861:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14185:()=>{},14290:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},14487:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var r=n(60687),i=n(88920),o=n(97905),s=n(5336),l=n(35071),a=n(43649);let c=(0,n(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var d=n(11860);function h({type:e,title:t,message:n,show:h,onClose:p,autoClose:u=!1,duration:f=5e3,icon:m}){let g={success:(0,r.jsx)(s.A,{className:"w-5 h-5"}),error:(0,r.jsx)(l.A,{className:"w-5 h-5"}),warning:(0,r.jsx)(a.A,{className:"w-5 h-5"}),info:(0,r.jsx)(c,{className:"w-5 h-5"})};return u&&h&&p&&setTimeout(()=>{p()},f),(0,r.jsx)(i.N,{children:h&&(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3},className:`alert-modern ${{success:"alert-modern-success",error:"alert-modern-error",warning:"alert-modern-warning",info:"alert-modern-info"}[e]}`,children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:m||g[e]}),(0,r.jsxs)("div",{className:"flex-1",children:[t&&(0,r.jsx)("h4",{className:"font-semibold mb-1",children:t}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:n})]}),p&&(0,r.jsx)("button",{onClick:p,className:"flex-shrink-0 ml-4 p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})})]})})}},15574:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21782:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("heading-3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},25366:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},25541:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27777:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},28559:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31110:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},31158:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},31934:(e,t,n)=>{Promise.resolve().then(n.bind(n,90079))},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},43649:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44692:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]])},45984:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("heading-1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},46534:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=n(65239),i=n(48088),o=n(88170),s=n.n(o),l=n(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);n.d(t,a);let c={children:["",{children:["blog",{children:["editor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,90079)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog/editor/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog/editor/page.tsx"],h={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/blog/editor/page",pathname:"/blog/editor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},47342:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48730:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},54388:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69169:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("heading-2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},70615:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},72102:(e,t,n)=>{Promise.resolve().then(n.bind(n,8954))},80375:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},90079:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog/editor/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/blog/editor/page.tsx","default")},93613:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},98916:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,135,951,15,141,556],()=>n(46534));module.exports=r})();