(()=>{var e={};e.id=555,e.ids=[555],e.modules={1188:(e,t,a)=>{"use strict";a.d(t,{D:()=>n,ThemeProvider:()=>o});var s=a(60687),l=a(43210),i=a(22362);let r=(0,l.createContext)(void 0),n=()=>{let e=(0,l.useContext)(r);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},o=({children:e})=>{let{settings:t,updateSettings:a}=(0,i.t)(),n="auto"===t.theme?"dark":t.theme;(0,l.useEffect)(()=>{},[n,t.accentColor,t.animationsEnabled,t.compactMode]),(0,l.useEffect)(()=>{t.theme},[t.theme]);let o={theme:n,accentColor:t.accentColor,animationsEnabled:t.animationsEnabled,compactMode:t.compactMode,setTheme:e=>{a({theme:e})},setAccentColor:e=>{a({accentColor:e})},toggleAnimations:()=>{a({animationsEnabled:!t.animationsEnabled})},toggleCompactMode:()=>{a({compactMode:!t.compactMode})}};return(0,s.jsx)(r.Provider,{value:o,children:e})}},2364:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=a(65239),l=a(48088),i=a(88170),r=a.n(i),n=a(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let d={children:["",{children:["superagent",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,75975)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/superagent/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/superagent/page.tsx"],h={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/superagent/page",pathname:"/superagent",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7036:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},8819:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10022:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11922:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14290:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},15773:(e,t,a)=>{Promise.resolve().then(a.bind(a,19864)),Promise.resolve().then(a.bind(a,68462))},15807:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},16764:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},18172:(e,t,a)=>{Promise.resolve().then(a.bind(a,28182))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19864:(e,t,a)=>{"use strict";a.d(t,{SettingsProvider:()=>l});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","useSettings");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","SettingsProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","default")},21782:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("heading-3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},22362:(e,t,a)=>{"use strict";a.d(t,{SettingsProvider:()=>o,t:()=>n});var s=a(60687),l=a(43210);let i={firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0},r=(0,l.createContext)(void 0),n=()=>{let e=(0,l.useContext)(r);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},o=({children:e})=>{let[t,a]=(0,l.useState)(i),[n,o]=(0,l.useState)(!0),[d,c]=(0,l.useState)(null);(0,l.useEffect)(()=>{try{let e=localStorage.getItem("userSettings");if(e){let t=JSON.parse(e);a({...i,...t})}}catch(e){console.error("Failed to load settings:",e),c("Failed to load user settings")}finally{o(!1)}},[]),(0,l.useEffect)(()=>{if(!n)try{localStorage.setItem("userSettings",JSON.stringify(t))}catch(e){console.error("Failed to save settings:",e),c("Failed to save settings")}},[t,n]);let h=async()=>{try{c(null);let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await e.json();if(!e.ok)throw Error(a.error||"Failed to save settings")}catch(t){let e=t instanceof Error?t.message:"Failed to save settings";throw c(e),Error(e)}};return(0,s.jsx)(r.Provider,{value:{settings:t,updateSettings:e=>{a(t=>({...t,...e})),c(null)},resetSettings:()=>{a(i),c(null)},saveSettings:h,isLoading:n,error:d},children:e})}},25366:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},27777:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},28182:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>el});var s=a(60687),l=a(43210),i=a(97905),r=a(88920),n=a(78200),o=a(99270),d=a(53411),c=a(62688);let h=(0,c.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var m=a(10022),x=a(56085),u=a(28947),p=a(99891),b=a(15807),g=a(84027);let y=(0,c.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]),v=(0,c.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var f=a(78272),w=a(45583);let j=(0,c.A)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]);var N=a(84821);let k=(0,c.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var A=a(64398),C=a(5336),M=a(93613),P=a(61611),S=a(48730),T=a(70615),E=a(31158),z=a(27777),q=a(11922),R=a(44692);let H=(0,c.A)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]),F=(0,c.A)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]]),L=(0,c.A)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]]),O=(0,c.A)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]]);var I=a(25366),V=a(14290),$=a(98916),D=a(80375),U=a(54388),W=a(31110),_=a(45984),B=a(69169),G=a(21782),Q=a(98971),J=a(63143),K=a(13861);let Z=(0,c.A)("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]),X=(0,c.A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]),Y=(0,c.A)("highlighter",[["path",{d:"m9 11-6 6v3h9l3-3",key:"1a3l36"}],["path",{d:"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4",key:"14a9rk"}]]);var ee=a(47342),et=a(69282),ea=a(8819);function es({content:e,onChange:t,onSEOAnalysis:a,className:n}){let[d,c]=(0,l.useState)(e),[h,x]=(0,l.useState)(!1),[u,p]=(0,l.useState)(!1),[b,g]=(0,l.useState)(""),[y,v]=(0,l.useState)(!1),[f,j]=(0,l.useState)(!1),[N,k]=(0,l.useState)(null),[A,C]=(0,l.useState)(0),[M,P]=(0,l.useState)(0),S=(0,l.useRef)(null),E=(0,l.useRef)(null),es=(0,l.useCallback)(e=>{let t=e.split(/[.!?]+/).filter(e=>e.trim().length>0),a=e.trim().split(/\s+/).length/t.length,s=100;return a>20&&(s-=20),a>25&&(s-=20),Math.max(0,s)},[]),el=(0,l.useCallback)(e=>{let t={};e.forEach(e=>{let a=e.toLowerCase().replace(/[^a-z]/g,"");a.length>3&&(t[a]=(t[a]||0)+1)});let a=Object.values(t);return 0===a.length?0:Math.min(100,20*(Math.max(...a)/e.length*100))},[]),ei=(0,l.useCallback)((e,t,a,s)=>{let l=[];return t<300&&l.push("Consider adding more content (aim for 300+ words)"),t>2e3&&l.push("Content might be too long, consider breaking into sections"),a<2&&l.push("Add more headings to improve structure"),s<2&&l.push("Add relevant internal and external links"),e<60&&l.push("Optimize content for better SEO performance"),l},[]);(0,l.useCallback)(e=>{let t=e.replace(/<[^>]*>/g,""),a=t.trim().split(/\s+/),s=a.length,l=(e.match(/<h[1-6][^>]*>/gi)||[]).length,i=(e.match(/<a[^>]*>/gi)||[]).length,r=(e.match(/<img[^>]*>/gi)||[]).length,n=Math.min(100,s/1500*100),o=Math.min(100,l/5*100),d=es(t),c=el(a),h=Math.round(.3*n+.2*o+.3*d+.2*c);return{score:h,readabilityScore:d,keywordDensity:c,headingStructure:o,metaOptimization:75,contentLength:n,internalLinks:Math.floor(.7*i),externalLinks:Math.floor(.3*i),imageOptimization:Math.min(100,20*r),suggestions:ei(h,s,l,i)}},[es,el,ei]);let er=(e,a)=>{if(document.execCommand(e,!1,a),S.current){let e=S.current.innerHTML;c(e),t(e)}},en=()=>{let e=window.getSelection();e&&e.toString()&&g(e.toString())},eo=[{icon:z.A,command:"bold",tooltip:"Bold"},{icon:q.A,command:"italic",tooltip:"Italic"},{icon:R.A,command:"underline",tooltip:"Underline"},{icon:H,command:"strikeThrough",tooltip:"Strikethrough"},{icon:F,command:"justifyLeft",tooltip:"Align Left"},{icon:L,command:"justifyCenter",tooltip:"Align Center"},{icon:O,command:"justifyRight",tooltip:"Align Right"},{icon:I.A,command:"insertUnorderedList",tooltip:"Bullet List"},{icon:V.A,command:"insertOrderedList",tooltip:"Numbered List"},{icon:$.A,command:"formatBlock",value:"blockquote",tooltip:"Quote"},{icon:D.A,command:"formatBlock",value:"pre",tooltip:"Code Block"},{icon:U.A,command:"undo",tooltip:"Undo"},{icon:W.A,command:"redo",tooltip:"Redo"}];return(0,s.jsx)("div",{className:`${n||""} ${u?"fixed inset-0 z-50 bg-gray-900":"relative"}`,children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-white/5 backdrop-blur-sm border-b border-white/20 p-4",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:eo.slice(0,4).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>er(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:eo.slice(4,7).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>er(e.command),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:eo.slice(7,9).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>er(e.command),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:[{icon:_.A,command:"formatBlock",value:"h1",tooltip:"Heading 1"},{icon:B.A,command:"formatBlock",value:"h2",tooltip:"Heading 2"},{icon:G.A,command:"formatBlock",value:"h3",tooltip:"Heading 3"}].map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>er(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>v(!y),className:"p-2 rounded-lg bg-white/10 hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:"Text Color",children:(0,s.jsx)(Q.A,{className:"h-4 w-4"})}),(0,s.jsx)(r.N,{children:y&&(0,s.jsx)(i.P.div,{ref:E,initial:{opacity:0,scale:.9,y:-10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:-10},className:"absolute top-12 left-0 bg-white/20 backdrop-blur-xl rounded-xl p-3 border border-white/30 shadow-2xl z-10",children:(0,s.jsx)("div",{className:"grid grid-cols-6 gap-2",children:["#000000","#333333","#666666","#999999","#CCCCCC","#FFFFFF","#FF0000","#FF6600","#FFCC00","#33FF00","#0099FF","#6600FF","#FF0099","#FF3366","#FF6699","#66FF99","#99CCFF","#CC99FF"].map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{er("foreColor",e),v(!1)},className:"w-6 h-6 rounded-full border-2 border-white/30 hover:border-white/60 transition-all duration-200",style:{backgroundColor:e}},t))})})})]}),(0,s.jsx)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1",children:eo.slice(9).map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>er(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:e.tooltip,children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-white/10 rounded-xl p-1 ml-auto",children:[(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>x(!h),className:`p-2 rounded-lg transition-all duration-200 ${h?"bg-purple-500 text-white":"hover:bg-white/20 text-white/80 hover:text-white"}`,title:h?"Edit Mode":"Preview Mode",children:h?(0,s.jsx)(J.A,{className:"h-4 w-4"}):(0,s.jsx)(K.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>p(!u),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",title:u?"Exit Fullscreen":"Fullscreen",children:u?(0,s.jsx)(Z,{className:"h-4 w-4"}):(0,s.jsx)(X,{className:"h-4 w-4"})})]})]})}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[h?(0,s.jsx)("div",{className:"p-8 min-h-[500px] text-white",children:(0,s.jsx)("div",{className:"prose prose-lg prose-invert max-w-none",dangerouslySetInnerHTML:{__html:d}})}):(0,s.jsx)("div",{ref:S,contentEditable:!0,suppressContentEditableWarning:!0,onInput:e=>{let a=e.currentTarget.innerHTML;c(a),t(a)},onMouseUp:en,onKeyUp:en,className:"p-8 min-h-[500px] text-white focus:outline-none",style:{fontSize:"16px",lineHeight:"1.6",fontFamily:"Inter, system-ui, sans-serif"},dangerouslySetInnerHTML:{__html:d}}),(0,s.jsx)(r.N,{children:b&&!h&&(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:.9,y:10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:10},className:"absolute bg-gray-800/90 backdrop-blur-xl rounded-xl p-2 border border-white/20 shadow-2xl z-20",style:{top:"50%",left:"50%",transform:"translate(-50%, -50%)"},children:(0,s.jsx)("div",{className:"flex items-center gap-1",children:[{icon:z.A,command:"bold"},{icon:q.A,command:"italic"},{icon:R.A,command:"underline"},{icon:Y,command:"hiliteColor",value:"#ffff00"},{icon:ee.A,command:"createLink",value:prompt("Enter URL:")||""}].map((e,t)=>(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>er(e.command,e.value),className:"p-2 rounded-lg hover:bg-white/20 text-white/80 hover:text-white transition-all duration-200",children:(0,s.jsx)(e.icon,{className:"h-4 w-4"})},t))})})})]}),N&&(0,s.jsx)("div",{className:"w-80 bg-white/5 backdrop-blur-sm border-l border-white/20 p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-white mb-4",children:"SEO Analysis"}),(0,s.jsxs)("div",{className:"relative w-32 h-32 mx-auto",children:[(0,s.jsxs)("svg",{className:"w-32 h-32 transform -rotate-90",viewBox:"0 0 120 120",children:[(0,s.jsx)("circle",{cx:"60",cy:"60",r:"50",stroke:"rgba(255,255,255,0.1)",strokeWidth:"8",fill:"none"}),(0,s.jsx)(i.P.circle,{cx:"60",cy:"60",r:"50",stroke:N.score>=80?"#10B981":N.score>=60?"#F59E0B":"#EF4444",strokeWidth:"8",fill:"none",strokeLinecap:"round",initial:{strokeDasharray:0,strokeDashoffset:0},animate:{strokeDasharray:314,strokeDashoffset:314-314*N.score/100},transition:{duration:1.5,ease:"easeOut"}})]}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:N.score}),(0,s.jsx)("div",{className:"text-xs text-white/60",children:"SEO Score"})]})})]})]}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Readability",value:N.readabilityScore,icon:et.A},{label:"Content Length",value:N.contentLength,icon:m.A},{label:"Heading Structure",value:N.headingStructure,icon:_.A},{label:"Keyword Density",value:N.keywordDensity,icon:o.A}].map((e,t)=>(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(e.icon,{className:"h-4 w-4 text-white/60"}),(0,s.jsx)("span",{className:"text-sm text-white/80",children:e.label})]}),(0,s.jsx)("span",{className:"text-sm font-bold text-white",children:Math.round(e.value)})]}),(0,s.jsx)("div",{className:"w-full bg-white/10 rounded-full h-2",children:(0,s.jsx)(i.P.div,{className:`h-2 rounded-full ${e.value>=80?"bg-emerald-500":e.value>=60?"bg-yellow-500":"bg-red-500"}`,initial:{width:0},animate:{width:`${e.value}%`},transition:{duration:1,delay:.1*t}})})]},t))}),(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-bold text-white mb-3",children:"Content Stats"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Words"}),(0,s.jsx)("span",{className:"text-white",children:A})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Reading Time"}),(0,s.jsxs)("span",{className:"text-white",children:[M," min"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"Internal Links"}),(0,s.jsx)("span",{className:"text-white",children:N.internalLinks})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-white/60",children:"External Links"}),(0,s.jsx)("span",{className:"text-white",children:N.externalLinks})]})]})]}),N.suggestions.length>0&&(0,s.jsxs)("div",{className:"bg-white/5 rounded-xl p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-bold text-white mb-3",children:"SEO Suggestions"}),(0,s.jsx)("div",{className:"space-y-2",children:N.suggestions.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(w.A,{className:"h-3 w-3 text-yellow-400 mt-1 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-xs text-white/70",children:e})]},t))})]})]})})]}),(0,s.jsx)("div",{className:"bg-white/5 backdrop-blur-sm border-t border-white/20 px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-white/60",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("span",{children:[A," words"]}),(0,s.jsxs)("span",{children:[M," min read"]}),N&&(0,s.jsxs)("span",{className:`px-2 py-1 rounded-full text-xs ${N.score>=80?"bg-emerald-500/20 text-emerald-300":N.score>=60?"bg-yellow-500/20 text-yellow-300":"bg-red-500/20 text-red-300"}`,children:["SEO: ",N.score,"/100"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>navigator.clipboard.writeText(d),className:"p-1 rounded hover:bg-white/10 transition-colors",title:"Copy Content",children:(0,s.jsx)(T.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-1 rounded hover:bg-white/10 transition-colors",title:"Save",children:(0,s.jsx)(ea.A,{className:"h-4 w-4"})})]})]})})]})})}function el(){let[e,t]=(0,l.useState)(""),[a,c]=(0,l.useState)(!1),[z,q]=(0,l.useState)(null),[R,H]=(0,l.useState)(null),[F,L]=(0,l.useState)(null),[O,I]=(0,l.useState)(!1),[V,$]=(0,l.useState)("content"),[D,U]=(0,l.useState)(!1),[W,_]=(0,l.useState)({contentType:"article",targetWordCount:2e3,tone:"professional",maxPrimaryResults:6,maxDeepResults:4,enableParallelProcessing:!0,qualityThreshold:40,includeSourceCitations:!0,enableFactChecking:!0,retryAttempts:3,targetAudience:"general",contentPurpose:"inform",includeHooks:!0,enableStorytelling:!0,seoOptimization:!0,readabilityTarget:"high-school",includeCallToAction:!0,contentStructure:"analytical",brandVoice:"",competitorAnalysis:!1,trendAnalysis:!1,factCheckingLevel:"standard"}),B=(0,l.useRef)(null),G=(0,l.useRef)(null),Q=async()=>{if(!e.trim())return;c(!0),q(null),H(null),L(null);let t=null,s=null;try{B.current&&B.current.close(),s=setTimeout(()=>{if(a&&(L("Workflow timed out after 15 minutes"),c(!1),t))try{t.releaseLock()}catch(e){}},9e5);let l=await fetch("/api/superagent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({topic:e,options:W})});if(!l.ok)throw Error(`HTTP error! status: ${l.status}`);if(!l.body)throw Error("No response body");G.current=t=l.body.getReader();let i=new TextDecoder,r="";try{for(;;){let{done:e,value:a}=await t.read();if(e)break;let l=i.decode(a,{stream:!0}),n=(r+=l).split("\n");for(let e of(r=n.pop()||"",n))if(e.startsWith("data: "))try{let t=e.slice(6).trim();if(t){let e=JSON.parse(t);if("progress"===e.type)q({phase:e.phase,step:e.step,progress:e.progress,message:e.message,estimatedTimeRemaining:e.estimatedTimeRemaining});else if("result"===e.type){H(e),c(!1),s&&clearTimeout(s);return}else if("error"===e.type){L(e.message),c(!1),s&&clearTimeout(s);return}}}catch(t){console.error("Error parsing SSE data:",t,"Line:",e)}}if(r.trim()&&r.startsWith("data: "))try{let e=r.slice(6).trim();if(e){let t=JSON.parse(e);"result"===t.type?(H(t),c(!1)):"error"===t.type&&(L(t.message),c(!1))}}catch(e){console.error("Error parsing final buffer data:",e)}}finally{if(t)try{t.releaseLock()}catch(e){console.error("Error releasing reader lock:",e)}}}catch(e){console.error("Workflow error:",e),L(e instanceof Error?e.message:"Failed to execute workflow"),c(!1)}finally{if(s&&clearTimeout(s),t)try{t.releaseLock()}catch(e){}}},J=e=>{let t=Math.floor(e/1e3),a=Math.floor(t/60);return a>0?`${a}m ${t%60}s`:`${t}s`},K=async e=>{try{await navigator.clipboard.writeText(e),U(!0),setTimeout(()=>U(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"}),(0,s.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"4s"}})]}),(0,s.jsxs)("div",{className:"relative z-10 container mx-auto px-6 py-8",children:[(0,s.jsx)("div",{className:"text-center mb-16",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"mb-8",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-4 mb-6 p-4 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl blur-lg opacity-75"}),(0,s.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl",children:(0,s.jsx)(x.A,{className:"h-10 w-10 text-white"})})]}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("h1",{className:"text-5xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent",children:"Professional Content Writer AI"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)("div",{className:"px-3 py-1 bg-emerald-500/20 text-emerald-300 rounded-full text-sm font-medium border border-emerald-500/30",children:"Professional"}),(0,s.jsx)("div",{className:"px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30",children:"Enhanced"}),(0,s.jsx)("div",{className:"px-3 py-1 bg-yellow-500/20 text-yellow-300 rounded-full text-sm font-medium border border-yellow-500/30",children:"v3.0"})]})]})]}),(0,s.jsx)(i.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3,duration:.8},className:"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed",children:"Professional content writing AI that follows industry best practices. Features advanced research, content strategy development, storytelling integration, SEO optimization, and comprehensive quality analysis for publication-ready content."}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.8},className:"flex flex-wrap justify-center gap-3 mt-6",children:[{icon:x.A,text:"Professional Writing",color:"from-emerald-500 to-green-600"},{icon:u.A,text:"Content Strategy",color:"from-blue-500 to-cyan-600"},{icon:m.A,text:"Storytelling",color:"from-purple-500 to-pink-600"},{icon:o.A,text:"SEO Optimization",color:"from-orange-500 to-red-600"},{icon:d.A,text:"Quality Analysis",color:"from-yellow-500 to-amber-600"},{icon:p.A,text:"Fact Checking",color:"from-red-500 to-pink-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20",children:[(0,s.jsx)(e.icon,{className:"h-4 w-4 text-white/80"}),(0,s.jsx)("span",{className:"text-sm text-white/80 font-medium",children:e.text})]},t))})]})}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2,duration:.8},className:"max-w-5xl mx-auto mb-16",children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"}),(0,s.jsxs)("div",{className:"relative space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(b.A,{className:"h-6 w-6 text-yellow-400"}),(0,s.jsx)("label",{className:"text-xl font-bold text-white",children:"Research Topic"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"Enter your research topic (e.g., 'artificial intelligence in healthcare', 'quantum computing applications')",className:"w-full px-6 py-5 text-lg bg-white/10 backdrop-blur-sm border border-white/30 rounded-2xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 focus:bg-white/15",disabled:a}),e&&(0,s.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)("div",{className:"w-3 h-3 bg-emerald-400 rounded-full animate-pulse"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("h3",{className:"text-lg font-bold text-white",children:"Configuration"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Content Type"}),(0,s.jsxs)("select",{value:W.contentType,onChange:e=>_({...W,contentType:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"article",className:"bg-gray-800 text-white",children:"Article"}),(0,s.jsx)("option",{value:"blog-post",className:"bg-gray-800 text-white",children:"Blog Post"}),(0,s.jsx)("option",{value:"research-paper",className:"bg-gray-800 text-white",children:"Research Paper"}),(0,s.jsx)("option",{value:"comprehensive-guide",className:"bg-gray-800 text-white",children:"Comprehensive Guide"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Target Word Count"}),(0,s.jsx)("input",{type:"number",value:W.targetWordCount,onChange:e=>_({...W,targetWordCount:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Tone"}),(0,s.jsxs)("select",{value:W.tone,onChange:e=>_({...W,tone:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"professional",className:"bg-gray-800 text-white",children:"Professional"}),(0,s.jsx)("option",{value:"casual",className:"bg-gray-800 text-white",children:"Casual"}),(0,s.jsx)("option",{value:"academic",className:"bg-gray-800 text-white",children:"Academic"}),(0,s.jsx)("option",{value:"conversational",className:"bg-gray-800 text-white",children:"Conversational"}),(0,s.jsx)("option",{value:"authoritative",className:"bg-gray-800 text-white",children:"Authoritative"}),(0,s.jsx)("option",{value:"engaging",className:"bg-gray-800 text-white",children:"Engaging"}),(0,s.jsx)("option",{value:"technical",className:"bg-gray-800 text-white",children:"Technical"})]})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-white/20 pt-8",children:[(0,s.jsxs)(i.P.button,{onClick:()=>I(!O),className:"flex items-center gap-3 mb-6 text-white hover:text-purple-300 transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)(y,{className:"h-5 w-5 text-purple-400"}),(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Advanced Settings"}),O?(0,s.jsx)(v,{className:"h-5 w-5"}):(0,s.jsx)(f.A,{className:"h-5 w-5"})]}),(0,s.jsx)(r.N,{children:O&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Primary Sources"}),(0,s.jsx)("input",{type:"number",min:"3",max:"10",value:W.maxPrimaryResults,onChange:e=>_({...W,maxPrimaryResults:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Deep Research Sources"}),(0,s.jsx)("input",{type:"number",min:"2",max:"8",value:W.maxDeepResults,onChange:e=>_({...W,maxDeepResults:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Quality Threshold"}),(0,s.jsx)("input",{type:"number",min:"20",max:"80",value:W.qualityThreshold,onChange:e=>_({...W,qualityThreshold:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Retry Attempts"}),(0,s.jsx)("input",{type:"number",min:"1",max:"5",value:W.retryAttempts,onChange:e=>_({...W,retryAttempts:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-yellow-400"}),(0,s.jsx)("h4",{className:"text-lg font-bold text-white",children:"Professional Writing Options"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Target Audience"}),(0,s.jsxs)("select",{value:W.targetAudience,onChange:e=>_({...W,targetAudience:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"general",className:"bg-gray-800 text-white",children:"General"}),(0,s.jsx)("option",{value:"beginner",className:"bg-gray-800 text-white",children:"Beginner"}),(0,s.jsx)("option",{value:"intermediate",className:"bg-gray-800 text-white",children:"Intermediate"}),(0,s.jsx)("option",{value:"expert",className:"bg-gray-800 text-white",children:"Expert"}),(0,s.jsx)("option",{value:"technical",className:"bg-gray-800 text-white",children:"Technical"}),(0,s.jsx)("option",{value:"business",className:"bg-gray-800 text-white",children:"Business"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Content Purpose"}),(0,s.jsxs)("select",{value:W.contentPurpose,onChange:e=>_({...W,contentPurpose:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"inform",className:"bg-gray-800 text-white",children:"Inform"}),(0,s.jsx)("option",{value:"persuade",className:"bg-gray-800 text-white",children:"Persuade"}),(0,s.jsx)("option",{value:"educate",className:"bg-gray-800 text-white",children:"Educate"}),(0,s.jsx)("option",{value:"entertain",className:"bg-gray-800 text-white",children:"Entertain"}),(0,s.jsx)("option",{value:"convert",className:"bg-gray-800 text-white",children:"Convert"}),(0,s.jsx)("option",{value:"engage",className:"bg-gray-800 text-white",children:"Engage"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Content Structure"}),(0,s.jsxs)("select",{value:W.contentStructure,onChange:e=>_({...W,contentStructure:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"analytical",className:"bg-gray-800 text-white",children:"Analytical"}),(0,s.jsx)("option",{value:"problem-solution",className:"bg-gray-800 text-white",children:"Problem-Solution"}),(0,s.jsx)("option",{value:"how-to",className:"bg-gray-800 text-white",children:"How-To"}),(0,s.jsx)("option",{value:"listicle",className:"bg-gray-800 text-white",children:"Listicle"}),(0,s.jsx)("option",{value:"comparison",className:"bg-gray-800 text-white",children:"Comparison"}),(0,s.jsx)("option",{value:"narrative",className:"bg-gray-800 text-white",children:"Narrative"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Readability Target"}),(0,s.jsxs)("select",{value:W.readabilityTarget,onChange:e=>_({...W,readabilityTarget:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"elementary",className:"bg-gray-800 text-white",children:"Elementary"}),(0,s.jsx)("option",{value:"middle-school",className:"bg-gray-800 text-white",children:"Middle School"}),(0,s.jsx)("option",{value:"high-school",className:"bg-gray-800 text-white",children:"High School"}),(0,s.jsx)("option",{value:"college",className:"bg-gray-800 text-white",children:"College"}),(0,s.jsx)("option",{value:"graduate",className:"bg-gray-800 text-white",children:"Graduate"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Fact-Checking Level"}),(0,s.jsxs)("select",{value:W.factCheckingLevel,onChange:e=>_({...W,factCheckingLevel:e.target.value}),className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a,children:[(0,s.jsx)("option",{value:"basic",className:"bg-gray-800 text-white",children:"Basic"}),(0,s.jsx)("option",{value:"standard",className:"bg-gray-800 text-white",children:"Standard"}),(0,s.jsx)("option",{value:"rigorous",className:"bg-gray-800 text-white",children:"Rigorous"}),(0,s.jsx)("option",{value:"academic",className:"bg-gray-800 text-white",children:"Academic"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white/80 mb-3",children:"Brand Voice"}),(0,s.jsx)("input",{type:"text",value:W.brandVoice,onChange:e=>_({...W,brandVoice:e.target.value}),placeholder:"e.g., friendly, authoritative, innovative",className:"w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300",disabled:a})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[{id:"includeHooks",label:"Include Hooks",icon:u.A,checked:W.includeHooks,onChange:e=>_({...W,includeHooks:e})},{id:"enableStorytelling",label:"Enable Storytelling",icon:m.A,checked:W.enableStorytelling,onChange:e=>_({...W,enableStorytelling:e})},{id:"seoOptimization",label:"SEO Optimization",icon:o.A,checked:W.seoOptimization,onChange:e=>_({...W,seoOptimization:e})},{id:"includeCallToAction",label:"Include CTAs",icon:w.A,checked:W.includeCallToAction,onChange:e=>_({...W,includeCallToAction:e})},{id:"competitorAnalysis",label:"Competitor Analysis",icon:d.A,checked:W.competitorAnalysis,onChange:e=>_({...W,competitorAnalysis:e})},{id:"trendAnalysis",label:"Trend Analysis",icon:h,checked:W.trendAnalysis,onChange:e=>_({...W,trendAnalysis:e})}].map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center gap-4 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/10 transition-all duration-300",children:[(0,s.jsx)(e.icon,{className:"h-5 w-5 text-yellow-400"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-white cursor-pointer",children:e.label})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"checkbox",id:e.id,checked:e.checked,onChange:t=>e.onChange(t.target.checked),className:"sr-only",disabled:a}),(0,s.jsx)("div",{onClick:()=>!a&&e.onChange(!e.checked),className:`w-12 h-6 rounded-full cursor-pointer transition-all duration-300 ${e.checked?"bg-yellow-500":"bg-white/20"}`,children:(0,s.jsx)("div",{className:`w-5 h-5 bg-white rounded-full shadow-lg transform transition-transform duration-300 mt-0.5 ${e.checked?"translate-x-6":"translate-x-0.5"}`})})]})]},e.id))}),(0,s.jsxs)("div",{className:"border-t border-white/20 pt-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)(j,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("h4",{className:"text-lg font-bold text-white",children:"Technical Settings"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[{id:"parallelProcessing",label:"Parallel Processing",icon:j,checked:W.enableParallelProcessing,onChange:e=>_({...W,enableParallelProcessing:e})},{id:"sourceCitations",label:"Source Citations",icon:N.A,checked:W.includeSourceCitations,onChange:e=>_({...W,includeSourceCitations:e})},{id:"factChecking",label:"Fact Checking",icon:p.A,checked:W.enableFactChecking,onChange:e=>_({...W,enableFactChecking:e})}].map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center gap-4 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/10 transition-all duration-300",children:[(0,s.jsx)(e.icon,{className:"h-5 w-5 text-blue-400"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-white cursor-pointer",children:e.label})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"checkbox",id:e.id,checked:e.checked,onChange:t=>e.onChange(t.target.checked),className:"sr-only",disabled:a}),(0,s.jsx)("div",{onClick:()=>!a&&e.onChange(!e.checked),className:`w-12 h-6 rounded-full cursor-pointer transition-all duration-300 ${e.checked?"bg-blue-500":"bg-white/20"}`,children:(0,s.jsx)("div",{className:`w-5 h-5 bg-white rounded-full shadow-lg transform transition-transform duration-300 mt-0.5 ${e.checked?"translate-x-6":"translate-x-0.5"}`})})]})]},e.id))})]})]})})]}),(0,s.jsxs)("div",{className:"pt-8",children:[(0,s.jsxs)(i.P.button,{whileHover:{scale:1.02,y:-2},whileTap:{scale:.98},onClick:Q,disabled:a||!e.trim(),className:"relative w-full group overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 rounded-2xl blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-300"}),(0,s.jsx)("div",{className:"relative bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 text-white font-bold py-6 px-8 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-4 border border-white/20",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"relative",children:[(0,s.jsx)(w.A,{className:"h-6 w-6"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-white/30 rounded-full blur-sm"})]}),(0,s.jsx)("span",{className:"text-lg",children:"Processing Research..."}),(0,s.jsx)("div",{className:"flex gap-1",children:[0,1,2].map(e=>(0,s.jsx)(i.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:.2*e},className:"w-2 h-2 bg-white/60 rounded-full"},e))})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(k,{className:"h-6 w-6"}),(0,s.jsx)(i.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0},className:"absolute inset-0 bg-white/30 rounded-full blur-sm"})]}),(0,s.jsx)("span",{className:"text-lg",children:"Launch AI Superagent"}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(A.A,{className:"h-4 w-4 text-yellow-300"}),(0,s.jsx)("span",{className:"text-sm text-white/80",children:"Enhanced"})]})]})}),(0,s.jsx)(i.P.div,{className:"absolute inset-0 bg-white/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300",whileHover:{scale:1.05}})]}),e.trim()&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-center gap-2 mt-4 text-white/60 text-sm",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 text-emerald-400"}),(0,s.jsx)("span",{children:"Ready to generate high-quality content"})]})]})]})]})}),(0,s.jsx)(r.N,{children:z&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},className:"max-w-5xl mx-auto mb-12",children:(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-cyan-500/10 animate-pulse"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"flex items-center gap-6 mb-8",children:[(0,s.jsxs)(i.P.div,{animate:{rotate:360,scale:[1,1.1,1]},transition:{rotate:{duration:3,repeat:1/0,ease:"linear"},scale:{duration:2,repeat:1/0}},className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl blur-lg opacity-75"}),(0,s.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-white"})})]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)(i.P.h3,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"text-2xl font-bold text-white mb-2",children:["analysis"===z.phase&&"\uD83E\uDDE0 Analyzing Topic","strategy"===z.phase&&"\uD83C\uDFAF Content Strategy","outline"===z.phase&&"\uD83D\uDCDD Content Outline","primary-research"===z.phase&&"\uD83D\uDD0D Primary Research","gap-analysis"===z.phase&&"\uD83D\uDCCA Gap Analysis","deep-research"===z.phase&&"\uD83C\uDFAF Deep Research","content-generation"===z.phase&&"✨ Content Generation","editing"===z.phase&&"✏️ Content Enhancement","fact-checking"===z.phase&&"\uD83D\uDD0D Fact Checking","optimization"===z.phase&&"\uD83D\uDE80 Final Optimization"]}),(0,s.jsx)(i.P.p,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},className:"text-white/80 text-lg",children:z.message})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)(i.P.div,{initial:{scale:0},animate:{scale:1},className:"text-4xl font-bold text-white mb-1",children:[z.progress,"%"]}),(0,s.jsx)("div",{className:"text-white/60 text-sm",children:"Complete"})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-full bg-white/20 rounded-full h-4 mb-4 overflow-hidden",children:(0,s.jsx)(i.P.div,{className:"h-4 rounded-full bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 relative overflow-hidden",initial:{width:0},animate:{width:`${z.progress}%`},transition:{duration:.8,ease:"easeOut"},children:(0,s.jsx)(i.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent",animate:{x:[-100,300]},transition:{duration:2,repeat:1/0,ease:"linear"},style:{width:"100px"}})})}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(i.P.span,{initial:{opacity:0},animate:{opacity:1},className:"text-white/80 font-medium",children:z.step}),(0,s.jsx)("div",{className:"flex gap-2",children:["analysis","primary-research","gap-analysis","deep-research","content-generation"].map((e,t)=>(0,s.jsx)(i.P.div,{className:`w-3 h-3 rounded-full transition-all duration-300 ${z.phase===e?"bg-white shadow-lg":t<["analysis","primary-research","gap-analysis","deep-research","content-generation"].indexOf(z.phase)?"bg-emerald-400":"bg-white/30"}`,animate:z.phase===e?{scale:[1,1.3,1]}:{},transition:{duration:1,repeat:1/0}},e))})]})]})]})]})})}),(0,s.jsx)(r.N,{children:F&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},className:"max-w-5xl mx-auto mb-12",children:(0,s.jsxs)("div",{className:"bg-red-500/10 backdrop-blur-xl border border-red-500/30 rounded-3xl p-8 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-500/5 to-pink-500/5"}),(0,s.jsxs)("div",{className:"relative flex items-center gap-4",children:[(0,s.jsx)(i.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0},className:"p-3 bg-red-500/20 rounded-2xl",children:(0,s.jsx)(M.A,{className:"h-8 w-8 text-red-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-red-300 mb-2",children:"Workflow Error"}),(0,s.jsx)("p",{className:"text-red-200 text-lg",children:F})]})]})]})})}),(0,s.jsx)(r.N,{children:R&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},exit:{opacity:0,y:-30},className:"max-w-7xl mx-auto",children:[(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center mb-12",children:(0,s.jsxs)("div",{className:"inline-flex items-center gap-4 p-6 bg-emerald-500/10 backdrop-blur-xl rounded-3xl border border-emerald-500/30 mb-6",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,360]},transition:{duration:2,ease:"easeInOut"},className:"p-3 bg-emerald-500/20 rounded-2xl",children:(0,s.jsx)(C.A,{className:"h-8 w-8 text-emerald-400"})}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Content Generated Successfully!"}),(0,s.jsx)("p",{className:"text-white/80",children:"High-quality research-backed content ready for use"})]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:[{icon:u.A,value:R.qualityScore,label:"Quality Score",color:"from-emerald-500 to-green-600",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/30"},{icon:m.A,value:R.wordCount.toLocaleString(),label:"Words Generated",color:"from-blue-500 to-cyan-600",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/30"},{icon:P.A,value:R.sourcesUsed,label:"Sources Analyzed",color:"from-purple-500 to-pink-600",bgColor:"bg-purple-500/10",borderColor:"border-purple-500/30"},{icon:S.A,value:J(R.metadata.totalResearchTime),label:"Processing Time",color:"from-orange-500 to-red-600",bgColor:"bg-orange-500/10",borderColor:"border-orange-500/30"}].map((e,t)=>(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:`${e.bgColor} backdrop-blur-xl rounded-3xl p-6 shadow-2xl border ${e.borderColor} relative overflow-hidden group hover:scale-105 transition-transform duration-300`,children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"}),(0,s.jsxs)("div",{className:"relative flex items-center gap-4",children:[(0,s.jsx)("div",{className:`p-3 bg-gradient-to-r ${e.color} rounded-2xl shadow-lg`,children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-white mb-1",children:e.value}),(0,s.jsx)("div",{className:"text-sm text-white/70",children:e.label})]})]})]},t))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Core Quality Metrics"}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Readability",value:R.qualityMetrics.readabilityScore,color:"bg-blue-500"},{label:"Coherence",value:R.qualityMetrics.coherenceScore,color:"bg-green-500"},{label:"Factual Accuracy",value:R.qualityMetrics.factualAccuracy,color:"bg-purple-500"},{label:"Source Reliability",value:R.qualityMetrics.sourceReliability,color:"bg-orange-500"},{label:"Comprehensiveness",value:R.qualityMetrics.comprehensiveness,color:"bg-cyan-500"},{label:"Originality",value:R.qualityMetrics.originalityScore,color:"bg-pink-500"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full ${e.color}`,style:{width:`${e.value}%`}})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-800 w-8",children:Math.round(e.value)})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Professional Writing"}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Engagement",value:R.qualityMetrics.engagementScore||75,color:"bg-yellow-500"},{label:"Storytelling",value:R.qualityMetrics.storytellingQuality||70,color:"bg-indigo-500"},{label:"Hook Effectiveness",value:R.qualityMetrics.hookEffectiveness||80,color:"bg-red-500"},{label:"CTA Strength",value:R.qualityMetrics.callToActionStrength||85,color:"bg-emerald-500"},{label:"SEO Optimization",value:R.qualityMetrics.seoOptimization||90,color:"bg-blue-600"},{label:"Brand Voice",value:R.qualityMetrics.brandVoiceConsistency||75,color:"bg-purple-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full ${e.color}`,style:{width:`${e.value}%`}})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-800 w-8",children:Math.round(e.value)})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Content Excellence"}),(0,s.jsx)("div",{className:"space-y-4",children:[{label:"Expertise Level",value:R.qualityMetrics.expertiseLevel||85,color:"bg-teal-500"},{label:"Trustworthiness",value:R.qualityMetrics.trustworthiness||90,color:"bg-green-600"},{label:"Emotional Resonance",value:R.qualityMetrics.emotionalResonance||70,color:"bg-pink-600"},{label:"Actionability",value:R.qualityMetrics.actionability||80,color:"bg-orange-600"},{label:"Visual Appeal",value:R.qualityMetrics.visualAppeal||85,color:"bg-violet-500"},{label:"Scanability",value:R.qualityMetrics.scanability||90,color:"bg-cyan-600"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e.label}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full ${e.color}`,style:{width:`${e.value}%`}})}),(0,s.jsx)("span",{className:"text-sm font-semibold text-gray-800 w-8",children:Math.round(e.value)})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Research Analytics"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Confidence Score"}),(0,s.jsxs)("span",{className:"text-lg font-bold text-gray-800",children:[R.metadata?.confidenceScore||0,"%"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Average Source Quality"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-800",children:Math.round(R.metadata?.averageSourceQuality||0)})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Error Count"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-800",children:R.metadata?.errorCount||0})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Retry Count"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-800",children:R.metadata?.retryCount||0})]}),R.metadata.phaseTimings&&(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-2",children:"Phase Timings"}),Object.entries(R.metadata.phaseTimings).map(([e,t])=>(0,s.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[(0,s.jsx)("span",{className:"text-gray-600 capitalize",children:e.replace("-"," ")}),(0,s.jsx)("span",{className:"text-gray-800",children:J(t)})]},e))]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Content Elements"}),R.hooks&&R.hooks.length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Hooks Identified"}),(0,s.jsx)("div",{className:"space-y-2",children:R.hooks.slice(0,3).map((e,t)=>(0,s.jsx)("div",{className:"p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-gray-700 italic",children:['"',e,'"']})},t))})]}),R.callToActions&&R.callToActions.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Call-to-Actions"}),(0,s.jsx)("div",{className:"space-y-2",children:R.callToActions.slice(0,3).map((e,t)=>(0,s.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-gray-700 font-medium",children:e})},t))})]})]}),(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Content Analysis"}),R.readabilityAnalysis&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Readability Analysis"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-blue-800",children:R.readabilityAnalysis.grade}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"Reading Level"})]}),(0,s.jsxs)("div",{className:"text-center p-3 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"text-lg font-bold text-purple-800",children:[R.readabilityAnalysis.readingTime," min"]}),(0,s.jsx)("div",{className:"text-xs text-purple-600",children:"Reading Time"})]})]}),(0,s.jsx)("div",{className:"mt-3 text-center",children:(0,s.jsxs)("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${"simple"===R.readabilityAnalysis.complexity?"bg-green-100 text-green-800":"moderate"===R.readabilityAnalysis.complexity?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:[R.readabilityAnalysis.complexity.charAt(0).toUpperCase()+R.readabilityAnalysis.complexity.slice(1)," Complexity"]})})]}),R.seoKeywords&&R.seoKeywords.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"SEO Keywords"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:R.seoKeywords.slice(0,8).map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full text-xs font-medium",children:e},t))})]}),R.factCheckResults&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Fact Check Results"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-center",children:[(0,s.jsxs)("div",{className:"p-2 bg-green-50 border border-green-200 rounded",children:[(0,s.jsx)("div",{className:"text-sm font-bold text-green-800",children:R.factCheckResults.verifiedClaims}),(0,s.jsx)("div",{className:"text-xs text-green-600",children:"Verified"})]}),(0,s.jsxs)("div",{className:"p-2 bg-yellow-50 border border-yellow-200 rounded",children:[(0,s.jsx)("div",{className:"text-sm font-bold text-yellow-800",children:R.factCheckResults.unverifiedClaims}),(0,s.jsx)("div",{className:"text-xs text-yellow-600",children:"Unverified"})]}),(0,s.jsxs)("div",{className:"p-2 bg-blue-50 border border-blue-200 rounded",children:[(0,s.jsxs)("div",{className:"text-sm font-bold text-blue-800",children:[Math.round(R.factCheckResults.overallTrustworthiness),"%"]}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"Trust Score"})]})]})]})]})]}),R.citations&&R.citations.length>0&&(0,s.jsxs)("div",{className:"bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Source Citations"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:R.citations.slice(0,6).map((e,t)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-800 line-clamp-2",children:e.title}),(0,s.jsx)("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full ml-2",children:Math.round(e.relevanceScore)})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.domain}),e.usedInSections.length>0&&(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Used in: ",e.usedInSections.join(", ")]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden",children:[(0,s.jsx)("div",{className:"p-8 border-b border-white/20",children:(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:R.title}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:(R.metadata?.keywordsTargeted||[]).map((e,t)=>(0,s.jsx)("span",{className:"px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30",children:e},t))})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>K(R.article),className:"flex items-center gap-2 px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 rounded-xl border border-blue-500/30 transition-all duration-300",children:[(0,s.jsx)(T.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:D?"Copied!":"Copy"})]}),(0,s.jsxs)(i.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center gap-2 px-4 py-2 bg-emerald-500/20 hover:bg-emerald-500/30 text-emerald-300 rounded-xl border border-emerald-500/30 transition-all duration-300",children:[(0,s.jsx)(E.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Export"})]})]})]})}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(es,{content:R.article,onChange:e=>{console.log("Content updated:",e)},onSEOAnalysis:e=>{console.log("SEO Analysis:",e)},className:"border-none rounded-none"})})]})]})})]})]})}n.A,o.A,d.A,m.A},28947:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31110:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},31158:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34333:(e,t,a)=>{Promise.resolve().then(a.bind(a,22362)),Promise.resolve().then(a.bind(a,1188))},44692:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]])},45583:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45984:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("heading-1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},47342:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48730:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},54388:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},56085:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},60028:(e,t,a)=>{Promise.resolve().then(a.bind(a,75975))},61135:()=>{},61611:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},68462:(e,t,a)=>{"use strict";a.d(t,{ThemeProvider:()=>l});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","useTheme");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","ThemeProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","default")},69169:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("heading-2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},69282:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},70615:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},75975:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/superagent/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/superagent/page.tsx","default")},78200:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},78272:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},80375:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},84027:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84821:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("link-2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]])},88920:(e,t,a)=>{"use strict";a.d(t,{N:()=>g});var s=a(60687),l=a(43210),i=a(12157),r=a(72789),n=a(21279),o=a(32582);class d extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t}){let a=(0,l.useId)(),i=(0,l.useRef)(null),r=(0,l.useRef)({width:0,height:0,top:0,left:0}),{nonce:n}=(0,l.useContext)(o.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:s,top:l,left:o}=r.current;if(t||!i.current||!e||!s)return;i.current.dataset.motionPopId=a;let d=document.createElement("style");return n&&(d.nonce=n),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            top: ${l}px !important;
            left: ${o}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),(0,s.jsx)(d,{isPresent:t,childRef:i,sizeRef:r,children:l.cloneElement(e,{ref:i})})}let h=({children:e,initial:t,isPresent:a,onExitComplete:i,custom:o,presenceAffectsLayout:d,mode:h})=>{let x=(0,r.M)(m),u=(0,l.useId)(),p=(0,l.useCallback)(e=>{for(let t of(x.set(e,!0),x.values()))if(!t)return;i&&i()},[x,i]),b=(0,l.useMemo)(()=>({id:u,initial:t,isPresent:a,custom:o,onExitComplete:p,register:e=>(x.set(e,!1),()=>x.delete(e))}),d?[Math.random(),p]:[a,p]);return(0,l.useMemo)(()=>{x.forEach((e,t)=>x.set(t,!1))},[a]),l.useEffect(()=>{a||x.size||!i||i()},[a]),"popLayout"===h&&(e=(0,s.jsx)(c,{isPresent:a,children:e})),(0,s.jsx)(n.t.Provider,{value:b,children:e})};function m(){return new Map}var x=a(86044);let u=e=>e.key||"";function p(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}var b=a(15124);let g=({children:e,custom:t,initial:a=!0,onExitComplete:n,presenceAffectsLayout:o=!0,mode:d="sync",propagate:c=!1})=>{let[m,g]=(0,x.xQ)(c),y=(0,l.useMemo)(()=>p(e),[e]),v=c&&!m?[]:y.map(u),f=(0,l.useRef)(!0),w=(0,l.useRef)(y),j=(0,r.M)(()=>new Map),[N,k]=(0,l.useState)(y),[A,C]=(0,l.useState)(y);(0,b.E)(()=>{f.current=!1,w.current=y;for(let e=0;e<A.length;e++){let t=u(A[e]);v.includes(t)?j.delete(t):!0!==j.get(t)&&j.set(t,!1)}},[A,v.length,v.join("-")]);let M=[];if(y!==N){let e=[...y];for(let t=0;t<A.length;t++){let a=A[t],s=u(a);v.includes(s)||(e.splice(t,0,a),M.push(a))}"wait"===d&&M.length&&(e=M),C(p(e)),k(y);return}let{forceRender:P}=(0,l.useContext)(i.L);return(0,s.jsx)(s.Fragment,{children:A.map(e=>{let l=u(e),i=(!c||!!m)&&(y===A||v.includes(l));return(0,s.jsx)(h,{isPresent:i,initial:(!f.current||!!a)&&void 0,custom:i?void 0:t,presenceAffectsLayout:o,mode:d,onExitComplete:i?void 0:()=>{if(!j.has(l))return;j.set(l,!0);let e=!0;j.forEach(t=>{t||(e=!1)}),e&&(null==P||P(),C(w.current),c&&(null==g||g()),n&&n())},children:e},l)})})}},93613:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d,metadata:()=>o});var s=a(37413),l=a(73911),i=a.n(l);a(61135);var r=a(19864),n=a(68462);let o={title:"Invincible - Content Writing SaaS",description:"The Ultimate Content Writing SaaS Platform with AI-powered tools"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().className} antialiased`,children:(0,s.jsx)(r.SettingsProvider,{children:(0,s.jsx)(n.ThemeProvider,{children:e})})})})}},98916:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},98971:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},99270:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,135,951],()=>a(2364));module.exports=s})();