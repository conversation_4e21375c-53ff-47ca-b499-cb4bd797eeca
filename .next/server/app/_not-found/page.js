(()=>{var e={};e.id=492,e.ids=[492],e.modules={1188:(e,t,r)=>{"use strict";r.d(t,{D:()=>a,ThemeProvider:()=>l});var o=r(60687),n=r(43210),s=r(22362);let i=(0,n.createContext)(void 0),a=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},l=({children:e})=>{let{settings:t,updateSettings:r}=(0,s.t)(),a="auto"===t.theme?"dark":t.theme;(0,n.useEffect)(()=>{},[a,t.accentColor,t.animationsEnabled,t.compactMode]),(0,n.useEffect)(()=>{t.theme},[t.theme]);let l={theme:a,accentColor:t.accentColor,animationsEnabled:t.animationsEnabled,compactMode:t.compactMode,setTheme:e=>{r({theme:e})},setAccentColor:e=>{r({accentColor:e})},toggleAnimations:()=>{r({animationsEnabled:!t.animationsEnabled})},toggleCompactMode:()=>{r({compactMode:!t.compactMode})}};return(0,o.jsx)(i.Provider,{value:l,children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15773:(e,t,r)=>{Promise.resolve().then(r.bind(r,19864)),Promise.resolve().then(r.bind(r,68462))},16764:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19864:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","useSettings");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","SettingsProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","default")},22362:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>l,t:()=>a});var o=r(60687),n=r(43210);let s={firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0},i=(0,n.createContext)(void 0),a=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},l=({children:e})=>{let[t,r]=(0,n.useState)(s),[a,l]=(0,n.useState)(!0),[d,c]=(0,n.useState)(null);(0,n.useEffect)(()=>{try{let e=localStorage.getItem("userSettings");if(e){let t=JSON.parse(e);r({...s,...t})}}catch(e){console.error("Failed to load settings:",e),c("Failed to load user settings")}finally{l(!1)}},[]),(0,n.useEffect)(()=>{if(!a)try{localStorage.setItem("userSettings",JSON.stringify(t))}catch(e){console.error("Failed to save settings:",e),c("Failed to save settings")}},[t,a]);let h=async()=>{try{c(null);let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),r=await e.json();if(!e.ok)throw Error(r.error||"Failed to save settings")}catch(t){let e=t instanceof Error?t.message:"Failed to save settings";throw c(e),Error(e)}};return(0,o.jsx)(i.Provider,{value:{settings:t,updateSettings:e=>{r(t=>({...t,...e})),c(null)},resetSettings:()=>{r(s),c(null)},saveSettings:h,isLoading:a,error:d},children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34333:(e,t,r)=>{Promise.resolve().then(r.bind(r,22362)),Promise.resolve().then(r.bind(r,1188))},38988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var o=r(65239),n=r(48088),s=r(88170),i=r.n(s),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],h={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68462:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","useTheme");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var o=r(37413),n=r(73911),s=r.n(n);r(61135);var i=r(19864),a=r(68462);let l={title:"Invincible - Content Writing SaaS",description:"The Ultimate Content Writing SaaS Platform with AI-powered tools"};function d({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${s().className} antialiased`,children:(0,o.jsx)(i.SettingsProvider,{children:(0,o.jsx)(a.ThemeProvider,{children:e})})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,135],()=>r(38988));module.exports=o})();