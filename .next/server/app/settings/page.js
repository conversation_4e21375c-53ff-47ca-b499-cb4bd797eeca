(()=>{var e={};e.id=662,e.ids=[662],e.modules={1353:(e,r,t)=>{Promise.resolve().then(t.bind(t,74992))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8819:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},14487:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(60687),s=t(88920),l=t(97905),i=t(5336),n=t(35071),o=t(43649);let c=(0,t(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var d=t(11860);function u({type:e,title:r,message:t,show:u,onClose:h,autoClose:p=!1,duration:x=5e3,icon:f}){let m={success:(0,a.jsx)(i.A,{className:"w-5 h-5"}),error:(0,a.jsx)(n.A,{className:"w-5 h-5"}),warning:(0,a.jsx)(o.A,{className:"w-5 h-5"}),info:(0,a.jsx)(c,{className:"w-5 h-5"})};return p&&u&&h&&setTimeout(()=>{h()},x),(0,a.jsx)(s.N,{children:u&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},transition:{duration:.3},className:`alert-modern ${{success:"alert-modern-success",error:"alert-modern-error",warning:"alert-modern-warning",info:"alert-modern-info"}[e]}`,children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:f||m[e]}),(0,a.jsxs)("div",{className:"flex-1",children:[r&&(0,a.jsx)("h4",{className:"font-semibold mb-1",children:r}),(0,a.jsx)("p",{className:"text-sm opacity-90",children:t})]}),h&&(0,a.jsx)("button",{onClick:h,className:"flex-shrink-0 ml-4 p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33954:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=t(65239),s=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let c={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74198)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35071:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37924:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var a=t(60687),s=t(97905),l=t(43210);function i({label:e,type:r="text",value:t,onChange:i,placeholder:n="",required:o=!1,disabled:c=!1,error:d,icon:u,options:h=[],rows:p=4,className:x=""}){let[f,m]=(0,l.useState)(!1),b=e=>{i("number"===r?Number(e.target.value):e.target.value)},g=`
    modern-input-field
    ${d?"border-red-400":""}
    ${c?"opacity-50 cursor-not-allowed":""}
    ${x}
  `.trim(),y=`
    modern-input-label
    modern-input-label-active
    ${d?"text-red-400":""}
  `.trim();return(0,a.jsxs)(s.P.div,{className:"modern-input",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,a.jsxs)("label",{className:y,children:[e," ",o&&(0,a.jsx)("span",{className:"text-red-400",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[u&&(0,a.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 z-10 pointer-events-none",children:u}),"textarea"===r?(0,a.jsx)("textarea",{value:t,onChange:b,onFocus:()=>m(!0),onBlur:()=>m(!1),placeholder:n,required:o,disabled:c,rows:p,className:`${g} ${u?"pl-14":""}`}):"select"===r?(0,a.jsxs)("select",{value:t,onChange:b,onFocus:()=>m(!0),onBlur:()=>m(!1),required:o,disabled:c,className:`${g} ${u?"pl-14":""}`,children:[(0,a.jsx)("option",{value:"",children:n||`Select ${e}`}),h.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,a.jsx)("input",{type:r,value:t,onChange:b,onFocus:()=>m(!0),onBlur:()=>m(!1),placeholder:n,required:o,disabled:c,className:`${g} ${u?"pl-14":""}`})]}),d&&(0,a.jsx)(s.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"text-red-400 text-sm mt-1 px-3",children:d})]})}},43649:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},59497:(e,r,t)=>{Promise.resolve().then(t.bind(t,74198))},61611:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74198:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx","default")},74992:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var a=t(60687),s=t(43210),l=t(97905),i=t(58869),n=t(84027),o=t(61611),c=t(97051),d=t(98971),u=t(99891),h=t(78122),p=t(8819),x=t(81822),f=t(79216),m=t(37924),b=t(14487),g=t(16337),y=t(22362),v=t(1188);function j(){let[e,r]=(0,s.useState)("profile"),[t,j]=(0,s.useState)(!1),[w,k]=(0,s.useState)(""),[N,A]=(0,s.useState)(""),{settings:C,updateSettings:P,resetSettings:T,saveSettings:E,error:M}=(0,y.t)(),{setTheme:S,setAccentColor:D,toggleAnimations:q,toggleCompactMode:z}=(0,v.D)(),_=N||M,R=[{id:"profile",label:"Profile",icon:i.A},{id:"preferences",label:"Preferences",icon:n.A},{id:"content",label:"Content",icon:o.A},{id:"notifications",label:"Notifications",icon:c.A},{id:"appearance",label:"Appearance",icon:d.A},{id:"privacy",label:"Privacy",icon:u.A}],$=async()=>{j(!0),A(""),k("");try{await E(),k("Profile updated successfully!"),setTimeout(()=>k(""),3e3)}catch(e){A("Failed to save settings. Please try again.")}finally{j(!1)}};return(0,a.jsx)(g.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsx)(l.P.div,{className:"mb-8",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-2",children:"Account Settings"}),(0,a.jsx)("p",{className:"text-xl text-white/80",children:"Manage your profile, preferences, and account settings"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsx)(l.P.div,{className:"lg:col-span-1",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.6},children:(0,a.jsx)(f.A,{variant:"elevated",children:(0,a.jsx)("nav",{className:"space-y-2",children:R.map(t=>{let s=t.icon;return(0,a.jsxs)("button",{onClick:()=>r(t.id),className:`
                        w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-all duration-300
                        ${e===t.id?"bg-gradient-to-r from-blue-500 to-purple-600 text-white":"text-white/70 hover:text-white hover:bg-white/10"}
                      `,children:[(0,a.jsx)(s,{className:"w-5 h-5"}),t.label]},t.id)})})})}),(0,a.jsxs)(l.P.div,{className:"lg:col-span-3",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:[(0,a.jsxs)(f.A,{variant:"elevated",children:["profile"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Profile Information"}),(0,a.jsx)("p",{className:"text-white/70",children:"Update your personal information and profile details"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-12 h-12 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Profile Picture"}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)(x.A,{variant:"secondary",size:"sm",children:"Upload Photo"}),(0,a.jsx)(x.A,{variant:"secondary",size:"sm",children:"Remove"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(m.A,{label:"First Name",type:"text",value:C.firstName,onChange:e=>P({firstName:e}),placeholder:"Enter your first name",required:!0}),(0,a.jsx)(m.A,{label:"Last Name",type:"text",value:C.lastName,onChange:e=>P({lastName:e}),placeholder:"Enter your last name",required:!0})]}),(0,a.jsx)(m.A,{label:"Email Address",type:"email",value:C.email,onChange:e=>P({email:e}),placeholder:"Enter your email address",required:!0}),(0,a.jsx)(m.A,{label:"Bio",type:"textarea",value:C.bio,onChange:e=>P({bio:e}),placeholder:"Tell us about yourself...",rows:4})]}),"preferences"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"General Preferences"}),(0,a.jsx)("p",{className:"text-white/70",children:"Configure your account preferences and regional settings"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(m.A,{label:"Language",type:"select",value:C.defaultLanguage,onChange:e=>P({defaultLanguage:e}),options:[{value:"en",label:"English"},{value:"es",label:"Spanish"},{value:"fr",label:"French"},{value:"de",label:"German"},{value:"it",label:"Italian"},{value:"pt",label:"Portuguese"},{value:"ja",label:"Japanese"},{value:"ko",label:"Korean"}]}),(0,a.jsx)(m.A,{label:"Timezone",type:"select",value:C.timezone,onChange:e=>P({timezone:e}),options:[{value:"America/New_York",label:"Eastern Time (ET)"},{value:"America/Chicago",label:"Central Time (CT)"},{value:"America/Denver",label:"Mountain Time (MT)"},{value:"America/Los_Angeles",label:"Pacific Time (PT)"},{value:"Europe/London",label:"London (GMT)"},{value:"Europe/Paris",label:"Paris (CET)"},{value:"Asia/Tokyo",label:"Tokyo (JST)"},{value:"Asia/Shanghai",label:"Shanghai (CST)"},{value:"Australia/Sydney",label:"Sydney (AEDT)"},{value:"UTC",label:"UTC"}]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Auto-save Content"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Automatically save your work as you type"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.autoSaveEnabled,onChange:e=>P({autoSaveEnabled:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),"content"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Content Preferences"}),(0,a.jsx)("p",{className:"text-white/70",children:"Set your default preferences for content generation"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(m.A,{label:"Default Word Count",type:"number",value:C.defaultWordCount,onChange:e=>P({defaultWordCount:e}),placeholder:"1000"}),(0,a.jsx)(m.A,{label:"Default Tone",type:"select",value:C.defaultTone,onChange:e=>P({defaultTone:e}),options:[{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"authoritative",label:"Authoritative"},{value:"conversational",label:"Conversational"},{value:"technical",label:"Technical"},{value:"friendly",label:"Friendly"}]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Include Research by Default"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Automatically enable AI research for new content"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.includeResearchByDefault,onChange:e=>P({includeResearchByDefault:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),"notifications"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Notification Preferences"}),(0,a.jsx)("p",{className:"text-white/70",children:"Choose how and when you want to be notified"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Receive important updates via email"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.emailNotifications,onChange:e=>P({emailNotifications:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Receive browser notifications for real-time updates"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.pushNotifications,onChange:e=>P({pushNotifications:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Weekly Reports"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Get weekly summaries of your content activity"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.weeklyReports,onChange:e=>P({weeklyReports:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Marketing Emails"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Receive promotional content and feature updates"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.marketingEmails,onChange:e=>P({marketingEmails:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]}),"appearance"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Appearance & Display"}),(0,a.jsx)("p",{className:"text-white/70",children:"Customize how the interface looks and feels"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(m.A,{label:"Theme",type:"select",value:C.theme,onChange:e=>S(e),options:[{value:"dark",label:"Dark Theme"},{value:"light",label:"Light Theme"},{value:"auto",label:"Auto (System)"}]}),(0,a.jsx)(m.A,{label:"Accent Color",type:"select",value:C.accentColor,onChange:e=>D(e),options:[{value:"blue",label:"Professional Blue"},{value:"purple",label:"Executive Purple"},{value:"green",label:"Corporate Green"},{value:"red",label:"Business Red"},{value:"orange",label:"Enterprise Orange"}]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Enable Animations"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Show smooth transitions and visual effects"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.animationsEnabled,onChange:q,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Compact Mode"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Use a more compact interface layout"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.compactMode,onChange:z,className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]})]}),"privacy"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"border-b border-white/10 pb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Privacy & Data"}),(0,a.jsx)("p",{className:"text-white/70",children:"Control your privacy and data sharing preferences"})]}),(0,a.jsx)(m.A,{label:"Profile Visibility",type:"select",value:C.profileVisibility,onChange:e=>P({profileVisibility:e}),options:[{value:"private",label:"Private - Only visible to you"},{value:"team",label:"Team - Visible to team members"},{value:"public",label:"Public - Visible to everyone"}]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Data Sharing"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Allow anonymous usage data to improve the service"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.dataSharing,onChange:e=>P({dataSharing:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:"Analytics Tracking"}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Enable analytics to help us understand usage patterns"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:C.analyticsTracking,onChange:e=>P({analyticsTracking:e.target.checked}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-xl p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsx)("span",{className:"text-blue-400 font-medium",children:"Data Protection"})]}),(0,a.jsx)("p",{className:"text-blue-200 text-sm mb-3",children:"Your privacy is important to us. We follow industry best practices to protect your data."}),(0,a.jsxs)("div",{className:"space-y-2 text-white/70 text-sm",children:[(0,a.jsx)("div",{children:"• All data is encrypted in transit and at rest"}),(0,a.jsx)("div",{children:"• We never sell your personal information"}),(0,a.jsx)("div",{children:"• You can export or delete your data at any time"}),(0,a.jsx)("div",{children:"• Regular security audits and compliance checks"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-white/10 mt-8",children:[(0,a.jsx)(x.A,{variant:"secondary",onClick:()=>{confirm("Are you sure you want to reset all settings to default values?")&&(T(),k("Settings reset to defaults!"),setTimeout(()=>k(""),3e3))},icon:(0,a.jsx)(h.A,{className:"w-4 h-4"}),children:"Reset to Defaults"}),(0,a.jsx)(x.A,{variant:"primary",onClick:$,loading:t,icon:(0,a.jsx)(p.A,{className:"w-4 h-4"}),children:t?"Saving...":"Save Settings"})]})]}),(0,a.jsx)(b.A,{type:"success",message:w,show:!!w,onClose:()=>k(""),autoClose:!0}),(0,a.jsx)(b.A,{type:"error",message:_||"",show:!!_,onClose:()=>A(""),autoClose:!0})]})]})]})})}},78122:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},98971:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,135,951,15,141,556],()=>t(33954));module.exports=a})();