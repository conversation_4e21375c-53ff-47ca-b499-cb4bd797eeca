exports.id=24,exports.ids=[24],exports.modules={1188:(e,t,r)=>{"use strict";r.d(t,{D:()=>i,ThemeProvider:()=>l});var o=r(60687),n=r(43210),a=r(22362);let s=(0,n.createContext)(void 0),i=()=>{let e=(0,n.useContext)(s);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},l=({children:e})=>{let{settings:t,updateSettings:r}=(0,a.t)(),i="auto"===t.theme?"dark":t.theme;(0,n.useEffect)(()=>{},[i,t.accentColor,t.animationsEnabled,t.compactMode]),(0,n.useEffect)(()=>{t.theme},[t.theme]);let l={theme:i,accentColor:t.accentColor,animationsEnabled:t.animationsEnabled,compactMode:t.compactMode,setTheme:e=>{r({theme:e})},setAccentColor:e=>{r({accentColor:e})},toggleAnimations:()=>{r({animationsEnabled:!t.animationsEnabled})},toggleCompactMode:()=>{r({compactMode:!t.compactMode})}};return(0,o.jsx)(s.Provider,{value:l,children:e})}},7036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},13252:(e,t,r)=>{"use strict";r.d(t,{A:()=>N});var o=r(60687),n=r(43210),a=r(97905),s=r(45984),i=r(69169),l=r(21782),d=r(27777),c=r(11922),m=r(98916),h=r(25366),g=r(14290),b=r(84821),x=r(9005),u=r(80375),p=r(69282),v=r(75034),f=r(98971),w=r(11736),j=r(85629),y=r(67965),C=r(35800);function N({content:e,onChange:t,viewMode:r}){let N=(0,n.useRef)(null),[S,P]=(0,n.useState)(""),[k,A]=(0,n.useState)(0),[E,T]=(0,n.useState)(0),I=(r,o="",n="")=>{if(!N.current)return;let a=N.current,s=a.selectionStart,i=a.selectionEnd,l=e.substring(s,i)||n;t(e.substring(0,s)+r+l+o+e.substring(i)),setTimeout(()=>{if(N.current){let e=s+r.length+l.length;N.current.setSelectionRange(e,e),N.current.focus()}},0)},M=[{icon:s.A,label:"Heading 1",action:()=>I("# ","","Heading 1"),color:"text-red-600 hover:bg-red-50 hover:text-red-700",bgColor:"hover:shadow-red-200"},{icon:i.A,label:"Heading 2",action:()=>I("## ","","Heading 2"),color:"text-orange-600 hover:bg-orange-50 hover:text-orange-700",bgColor:"hover:shadow-orange-200"},{icon:l.A,label:"Heading 3",action:()=>I("### ","","Heading 3"),color:"text-amber-600 hover:bg-amber-50 hover:text-amber-700",bgColor:"hover:shadow-amber-200"},{icon:d.A,label:"Bold",action:()=>I("**","**","bold text"),color:"text-red-700 hover:bg-red-50 hover:text-red-800",bgColor:"hover:shadow-red-200"},{icon:c.A,label:"Italic",action:()=>I("*","*","italic text"),color:"text-orange-700 hover:bg-orange-50 hover:text-orange-800",bgColor:"hover:shadow-orange-200"},{icon:m.A,label:"Quote",action:()=>I("> ","","quote"),color:"text-amber-700 hover:bg-amber-50 hover:text-amber-800",bgColor:"hover:shadow-amber-200"},{icon:h.A,label:"Bullet List",action:()=>I("- ","","list item"),color:"text-red-600 hover:bg-red-50 hover:text-red-700",bgColor:"hover:shadow-red-200"},{icon:g.A,label:"Numbered List",action:()=>I("1. ","","list item"),color:"text-orange-600 hover:bg-orange-50 hover:text-orange-700",bgColor:"hover:shadow-orange-200"},{icon:b.A,label:"Link",action:()=>I("[","](url)","link text"),color:"text-amber-600 hover:bg-amber-50 hover:text-amber-700",bgColor:"hover:shadow-amber-200"},{icon:x.A,label:"Image",action:()=>I("![","](image-url)","alt text"),color:"text-red-700 hover:bg-red-50 hover:text-red-800",bgColor:"hover:shadow-red-200"},{icon:u.A,label:"Code",action:()=>I("`","`","code"),color:"text-orange-700 hover:bg-orange-50 hover:text-orange-800",bgColor:"hover:shadow-orange-200"}];return"preview"===r?(0,o.jsx)("div",{className:"p-8 min-h-[600px] bg-gradient-to-br from-amber-50/50 via-orange-50/50 to-red-50/30",children:(0,o.jsx)("div",{className:"prose prose-lg prose-orange max-w-none",children:(0,o.jsx)(w.oz,{remarkPlugins:[j.A],components:{code({node:e,inline:t,className:r,children:n,...a}){let s=/language-(\w+)/.exec(r||"");return!t&&s?(0,o.jsxs)("div",{className:"my-4 rounded-xl overflow-hidden border border-orange-200 shadow-lg",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-orange-100 to-red-100 px-4 py-2 text-sm font-medium text-orange-800 border-b border-orange-200",children:s[1]}),(0,o.jsx)(y.A,{style:C.A,language:s[1],PreTag:"div",className:"!bg-gradient-to-br !from-orange-50 !to-amber-50",...a,children:String(n).replace(/\n$/,"")})]}):(0,o.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 px-2 py-1 rounded-md text-sm font-medium border border-orange-200",...a,children:n})},h1:({children:e})=>(0,o.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-red-700 to-orange-600 bg-clip-text text-transparent mb-6 border-b-2 border-gradient-to-r from-red-200 to-orange-200 pb-3",children:e}),h2:({children:e})=>(0,o.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-orange-700 to-amber-600 bg-clip-text text-transparent mb-4 border-b border-orange-200 pb-2",children:e}),h3:({children:e})=>(0,o.jsx)("h3",{className:"text-2xl font-bold bg-gradient-to-r from-amber-700 to-yellow-600 bg-clip-text text-transparent mb-3",children:e}),p:({children:e})=>(0,o.jsx)("p",{className:"text-gray-800 leading-relaxed mb-6 text-lg",children:e}),blockquote:({children:e})=>(0,o.jsx)("blockquote",{className:"border-l-4 border-gradient-to-b from-orange-400 to-red-400 pl-6 py-4 bg-gradient-to-r from-orange-50 to-amber-50 text-orange-900 italic my-6 rounded-r-lg shadow-sm",children:e}),ul:({children:e})=>(0,o.jsx)("ul",{className:"list-disc list-inside text-gray-800 mb-6 space-y-2 text-lg",children:e}),ol:({children:e})=>(0,o.jsx)("ol",{className:"list-decimal list-inside text-gray-800 mb-6 space-y-2 text-lg",children:e}),li:({children:e})=>(0,o.jsx)("li",{className:"ml-2",children:e}),a:({children:e,href:t})=>(0,o.jsx)("a",{href:t,className:"text-orange-600 hover:text-red-600 underline decoration-orange-300 hover:decoration-red-400 transition-all duration-300 font-medium",target:"_blank",rel:"noopener noreferrer",children:e}),strong:({children:e})=>(0,o.jsx)("strong",{className:"font-bold bg-gradient-to-r from-red-700 to-orange-600 bg-clip-text text-transparent",children:e}),em:({children:e})=>(0,o.jsx)("em",{className:"italic text-orange-800 font-medium",children:e}),table:({children:e})=>(0,o.jsx)("div",{className:"overflow-x-auto my-6",children:(0,o.jsx)("table",{className:"min-w-full border border-orange-200 rounded-lg overflow-hidden shadow-sm",children:e})}),thead:({children:e})=>(0,o.jsx)("thead",{className:"bg-gradient-to-r from-orange-100 to-amber-100",children:e}),th:({children:e})=>(0,o.jsx)("th",{className:"px-4 py-3 text-left font-semibold text-orange-900 border-b border-orange-200",children:e}),td:({children:e})=>(0,o.jsx)("td",{className:"px-4 py-3 text-gray-800 border-b border-orange-100",children:e})},children:e})})}):(0,o.jsxs)("div",{className:"flex flex-col h-full bg-gradient-to-br from-amber-50/30 to-orange-50/30",children:[(0,o.jsxs)("div",{className:"flex flex-wrap items-center gap-2 p-6 bg-gradient-to-r from-amber-50 via-orange-50 to-red-50 border-b border-orange-200 shadow-sm",children:[(0,o.jsx)("div",{className:"flex flex-wrap items-center gap-1",children:M.map((e,t)=>(0,o.jsx)(a.P.button,{whileHover:{scale:1.05,y:-1},whileTap:{scale:.95},onClick:e.action,className:`p-3 rounded-xl transition-all duration-300 ${e.color} ${e.bgColor} shadow-sm hover:shadow-md border border-transparent hover:border-orange-200`,title:e.label,children:(0,o.jsx)(e.icon,{className:"h-4 w-4"})},t))}),(0,o.jsx)("div",{className:"w-px h-8 bg-gradient-to-b from-orange-300 to-red-300 mx-3"}),(0,o.jsxs)("div",{className:"flex items-center gap-3 text-sm font-medium",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2 text-orange-700 bg-white/60 px-3 py-2 rounded-lg border border-orange-200",children:[(0,o.jsx)(p.A,{className:"h-4 w-4"}),(0,o.jsxs)("span",{children:["Words: ",E.toLocaleString()]})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2 text-amber-700 bg-white/60 px-3 py-2 rounded-lg border border-amber-200",children:[(0,o.jsx)(v.A,{className:"h-4 w-4"}),(0,o.jsxs)("span",{children:["Characters: ",e.length.toLocaleString()]})]})]})]}),(0,o.jsxs)("div",{className:"flex-1 relative",children:[(0,o.jsx)("textarea",{ref:N,value:e,onChange:e=>t(e.target.value),className:"w-full h-full min-h-[500px] p-8 bg-gradient-to-br from-amber-50/20 via-orange-50/20 to-red-50/10 border-none outline-none resize-none text-gray-800 leading-relaxed text-base placeholder-orange-400",placeholder:"Start writing your article in Markdown... Use the toolbar above for quick formatting!",style:{fontFamily:'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',lineHeight:"1.8"}}),(0,o.jsxs)(a.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"absolute bottom-6 right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-orange-200 text-xs text-orange-700 max-w-sm",children:[(0,o.jsxs)("div",{className:"font-bold mb-3 text-orange-900 flex items-center gap-2",children:[(0,o.jsx)(f.A,{className:"h-4 w-4"}),"Markdown Quick Reference"]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"# H1"}),(0,o.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium",children:"## H2"}),(0,o.jsx)("code",{className:"bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium",children:"### H3"})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"**bold**"}),(0,o.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium",children:"*italic*"})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("code",{className:"bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium",children:"- list"}),(0,o.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"1. numbered"})]}),(0,o.jsx)("div",{children:(0,o.jsx)("code",{className:"bg-gradient-to-r from-orange-100 to-amber-100 px-2 py-1 rounded text-orange-700 font-medium",children:"[link](url)"})}),(0,o.jsx)("div",{children:(0,o.jsx)("code",{className:"bg-gradient-to-r from-amber-100 to-yellow-100 px-2 py-1 rounded text-amber-700 font-medium",children:"`inline code`"})}),(0,o.jsx)("div",{children:(0,o.jsx)("code",{className:"bg-gradient-to-r from-red-100 to-orange-100 px-2 py-1 rounded text-red-700 font-medium",children:"> blockquote"})})]})]})]})]})}},15773:(e,t,r)=>{Promise.resolve().then(r.bind(r,19864)),Promise.resolve().then(r.bind(r,68462))},16764:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19864:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","useSettings");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","SettingsProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","default")},22362:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>l,t:()=>i});var o=r(60687),n=r(43210);let a={firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0},s=(0,n.createContext)(void 0),i=()=>{let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},l=({children:e})=>{let[t,r]=(0,n.useState)(a),[i,l]=(0,n.useState)(!0),[d,c]=(0,n.useState)(null);(0,n.useEffect)(()=>{try{let e=localStorage.getItem("userSettings");if(e){let t=JSON.parse(e);r({...a,...t})}}catch(e){console.error("Failed to load settings:",e),c("Failed to load user settings")}finally{l(!1)}},[]),(0,n.useEffect)(()=>{if(!i)try{localStorage.setItem("userSettings",JSON.stringify(t))}catch(e){console.error("Failed to save settings:",e),c("Failed to save settings")}},[t,i]);let m=async()=>{try{c(null);let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),r=await e.json();if(!e.ok)throw Error(r.error||"Failed to save settings")}catch(t){let e=t instanceof Error?t.message:"Failed to save settings";throw c(e),Error(e)}};return(0,o.jsx)(s.Provider,{value:{settings:t,updateSettings:e=>{r(t=>({...t,...e})),c(null)},resetSettings:()=>{r(a),c(null)},saveSettings:m,isLoading:i,error:d},children:e})}},34333:(e,t,r)=>{Promise.resolve().then(r.bind(r,22362)),Promise.resolve().then(r.bind(r,1188))},61135:()=>{},68462:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","useTheme");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var o=r(37413),n=r(73911),a=r.n(n);r(61135);var s=r(19864),i=r(68462);let l={title:"Invincible - Content Writing SaaS",description:"The Ultimate Content Writing SaaS Platform with AI-powered tools"};function d({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{className:`${a().className} antialiased`,children:(0,o.jsx)(s.SettingsProvider,{children:(0,o.jsx)(i.ThemeProvider,{children:e})})})})}}};