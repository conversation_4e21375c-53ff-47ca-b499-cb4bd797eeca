"use strict";exports.id=830,exports.ids=[830],exports.modules={37449:(t,e,n)=>{var s,o,i,a,r,l,c,d,u,h,f,g;n.d(e,{ij:()=>W}),function(t){t.STRING="string",t.NUMBER="number",t.INTEGER="integer",t.BOOLEAN="boolean",t.ARRAY="array",t.OBJECT="object"}(s||(s={})),function(t){t.LANGUAGE_UNSPECIFIED="language_unspecified",t.PYTHON="python"}(o||(o={})),function(t){t.OUTCOME_UNSPECIFIED="outcome_unspecified",t.OUTCOME_OK="outcome_ok",t.OUTCOME_FAILED="outcome_failed",t.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"}(i||(i={}));let E=["user","model","function","system"];!function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",t.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"}(a||(a={})),function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"}(r||(r={})),function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"}(l||(l={})),function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER"}(c||(c={})),function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.OTHER="OTHER"}(d||(d={})),function(t){t.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",t.RETRIEVAL_QUERY="RETRIEVAL_QUERY",t.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",t.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",t.CLASSIFICATION="CLASSIFICATION",t.CLUSTERING="CLUSTERING"}(u||(u={})),function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"}(h||(h={})),function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"}(f||(f={}));class C extends Error{constructor(t){super(`[GoogleGenerativeAI Error]: ${t}`)}}class p extends C{constructor(t,e){super(t),this.response=e}}class m extends C{constructor(t,e,n,s){super(t),this.status=e,this.statusText=n,this.errorDetails=s}}class O extends C{}class _ extends C{}!function(t){t.GENERATE_CONTENT="generateContent",t.STREAM_GENERATE_CONTENT="streamGenerateContent",t.COUNT_TOKENS="countTokens",t.EMBED_CONTENT="embedContent",t.BATCH_EMBED_CONTENTS="batchEmbedContents"}(g||(g={}));class y{constructor(t,e,n,s,o){this.model=t,this.task=e,this.apiKey=n,this.stream=s,this.requestOptions=o}toString(){var t,e;let n=(null==(t=this.requestOptions)?void 0:t.apiVersion)||"v1beta",s=(null==(e=this.requestOptions)?void 0:e.baseUrl)||"https://generativelanguage.googleapis.com",o=`${s}/${n}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}}async function I(t){var e;let n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",function(t){let e=[];return(null==t?void 0:t.apiClient)&&e.push(t.apiClient),e.push("genai-js/0.24.1"),e.join(" ")}(t.requestOptions)),n.append("x-goog-api-key",t.apiKey);let s=null==(e=t.requestOptions)?void 0:e.customHeaders;if(s){if(!(s instanceof Headers))try{s=new Headers(s)}catch(t){throw new O(`unable to convert customHeaders value ${JSON.stringify(s)} to Headers: ${t.message}`)}for(let[t,e]of s.entries()){if("x-goog-api-key"===t)throw new O(`Cannot set reserved header name ${t}`);if("x-goog-api-client"===t)throw new O(`Header name ${t} can only be set using the apiClient field`);n.append(t,e)}}return n}async function T(t,e,n,s,o,i){let a=new y(t,e,n,s,i);return{url:a.toString(),fetchOptions:Object.assign(Object.assign({},function(t){let e={};if((null==t?void 0:t.signal)!==void 0||(null==t?void 0:t.timeout)>=0){let n=new AbortController;(null==t?void 0:t.timeout)>=0&&setTimeout(()=>n.abort(),t.timeout),(null==t?void 0:t.signal)&&t.signal.addEventListener("abort",()=>{n.abort()}),e.signal=n.signal}return e}(i)),{method:"POST",headers:await I(a),body:o})}}async function N(t,e,n,s,o,i={},a=fetch){let{url:r,fetchOptions:l}=await T(t,e,n,s,o,i);return R(r,l,a)}async function R(t,e,n=fetch){let s;try{s=await n(t,e)}catch(n){var o=n,i=t;let e=o;throw"AbortError"===e.name?(e=new _(`Request aborted when fetching ${i.toString()}: ${o.message}`)).stack=o.stack:o instanceof m||o instanceof O||((e=new C(`Error fetching from ${i.toString()}: ${o.message}`)).stack=o.stack),e}return s.ok||await A(s,t),s}async function A(t,e){let n,s="";try{let e=await t.json();s=e.error.message,e.error.details&&(s+=` ${JSON.stringify(e.error.details)}`,n=e.error.details)}catch(t){}throw new m(`Error fetching from ${e.toString()}: [${t.status} ${t.statusText}] ${s}`,t.status,t.statusText,n)}function S(t){return t.text=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),b(t.candidates[0]))throw new p(`${M(t)}`,t);return function(t){var e,n,s,o;let i=[];if(null==(n=null==(e=t.candidates)?void 0:e[0].content)?void 0:n.parts)for(let e of null==(o=null==(s=t.candidates)?void 0:s[0].content)?void 0:o.parts)e.text&&i.push(e.text),e.executableCode&&i.push("\n```"+e.executableCode.language+"\n"+e.executableCode.code+"\n```\n"),e.codeExecutionResult&&i.push("\n```\n"+e.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}(t)}if(t.promptFeedback)throw new p(`Text not available. ${M(t)}`,t);return""},t.functionCall=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),b(t.candidates[0]))throw new p(`${M(t)}`,t);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),v(t)[0]}if(t.promptFeedback)throw new p(`Function call not available. ${M(t)}`,t)},t.functionCalls=()=>{if(t.candidates&&t.candidates.length>0){if(t.candidates.length>1&&console.warn(`This response had ${t.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),b(t.candidates[0]))throw new p(`${M(t)}`,t);return v(t)}if(t.promptFeedback)throw new p(`Function call not available. ${M(t)}`,t)},t}function v(t){var e,n,s,o;let i=[];if(null==(n=null==(e=t.candidates)?void 0:e[0].content)?void 0:n.parts)for(let e of null==(o=null==(s=t.candidates)?void 0:s[0].content)?void 0:o.parts)e.functionCall&&i.push(e.functionCall);return i.length>0?i:void 0}let w=[d.RECITATION,d.SAFETY,d.LANGUAGE];function b(t){return!!t.finishReason&&w.includes(t.finishReason)}function M(t){var e,n,s;let o="";if((!t.candidates||0===t.candidates.length)&&t.promptFeedback)o+="Response was blocked",(null==(e=t.promptFeedback)?void 0:e.blockReason)&&(o+=` due to ${t.promptFeedback.blockReason}`),(null==(n=t.promptFeedback)?void 0:n.blockReasonMessage)&&(o+=`: ${t.promptFeedback.blockReasonMessage}`);else if(null==(s=t.candidates)?void 0:s[0]){let e=t.candidates[0];b(e)&&(o+=`Candidate was blocked due to ${e.finishReason}`,e.finishMessage&&(o+=`: ${e.finishMessage}`))}return o}function D(t){return this instanceof D?(this.v=t,this):new D(t)}"function"==typeof SuppressedError&&SuppressedError;let L=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;async function x(t){let e=[],n=t.getReader();for(;;){let{done:t,value:s}=await n.read();if(t)return S(function(t){let e=t[t.length-1],n={promptFeedback:null==e?void 0:e.promptFeedback};for(let e of t){if(e.candidates){let t=0;for(let s of e.candidates)if(n.candidates||(n.candidates=[]),n.candidates[t]||(n.candidates[t]={index:t}),n.candidates[t].citationMetadata=s.citationMetadata,n.candidates[t].groundingMetadata=s.groundingMetadata,n.candidates[t].finishReason=s.finishReason,n.candidates[t].finishMessage=s.finishMessage,n.candidates[t].safetyRatings=s.safetyRatings,s.content&&s.content.parts){n.candidates[t].content||(n.candidates[t].content={role:s.content.role||"user",parts:[]});let e={};for(let o of s.content.parts)o.text&&(e.text=o.text),o.functionCall&&(e.functionCall=o.functionCall),o.executableCode&&(e.executableCode=o.executableCode),o.codeExecutionResult&&(e.codeExecutionResult=o.codeExecutionResult),0===Object.keys(e).length&&(e.text=""),n.candidates[t].content.parts.push(e)}t++}e.usageMetadata&&(n.usageMetadata=e.usageMetadata)}return n}(e));e.push(s)}}async function H(t,e,n,s){let[o,i]=(function(t){let e=t.getReader();return new ReadableStream({start(t){let n="";return function s(){return e.read().then(({value:e,done:o})=>{let i;if(o)return n.trim()?void t.error(new C("Failed to parse stream")):void t.close();let a=(n+=e).match(L);for(;a;){try{i=JSON.parse(a[1])}catch(e){t.error(new C(`Error parsing JSON response: "${a[1]}"`));return}t.enqueue(i),a=(n=n.substring(a[0].length)).match(L)}return s()}).catch(t=>{let e=t;throw e.stack=t.stack,e="AbortError"===e.name?new _("Request aborted when reading from the stream"):new C("Error reading from the stream")})}()}})})((await N(e,g.STREAM_GENERATE_CONTENT,t,!0,JSON.stringify(n),s)).body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))).tee();return{stream:function(t){return function(t,e,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var s,o=n.apply(t,e||[]),i=[];return s={},a("next"),a("throw"),a("return"),s[Symbol.asyncIterator]=function(){return this},s;function a(t){o[t]&&(s[t]=function(e){return new Promise(function(n,s){i.push([t,e,n,s])>1||r(t,e)})})}function r(t,e){try{var n;(n=o[t](e)).value instanceof D?Promise.resolve(n.value.v).then(l,c):d(i[0][2],n)}catch(t){d(i[0][3],t)}}function l(t){r("next",t)}function c(t){r("throw",t)}function d(t,e){t(e),i.shift(),i.length&&r(i[0][0],i[0][1])}}(this,arguments,function*(){let e=t.getReader();for(;;){let{value:t,done:n}=yield D(e.read());if(n)break;yield yield D(S(t))}})}(o),response:x(i)}}async function P(t,e,n,s){let o=await N(e,g.GENERATE_CONTENT,t,!1,JSON.stringify(n),s);return{response:S(await o.json())}}function U(t){if(null!=t){if("string"==typeof t)return{role:"system",parts:[{text:t}]};if(t.text)return{role:"system",parts:[t]};if(t.parts)if(!t.role)return{role:"system",parts:t.parts};else return t}}function F(t){let e=[];if("string"==typeof t)e=[{text:t}];else for(let n of t)"string"==typeof n?e.push({text:n}):e.push(n);var n=e;let s={role:"user",parts:[]},o={role:"function",parts:[]},i=!1,a=!1;for(let t of n)"functionResponse"in t?(o.parts.push(t),a=!0):(s.parts.push(t),i=!0);if(i&&a)throw new C("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!i&&!a)throw new C("No content is provided for sending chat message.");return i?s:o}function G(t){let e;return e=t.contents?t:{contents:[F(t)]},t.systemInstruction&&(e.systemInstruction=U(t.systemInstruction)),e}let $=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],j={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function Y(t){var e;if(void 0===t.candidates||0===t.candidates.length)return!1;let n=null==(e=t.candidates[0])?void 0:e.content;if(void 0===n||void 0===n.parts||0===n.parts.length)return!1;for(let t of n.parts)if(void 0===t||0===Object.keys(t).length||void 0!==t.text&&""===t.text)return!1;return!0}let K="SILENT_ERROR";class B{constructor(t,e,n,s={}){this.model=e,this.params=n,this._requestOptions=s,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=t,(null==n?void 0:n.history)&&(!function(t){let e=!1;for(let n of t){let{role:t,parts:s}=n;if(!e&&"user"!==t)throw new C(`First content should be with role 'user', got ${t}`);if(!E.includes(t))throw new C(`Each item should include role field. Got ${t} but valid roles are: ${JSON.stringify(E)}`);if(!Array.isArray(s))throw new C("Content should have 'parts' property with an array of Parts");if(0===s.length)throw new C("Each Content should have at least one part");let o={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(let t of s)for(let e of $)e in t&&(o[e]+=1);let i=j[t];for(let e of $)if(!i.includes(e)&&o[e]>0)throw new C(`Content with role '${t}' can't contain '${e}' part`);e=!0}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(t,e={}){var n,s,o,i,a,r;let l;await this._sendPromise;let c=F(t),d={safetySettings:null==(n=this.params)?void 0:n.safetySettings,generationConfig:null==(s=this.params)?void 0:s.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(a=this.params)?void 0:a.systemInstruction,cachedContent:null==(r=this.params)?void 0:r.cachedContent,contents:[...this._history,c]},u=Object.assign(Object.assign({},this._requestOptions),e);return this._sendPromise=this._sendPromise.then(()=>P(this._apiKey,this.model,d,u)).then(t=>{var e;if(Y(t.response)){this._history.push(c);let n=Object.assign({parts:[],role:"model"},null==(e=t.response.candidates)?void 0:e[0].content);this._history.push(n)}else{let e=M(t.response);e&&console.warn(`sendMessage() was unsuccessful. ${e}. Inspect response object for details.`)}l=t}).catch(t=>{throw this._sendPromise=Promise.resolve(),t}),await this._sendPromise,l}async sendMessageStream(t,e={}){var n,s,o,i,a,r;await this._sendPromise;let l=F(t),c={safetySettings:null==(n=this.params)?void 0:n.safetySettings,generationConfig:null==(s=this.params)?void 0:s.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(a=this.params)?void 0:a.systemInstruction,cachedContent:null==(r=this.params)?void 0:r.cachedContent,contents:[...this._history,l]},d=Object.assign(Object.assign({},this._requestOptions),e),u=H(this._apiKey,this.model,c,d);return this._sendPromise=this._sendPromise.then(()=>u).catch(t=>{throw Error(K)}).then(t=>t.response).then(t=>{if(Y(t)){this._history.push(l);let e=Object.assign({},t.candidates[0].content);e.role||(e.role="model"),this._history.push(e)}else{let e=M(t);e&&console.warn(`sendMessageStream() was unsuccessful. ${e}. Inspect response object for details.`)}}).catch(t=>{t.message!==K&&console.error(t)}),u}}async function k(t,e,n,s){return(await N(e,g.COUNT_TOKENS,t,!1,JSON.stringify(n),s)).json()}async function q(t,e,n,s){return(await N(e,g.EMBED_CONTENT,t,!1,JSON.stringify(n),s)).json()}async function J(t,e,n,s){let o=n.requests.map(t=>Object.assign(Object.assign({},t),{model:e}));return(await N(e,g.BATCH_EMBED_CONTENTS,t,!1,JSON.stringify({requests:o}),s)).json()}class V{constructor(t,e,n={}){this.apiKey=t,this._requestOptions=n,e.model.includes("/")?this.model=e.model:this.model=`models/${e.model}`,this.generationConfig=e.generationConfig||{},this.safetySettings=e.safetySettings||[],this.tools=e.tools,this.toolConfig=e.toolConfig,this.systemInstruction=U(e.systemInstruction),this.cachedContent=e.cachedContent}async generateContent(t,e={}){var n;let s=G(t),o=Object.assign(Object.assign({},this._requestOptions),e);return P(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(n=this.cachedContent)?void 0:n.name},s),o)}async generateContentStream(t,e={}){var n;let s=G(t),o=Object.assign(Object.assign({},this._requestOptions),e);return H(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(n=this.cachedContent)?void 0:n.name},s),o)}startChat(t){var e;return new B(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(e=this.cachedContent)?void 0:e.name},t),this._requestOptions)}async countTokens(t,e={}){let n=function(t,e){var n;let s={model:null==e?void 0:e.model,generationConfig:null==e?void 0:e.generationConfig,safetySettings:null==e?void 0:e.safetySettings,tools:null==e?void 0:e.tools,toolConfig:null==e?void 0:e.toolConfig,systemInstruction:null==e?void 0:e.systemInstruction,cachedContent:null==(n=null==e?void 0:e.cachedContent)?void 0:n.name,contents:[]},o=null!=t.generateContentRequest;if(t.contents){if(o)throw new O("CountTokensRequest must have one of contents or generateContentRequest, not both.");s.contents=t.contents}else if(o)s=Object.assign(Object.assign({},s),t.generateContentRequest);else{let e=F(t);s.contents=[e]}return{generateContentRequest:s}}(t,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),s=Object.assign(Object.assign({},this._requestOptions),e);return k(this.apiKey,this.model,n,s)}async embedContent(t,e={}){let n="string"==typeof t||Array.isArray(t)?{content:F(t)}:t,s=Object.assign(Object.assign({},this._requestOptions),e);return q(this.apiKey,this.model,n,s)}async batchEmbedContents(t,e={}){let n=Object.assign(Object.assign({},this._requestOptions),e);return J(this.apiKey,this.model,t,n)}}class W{constructor(t){this.apiKey=t}getGenerativeModel(t,e){if(!t.model)throw new C("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new V(this.apiKey,t,e)}getGenerativeModelFromCachedContent(t,e,n){if(!t.name)throw new O("Cached content must contain a `name` field.");if(!t.model)throw new O("Cached content must contain a `model` field.");for(let n of["model","systemInstruction"])if((null==e?void 0:e[n])&&t[n]&&(null==e?void 0:e[n])!==t[n]){if("model"===n&&(e.model.startsWith("models/")?e.model.replace("models/",""):e.model)===(t.model.startsWith("models/")?t.model.replace("models/",""):t.model))continue;throw new O(`Different value for "${n}" specified in modelParams (${e[n]}) and cachedContent (${t[n]})`)}let s=Object.assign(Object.assign({},e),{model:t.model,tools:t.tools,toolConfig:t.toolConfig,systemInstruction:t.systemInstruction,cachedContent:t});return new V(this.apiKey,s,n)}}}};