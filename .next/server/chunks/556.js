exports.id=556,exports.ids=[556],exports.modules={1188:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,ThemeProvider:()=>o});var r=s(60687),i=s(43210),a=s(22362);let n=(0,i.createContext)(void 0),l=()=>{let e=(0,i.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},o=({children:e})=>{let{settings:t,updateSettings:s}=(0,a.t)(),l="auto"===t.theme?"dark":t.theme;(0,i.useEffect)(()=>{},[l,t.accentColor,t.animationsEnabled,t.compactMode]),(0,i.useEffect)(()=>{t.theme},[t.theme]);let o={theme:l,accentColor:t.accentColor,animationsEnabled:t.animationsEnabled,compactMode:t.compactMode,setTheme:e=>{s({theme:e})},setAccentColor:e=>{s({accentColor:e})},toggleAnimations:()=>{s({animationsEnabled:!t.animationsEnabled})},toggleCompactMode:()=>{s({compactMode:!t.compactMode})}};return(0,r.jsx)(n.Provider,{value:o,children:e})}},7036:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},15773:(e,t,s)=>{Promise.resolve().then(s.bind(s,19864)),Promise.resolve().then(s.bind(s,68462))},16337:(e,t,s)=>{"use strict";s.d(t,{A:()=>F});var r=s(60687),i=s(43210),a=s(97905),n=s(85814),l=s.n(n),o=s(16189),d=s(22362),c=s(49625),h=s(45583),m=s(56085),x=s(74606),u=s(41550),b=s(72575),p=s(2943),f=s(53411),g=s(10022),v=s(84027),w=s(65668),j=s(14952),N=s(47033),y=s(58869),C=s(40083);let P=[{title:"Dashboard",href:"/dashboard",icon:c.A,badge:null},{title:"Aayush Agent",href:"/hierarchical-agents",icon:h.A,badge:"New"},{title:"AI Superagent",href:"/superagent",icon:m.A,badge:"Legacy"},{title:"Content Tools",items:[{title:"Blog Generator",href:"/blog",icon:x.A,badge:"Popular"},{title:"Email Generator",href:"/email",icon:u.A,badge:null},{title:"Tweet Generator",href:"/tweet",icon:b.A,badge:"New"},{title:"YouTube Scripts",href:"/youtube",icon:p.A,badge:null}]},{title:"Analytics",href:"/analytics",icon:f.A,badge:null},{title:"Content Library",href:"/library",icon:g.A,badge:null}],A=[{title:"Settings",href:"/settings",icon:v.A},{title:"Help & Support",href:"/help",icon:w.A}];function S({isCollapsed:e,onToggle:t}){let s=(0,o.usePathname)(),[n,c]=(0,i.useState)(null),{settings:h}=(0,d.t)(),x=e=>"/dashboard"===e?"/dashboard"===s:s.startsWith(e),u=({item:t,isNested:s=!1})=>{let i=x(t.href),a=t.icon,o=n===t.href;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(l(),{href:t.href,className:`
            relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 cursor-pointer
            ${i?"bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-white border border-blue-500/30":"text-white/70 hover:text-white hover:bg-white/5"}
            ${s?"ml-6":""}
            ${e?"justify-center":""}
          `,onMouseEnter:()=>c(t.href),onMouseLeave:()=>c(null),children:[i&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-xl"}),(0,r.jsxs)("div",{className:"relative z-10 flex items-center gap-3 w-full",children:[(0,r.jsx)(a,{className:`w-5 h-5 flex-shrink-0 ${i?"text-blue-400":""}`}),!e&&(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("span",{className:"font-medium",children:t.title}),t.badge&&(0,r.jsx)("span",{className:`
                    px-2 py-0.5 text-xs font-semibold rounded-full
                    ${"New"===t.badge?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-orange-500/20 text-orange-400 border border-orange-500/30"}
                  `,children:t.badge})]})]})]}),e&&o&&(0,r.jsxs)("div",{className:"absolute left-full ml-4 px-3 py-2 bg-gray-800/95 backdrop-blur-sm text-white text-sm rounded-lg shadow-xl border border-white/20 whitespace-nowrap z-[60]",style:{top:"50%",transform:"translateY(-50%)",pointerEvents:"none"},children:[t.title,t.badge&&(0,r.jsx)("span",{className:"ml-2 px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded",children:t.badge})]})]})};return(0,r.jsxs)(a.P.aside,{animate:{width:e?80:280},transition:{duration:.3,ease:"easeInOut"},className:"fixed left-0 top-0 h-full bg-gray-900/95 backdrop-blur-xl border-r border-white/10 z-40 flex flex-col",onMouseLeave:()=>c(null),children:[(0,r.jsx)("div",{className:"p-4 border-b border-white/10",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[!e&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-8 h-8 text-blue-400"}),(0,r.jsx)("span",{className:"text-xl font-bold text-white",children:"Invincible"})]}),(0,r.jsx)("button",{onClick:t,className:"p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 cursor-pointer",children:e?(0,r.jsx)(j.A,{className:"w-5 h-5 text-white/70"}):(0,r.jsx)(N.A,{className:"w-5 h-5 text-white/70"})})]})}),(0,r.jsx)("div",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:P.map((t,s)=>(0,r.jsx)("div",{className:"space-y-1",children:t.items?(0,r.jsxs)("div",{className:"space-y-1",children:[!e&&(0,r.jsx)("div",{className:"px-3 py-2 text-xs font-semibold text-white/50 uppercase tracking-wider",children:t.title}),t.items.map(t=>(0,r.jsx)(u,{item:t,isNested:!e},t.href))]}):(0,r.jsx)(u,{item:t})},s))}),(0,r.jsxs)("div",{className:"p-4 border-t border-white/10 space-y-2",children:[A.map(e=>(0,r.jsx)(u,{item:e},e.href)),(0,r.jsxs)("div",{className:`
          flex items-center gap-3 p-3 rounded-xl bg-white/5 border border-white/10
          ${e?"justify-center":""}
        `,children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-4 h-4 text-white"})}),!e&&(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-white",children:[h.firstName," ",h.lastName]}),(0,r.jsx)("div",{className:"text-xs text-white/60",children:"Pro Plan"})]}),(0,r.jsx)("button",{className:"p-1 rounded-lg hover:bg-white/10 transition-colors",children:(0,r.jsx)(C.A,{className:"w-4 h-4 text-white/70"})})]})]})]})]})}var k=s(88920),T=s(99270),E=s(96474),I=s(92363),$=s(97051),D=s(78272),U=s(85778),M=s(81822);function z(){let[e,t]=(0,i.useState)(!1),[s,n]=(0,i.useState)(!1),[l,o]=(0,i.useState)(!1),d=[{id:1,title:"Blog post generated",message:'Your "AI in Healthcare" blog post is ready',time:"2 min ago",unread:!0},{id:2,title:"Usage limit warning",message:"You've used 80% of your monthly credits",time:"1 hour ago",unread:!0},{id:3,title:"New feature available",message:"Try our new YouTube script generator",time:"1 day ago",unread:!1}];return(0,r.jsx)("header",{className:"sticky top-0 z-30 bg-white/5 backdrop-blur-xl border-b border-white/10",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsx)("div",{className:"flex-1 max-w-xl",children:(0,r.jsxs)(a.P.div,{className:`
              relative flex items-center transition-all duration-300
              ${e?"scale-105":""}
            `,children:[(0,r.jsx)(T.A,{className:"absolute left-3 w-5 h-5 text-white/50"}),(0,r.jsx)("input",{type:"text",placeholder:"Search content, templates, or ask AI...",className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300",onFocus:()=>t(!0),onBlur:()=>t(!1)}),e&&(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4",children:[(0,r.jsx)("div",{className:"text-sm text-white/60 mb-2",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer",children:[(0,r.jsx)(E.A,{className:"w-4 h-4 text-blue-400"}),(0,r.jsx)("span",{className:"text-white",children:"Create new blog post"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 text-purple-400"}),(0,r.jsx)("span",{className:"text-white",children:"Generate email campaign"})]})]})]})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(M.A,{variant:"primary",size:"sm",icon:(0,r.jsx)(E.A,{className:"w-4 h-4"}),children:"Create"}),(0,r.jsx)(M.A,{variant:"accent",size:"sm",icon:(0,r.jsx)(I.A,{className:"w-4 h-4"}),className:"bg-gradient-to-r from-yellow-500 to-orange-500",children:"Upgrade"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(a.P.button,{onClick:()=>o(!l),className:"relative p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,r.jsx)($.A,{className:"w-5 h-5 text-white"}),d.some(e=>e.unread)&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-gray-900"})]}),(0,r.jsx)(k.N,{children:l&&(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},className:"absolute top-full right-0 mt-2 w-80 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4 z-50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Notifications"}),(0,r.jsx)("button",{className:"text-sm text-blue-400 hover:text-blue-300",children:"Mark all read"})]}),(0,r.jsx)("div",{className:"space-y-3 max-h-80 overflow-y-auto",children:d.map(e=>(0,r.jsx)("div",{className:`
                          p-3 rounded-lg border transition-colors cursor-pointer
                          ${e.unread?"bg-blue-500/10 border-blue-500/20 hover:bg-blue-500/15":"bg-white/5 border-white/10 hover:bg-white/10"}
                        `,children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-white",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-white/70 mt-1",children:e.message}),(0,r.jsx)("span",{className:"text-xs text-white/50 mt-2 block",children:e.time})]}),e.unread&&(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-1"})]})},e.id))})]})})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(a.P.button,{onClick:()=>n(!s),className:"flex items-center gap-2 p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)(D.A,{className:"w-4 h-4 text-white/70"})]}),(0,r.jsx)(k.N,{children:s&&(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},className:"absolute top-full right-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4 z-50",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4 pb-4 border-b border-white/10",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-white",children:"John Doe"}),(0,r.jsx)("div",{className:"text-xs text-white/60",children:"<EMAIL>"}),(0,r.jsx)("div",{className:"text-xs text-blue-400 font-medium",children:"Pro Plan"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 text-white/70"}),(0,r.jsx)("span",{className:"text-sm text-white",children:"Profile Settings"})]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left",children:[(0,r.jsx)(U.A,{className:"w-4 h-4 text-white/70"}),(0,r.jsx)("span",{className:"text-sm text-white",children:"Billing & Usage"})]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 text-white/70"}),(0,r.jsx)("span",{className:"text-sm text-white",children:"Preferences"})]}),(0,r.jsx)("hr",{className:"border-white/10 my-2"}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-red-500/20 transition-colors text-left",children:[(0,r.jsx)(C.A,{className:"w-4 h-4 text-red-400"}),(0,r.jsx)("span",{className:"text-sm text-red-400",children:"Sign Out"})]})]})]})})]})]})]})})}function F({children:e}){let[t,s]=(0,i.useState)(!1),[n,l]=(0,i.useState)(!1);return n?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900",children:[(0,r.jsx)(S,{isCollapsed:t,onToggle:()=>{s(!t)}}),(0,r.jsxs)(a.P.div,{animate:{marginLeft:t?80:280},transition:{duration:.3,ease:"easeInOut"},className:"min-h-screen",children:[(0,r.jsx)(z,{}),(0,r.jsx)("main",{className:"p-6",children:(0,r.jsx)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e})})]}),(0,r.jsxs)("div",{className:"fixed inset-0 pointer-events-none overflow-hidden -z-10",children:[(0,r.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"}),(0,r.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-float",style:{animationDelay:"2s"}}),(0,r.jsx)("div",{className:"absolute top-3/4 left-1/3 w-64 h-64 bg-cyan-500/5 rounded-full blur-3xl animate-float",style:{animationDelay:"4s"}})]})]}):null}},16764:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19864:(e,t,s)=>{"use strict";s.d(t,{SettingsProvider:()=>i});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","useSettings");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","SettingsProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx","default")},22362:(e,t,s)=>{"use strict";s.d(t,{SettingsProvider:()=>o,t:()=>l});var r=s(60687),i=s(43210);let a={firstName:"John",lastName:"Doe",email:"<EMAIL>",bio:"Content creator and digital marketer passionate about AI-powered writing.",avatar:"",defaultLanguage:"en",timezone:"America/New_York",defaultWordCount:1e3,defaultTone:"professional",includeResearchByDefault:!0,autoSaveEnabled:!0,emailNotifications:!0,pushNotifications:!1,weeklyReports:!0,marketingEmails:!1,theme:"dark",accentColor:"blue",animationsEnabled:!0,compactMode:!1,profileVisibility:"private",dataSharing:!1,analyticsTracking:!0},n=(0,i.createContext)(void 0),l=()=>{let e=(0,i.useContext)(n);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e},o=({children:e})=>{let[t,s]=(0,i.useState)(a),[l,o]=(0,i.useState)(!0),[d,c]=(0,i.useState)(null);(0,i.useEffect)(()=>{try{let e=localStorage.getItem("userSettings");if(e){let t=JSON.parse(e);s({...a,...t})}}catch(e){console.error("Failed to load settings:",e),c("Failed to load user settings")}finally{o(!1)}},[]),(0,i.useEffect)(()=>{if(!l)try{localStorage.setItem("userSettings",JSON.stringify(t))}catch(e){console.error("Failed to save settings:",e),c("Failed to save settings")}},[t,l]);let h=async()=>{try{c(null);let e=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await e.json();if(!e.ok)throw Error(s.error||"Failed to save settings")}catch(t){let e=t instanceof Error?t.message:"Failed to save settings";throw c(e),Error(e)}};return(0,r.jsx)(n.Provider,{value:{settings:t,updateSettings:e=>{s(t=>({...t,...e})),c(null)},resetSettings:()=>{s(a),c(null)},saveSettings:h,isLoading:l,error:d},children:e})}},34333:(e,t,s)=>{Promise.resolve().then(s.bind(s,22362)),Promise.resolve().then(s.bind(s,1188))},61135:()=>{},68462:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>i});var r=s(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","useTheme");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","ThemeProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx","default")},79216:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(60687),i=s(97905);function a({children:e,variant:t="default",hover:s=!0,className:a="",onClick:n}){let l=`
    ${({default:"card-modern",elevated:"card-modern-elevated",glass:"glass",neu:"card-neu"})[t]}
    ${n?"cursor-pointer":""}
    ${a}
  `.trim();return(0,r.jsx)(i.P.div,{className:l,onClick:n,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},...s?{whileHover:{y:-4,scale:1.02},transition:{duration:.3}}:{},children:e})}},81822:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(60687),i=s(97905);function a({children:e,variant:t="primary",size:s="md",disabled:a=!1,loading:n=!1,onClick:l,type:o="button",className:d="",icon:c}){let h=`
    btn-modern
    ${({primary:"btn-modern-primary",secondary:"btn-modern-secondary",accent:"btn-modern-accent"})[t]}
    ${({sm:"text-sm",md:"text-base",lg:"text-lg"})[s]}
    ${a||n?"opacity-50 cursor-not-allowed":""}
    ${d}
  `.trim();return(0,r.jsx)(i.P.button,{type:o,className:h,onClick:a||n?void 0:l,disabled:a||n,whileHover:a||n?{}:{scale:1.02},whileTap:a||n?{}:{scale:.98},transition:{duration:.2},children:n?(0,r.jsx)("div",{className:"loading-spinner"}):(0,r.jsxs)(r.Fragment,{children:[c&&(0,r.jsx)("span",{className:"flex-shrink-0",children:c}),e]})})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>o});var r=s(37413),i=s(73911),a=s.n(i);s(61135);var n=s(19864),l=s(68462);let o={title:"Invincible - Content Writing SaaS",description:"The Ultimate Content Writing SaaS Platform with AI-powered tools"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().className} antialiased`,children:(0,r.jsx)(n.SettingsProvider,{children:(0,r.jsx)(l.ThemeProvider,{children:e})})})})}}};