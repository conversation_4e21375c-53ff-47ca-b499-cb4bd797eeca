{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/analytics", "regex": "^/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/analytics(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/blog/editor", "regex": "^/blog/editor(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/editor(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/editor", "regex": "^/editor(?:/)?$", "routeKeys": {}, "namedRegex": "^/editor(?:/)?$"}, {"page": "/email", "regex": "^/email(?:/)?$", "routeKeys": {}, "namedRegex": "^/email(?:/)?$"}, {"page": "/hierarchical-agents", "regex": "^/hierarchical\\-agents(?:/)?$", "routeKeys": {}, "namedRegex": "^/hierarchical\\-agents(?:/)?$"}, {"page": "/library", "regex": "^/library(?:/)?$", "routeKeys": {}, "namedRegex": "^/library(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/superagent", "regex": "^/superagent(?:/)?$", "routeKeys": {}, "namedRegex": "^/superagent(?:/)?$"}, {"page": "/tweet", "regex": "^/tweet(?:/)?$", "routeKeys": {}, "namedRegex": "^/tweet(?:/)?$"}, {"page": "/youtube", "regex": "^/youtube(?:/)?$", "routeKeys": {}, "namedRegex": "^/youtube(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}