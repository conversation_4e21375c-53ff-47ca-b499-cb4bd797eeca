# Hierarchical Agent System

A sophisticated multi-agent system with supervisor coordination, shared memory, and structured data processing for content research and generation.

## 🏗️ Architecture Overview

The system consists of a **Supervisor Agent** that orchestrates 5 specialized agents:

```
┌─────────────────┐
│ Supervisor Agent │ ← Coordinates all agents, manages shared memory
└─────────────────┘
         │
    ┌────┴────┐
    │ Shared  │ ← Centralized memory store with structured data
    │ Memory  │
    └────┬────┘
         │
    ┌────┴─────────────────────────────────────────┐
    │                                              │
┌───▼───┐ ┌──────▼──────┐ ┌────▼────┐ ┌────▼────┐ ┌──────▼──────┐
│Topic  │ │Primary      │ │Gap      │ │Deep     │ │Content      │
│Analyst│ │Research     │ │Analyst  │ │Research │ │Writing      │
└───────┘ └─────────────┘ └─────────┘ └─────────┘ └─────────────┘
```

## 🤖 Agent Specifications

### 1. Topic Analyst Agent
- **Model**: <PERSON><PERSON> (OpenRouter) for analysis
- **Search**: Google Search for top 5 pages
- **Function**: 
  - Extracts single keyword from topic
  - Searches and analyzes top 5 pages
  - Generates research queries for primary research

### 2. Primary Research Agent
- **Search**: Google Search
- **Function**:
  - Uses queries from Topic Analyst
  - Searches each query and extracts top 5 pages
  - Stores all extracted content in memory

### 3. Gap Analyst Agent
- **Model**: <PERSON><PERSON> (OpenRouter) for analysis
- **Function**:
  - Analyzes primary research content
  - Identifies gaps and missing information
  - Generates targeted queries for deep research

### 4. Deep Research Agent
- **Search**: Google Search
- **Function**:
  - Executes targeted queries from Gap Analyst
  - Fills identified content gaps
  - Stores additional research in memory

### 5. Content Writing Agent
- **Model**: Gemini for content generation
- **Search**: Google Search for style analysis
- **Function**:
  - Extracts keyword and searches top 5 pages for style analysis
  - Uses RAG approach with all memory data as knowledge base
  - Generates comprehensive content mimicking human writing style

## 🧠 Shared Memory System

All agents share a centralized memory store with structured data using delimiters:

```typescript
interface SharedMemory {
  topic: string
  topicAnalysis: string      // Topic Analyst results
  primaryResearch: string    // Primary Research results
  gapAnalysis: string        // Gap Analyst results
  deepResearch: string       // Deep Research results
  contentGeneration: string  // Content Writing results
  metadata: {
    startTime: Date
    currentPhase: string
    completedPhases: string[]
    errors: string[]
    agentLogs: Array<{
      agent: string
      timestamp: Date
      action: string
      result: string
    }>
  }
}
```

## 📊 Data Format

The system uses **structured data with delimiters** instead of JSON:

```
===TOPIC_ANALYSIS_START===
===KEYWORD===
artificial intelligence
===SEARCH_RESULTS_COUNT===
5
===ANALYSIS===
===MAIN_TOPIC===
AI applications in healthcare
===SUBTOPICS===
Medical diagnosis
Treatment planning
Drug discovery
===TOPIC_ANALYSIS_END===
```

## 🚀 Usage

### API Endpoint

```bash
POST /api/hierarchical-agents
Content-Type: application/json

{
  "topic": "artificial intelligence in healthcare",
  "options": {
    "maxRetries": 3,
    "timeoutMs": 300000
  }
}
```

### Programmatic Usage

```typescript
import { SupervisorAgent } from '@/lib/agents/hierarchical-agent-system'

const supervisor = new SupervisorAgent(
  'your topic here',
  (progress) => console.log(progress), // Progress callback
  {
    maxRetries: 3,
    timeoutMs: 300000,
    enableDetailedLogging: true
  }
)

const result = await supervisor.executeWorkflow()
```

### Web Interface

Visit `/hierarchical-agents` for a web interface to test the system.

## 🔧 Configuration

### Environment Variables

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id

# Optional
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Agent Options

```typescript
interface SupervisorOptions {
  maxRetries?: number      // Default: 3
  timeoutMs?: number       // Default: 300000 (5 minutes)
  enableDetailedLogging?: boolean // Default: true
}
```

## 🔄 Workflow Execution

1. **Initialization**: Supervisor creates shared memory and initializes all agents
2. **Topic Analysis**: Extract keyword → Search → Analyze with Qwen
3. **Primary Research**: Execute multiple queries → Extract content → Store in memory
4. **Gap Analysis**: Analyze research → Identify gaps → Generate deep research queries
5. **Deep Research**: Execute targeted queries → Fill gaps → Store additional data
6. **Content Writing**: Style analysis → RAG-based generation → Final content

## 🛡️ Error Handling

- **Retry Logic**: Exponential backoff for failed operations
- **Timeout Protection**: Configurable timeouts for each agent
- **Error Logging**: Comprehensive error tracking in shared memory
- **Graceful Degradation**: System continues even if some agents fail

## 📈 Progress Tracking

Real-time progress updates with detailed logging:

```typescript
interface AgentProgress {
  agent: string      // Agent name
  phase: string      // Current phase
  step: string       // Current step
  progress: number   // 0-100 percentage
  message: string    // Human-readable message
  timestamp: Date    // When this progress occurred
}
```

## 🧪 Testing

Run the test script:

```bash
node test-hierarchical-agents.js
```

Or test via the API:

```bash
curl -X POST http://localhost:3000/api/hierarchical-agents \
  -H "Content-Type: application/json" \
  -d '{"topic": "machine learning in finance"}'
```

## 🎯 Key Features

- ✅ **Supervisor Coordination**: Central orchestration of all agents
- ✅ **Shared Memory**: All agents access the same knowledge base
- ✅ **Structured Data**: Delimiter-based data format (no JSON)
- ✅ **Google Search Integration**: Web search capabilities for all research agents
- ✅ **Dual Model Support**: Qwen for analysis, Gemini for content generation
- ✅ **RAG Approach**: Content writing uses all research as knowledge base
- ✅ **Error Recovery**: Robust retry and error handling mechanisms
- ✅ **Progress Tracking**: Real-time updates on workflow execution
- ✅ **Style Mimicking**: Analyzes top pages to write in human style

## 🔍 Debugging

Access memory and logs:

```typescript
// Get memory summary
const summary = supervisor.getMemorySummary()

// Get specific memory section
const topicAnalysis = supervisor.getMemorySection('topicAnalysis')
const primaryResearch = supervisor.getMemorySection('primaryResearch')
```

## 📝 Output

The system generates comprehensive, well-researched content that:
- Uses all research data as supporting evidence
- Mimics human writing style from analyzed pages
- Includes proper markdown formatting
- Provides authoritative, go-to resource quality
- Incorporates insights from multiple research phases
